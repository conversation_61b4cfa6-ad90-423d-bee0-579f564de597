{"name": "@analytics/storage-utils", "version": "0.4.2", "description": "Storage utility with fallbacks", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-storage#readme", "repository": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-storage", "keywords": ["analytics", "analytics-project", "analytics-util", "storage", "cookies", "localStorage", "sessionStorage", "persistance"], "netlifySiteId": "961970d3-4d7b-4cc0-a54b-f304b73f0935", "amdName": "utilStorage", "source": "src/index.js", "main": "dist/analytics-util-storage.js", "module": "dist/analytics-util-storage.module.js", "unpkg": "dist/analytics-util-storage.umd.js", "sideEffects": false, "scripts": {"start": "npm run sync && concurrently 'npm:watch' 'npm:serve'", "test": "uvu -r esm tests", "serve": "servor dist index.html 8081 --reload --browse", "watch:test": "watchlist src tests -- npm run test", "watch:copy": "watchlist example -- npm run sync", "watch:build": "npm run build:browser -- --watch --no-compress", "sync": "cp example/index.html dist", "watch": "concurrently 'npm:watch:*'", "build": "concurrently 'npm:build:*' && npm run sync", "build:package": "microbundle", "build:browser": "microbundle build --external none -f iife,umd -o dist/browser", "build:no-deps": "microbundle build -f iife,umd -o dist/browser-no-deps", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish", "deploy": "npm run build && netlify deploy --prod --dir dist --site $npm_package_netlifySiteId"}, "files": ["lib", "dist", "package.json", "package-lock.json", "README.md"], "devDependencies": {"concurrently": "^6.1.0", "esm": "^3.2.25", "microbundle": "^0.13.0", "servor": "^4.0.2", "uvu": "^0.5.1", "watchlist": "^0.2.3"}, "dependencies": {"@analytics/cookie-utils": "^0.2.12", "@analytics/global-storage-utils": "^0.1.7", "@analytics/localstorage-utils": "^0.1.10", "@analytics/session-storage-utils": "^0.0.7", "@analytics/type-utils": "^0.6.2"}, "gitHead": "904e8bc88a9de0d9e55ecbe1e87ac2ee2a454e32"}