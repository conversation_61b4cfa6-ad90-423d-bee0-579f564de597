{"version": 3, "file": "analytics-util-types.module.js", "sources": ["../src/index.js"], "sourcesContent": ["\n/*\n  Constants for reuse\n*/\n\nexport const FUNCTION = 'function'\nexport const STRING = 'string'\nexport const UNDEFINED = 'undefined'\nexport const BOOLEAN = 'boolean'\nexport const OBJECT = 'object'\nexport const ARRAY = 'array'\nexport const NUMBER = 'number'\nexport const SYMBOL = 'symbol'\nexport const NULL = 'null'\nexport const ERROR = 'error'\nexport const TYPE_ERROR = 'typeError'\nexport const SYNTAX_ERROR = 'syntaxError'\nexport const ASYNC_FUNCTION = 'asyncFunction'\nexport const GENERATOR_FUNCTION = 'generatorFunction'\nexport const ASYNC_GENERATOR_FUNCTION = 'asyncGeneratorFunction'\n\nexport const noOp = () => {}\nexport const ANY = 'any'\nexport const ALL = '*'\nexport const NONE = 'none'\nexport const HIDDEN = 'hidden'\nexport const PREFIX = '__'\n\n/* DOM Constants */\nexport const FORM = 'form'\nexport const INPUT = 'input'\nexport const BUTTON = 'button'\nexport const SELECT = 'select'\nexport const CHANGE = 'change'\nexport const SUBMIT = 'submit'\n\n/* Regex patterns */\nexport const REGEX_ISO = /^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/\nexport const REGEX_EMAIL = /^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/\nexport const REGEX_JSON = /^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/\n\n/* ────────────────────\nEnvironment checks\n─────────────────────── */\n// alt implementations \n// - https://github.com/MikeKovarik/platform-detect\n/** @type {Object} */\nconst PROCESS = typeof process !== UNDEFINED ? process : {}\n\n/** @type {String} */\nexport const ENV = (PROCESS.env && PROCESS.env.NODE_ENV) || ''\n\n/** @type {Boolean} */\nexport const isProd = ENV === 'production'\n\n/** @type {Boolean} */\nexport const isStaging = ENV === 'staging'\n\n/** @type {Boolean} */\nexport const isDev = ENV === 'development'\n\n/** @type {Boolean} */\nexport const isBrowser = typeof document !== UNDEFINED\n/** @type {Boolean} */\nexport const isLocalHost = isBrowser && window.location.hostname === 'localhost'\n\n/** @type {Boolean} */\nexport const isNode = PROCESS.versions != null && PROCESS.versions.node != null\n\n/** @type {Boolean} */\nexport const isDeno = typeof Deno !== UNDEFINED && typeof Deno.core !== UNDEFINED;\n\n/** @type {Boolean} */\nexport const isWebWorker = typeof self === OBJECT && self.constructor && self.constructor.name === 'DedicatedWorkerGlobalScope'\n\n/** @type {Boolean} */\nexport const isJsDom = (isBrowser && window.name === 'nodejs') || (typeof navigator !== UNDEFINED && typeof navigator.userAgent !== UNDEFINED && (navigator.userAgent.includes('Node.js') || navigator.userAgent.includes('jsdom')))\n\n/* ────────────────────\nType checks\n─────────────────────── */\n\nfunction text(method, s) {\n  return s.charAt(0)[method]() + s.slice(1)\n}\n\nconst upper = text.bind(null, 'toUpperCase')\nconst lower = text.bind(null, 'toLowerCase')\n\n/**\n * Returns the object type of the given payload\n * @param {*} val\n * @returns {string}\n */\nexport function getTypeName(val) {\n  if (isNull(val)) return upper(NULL)\n  return (typeof val === OBJECT) ? ctorName(val) : Object.prototype.toString.call(val).slice(8, -1)\n}\n\n/**\n * Returns the object type of the given payload\n * @param {*} val\n * @returns {string}\n */\nexport function getType(val, toLowerCase = true) {\n  const type = getTypeName(val)\n  // console.log('type', type)\n  return (toLowerCase) ? lower(type) : type\n}\n\n// export function getType(val) {\n//   if (isNull(val)) return NULL\n//   const type = typeof val\n//   if (type === OBJECT) return ctorName(val).toLowerCase()\n//   return type\n// }\n\nfunction typeOf(kind, val) {\n  return typeof val === kind\n}\n\n/** \n * Check if value is function.\n * @param x\n * @return {x is Function}\n */\nexport const isFunction = typeOf.bind(null, FUNCTION)\n\n/** \n * Check if value is string.\n * @param x\n * @return {x is string}\n */\nexport const isString = typeOf.bind(null, STRING)\n\n/** \n * Check if value is undefined.\n * @param x\n * @return {x is undefined}\n */\nexport const isUndefined = typeOf.bind(null, UNDEFINED)\n\n/** \n * Check if value is not undefined.\n * @param x\n * @return {x is undefined}\n */\nexport function isDefined(x) {\n  return !isUndefined(x)\n}\n\n/** \n * @param x\n * @return {x is boolean}\n */\nexport const isBoolean = typeOf.bind(null, BOOLEAN)\n\n/** \n * @param x\n * @return {x is symobl}\n */\nexport const isSymbol = typeOf.bind(null, SYMBOL)\n\n/** \n * @param x\n * @return {x is boolean}\n * @example\n * isNull(null)\n * // true\n */\nexport function isNull(x) {\n  return x === null\n}\n\n/** \n * Check if value is number.\n * @param n\n * @return {boolean}\n * @example\n * > isNumber(0)\n * true\n * > isNumber(1)\n * true\n * > isNumber(1.1)\n * true\n * > isNumber(0xff)\n * true\n * > isNumber(0644)\n * true\n * > isNumber(6.2e5)\n * true\n * > isNumber(NaN)\n * false\n * > isNumber(Infinity)\n * true\n */\nexport function isNumber(n) {\n  return getType(n) === NUMBER && !isNaN(n)\n}\n\nexport function isNumberLike(n) {\n  return !isNaN(parseFloat(n))\n}\n\n/** \n * Check if value is ES2015 `class`.\n * @param x\n * @return {x is Class}\n */\nexport function isClass(x) {\n  if (isFunction(x)) {\n    return /^class /.test(Function.prototype.toString.call(x))\n  }\n  return false\n}\n\n/** \n * @template T\n * @param x\n * @return {x is Array<T>}\n */\nexport function isArray(x) {\n  return getType(x) === ARRAY\n}\n\n/** \n * @param obj\n * @return {obj is Object}\n */\nexport function isObject(obj) {\n  if (!isObjectLike(obj)) return false\n\n  let proto = obj\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto)\n  }\n\n  return Object.getPrototypeOf(obj) === proto\n}\n\nexport function isObjectLike(obj) {\n  return obj && (typeof obj === OBJECT || obj !== null)\n}\n\n/**\n* Tests if a value is a parseable JSON string.\n* @param {*} x - value to test\n* @returns {boolean} boolean indicating if a value is a parseable JSON string\n* @example\n* isJson('{\"a\":5}') // returns true\n* isJson('[]') // returns true\n* isJson('{a\":5}') // returns false\n*/\nexport function isJson(x) {\n  if (!isString(x) || !REGEX_JSON.test(x)) return false\n  try {\n    JSON.parse(x)\n  } catch (e) { \n    return false\n  }\n  return true\n}\n\n/**\n * Is primative scalar value\n * @param x\n * @return {boolean}\n * @example\n   isPrimitive(true) =>  true\n   isPrimitive({}) => false\n   isPrimitive(0) =>  true\n   isPrimitive('1') =>  true\n   isPrimitive(1.1) =>  true\n   isPrimitive(NaN) =>  true\n   isPrimitive(Infinity) =>  true\n   isPrimitive(function() {}) => false\n   isPrimitive(Date), => false\n   isPrimitive(null) =>  true\n   isPrimitive(undefined) =>  true\n */\nexport function isPrimitive(x) {\n  if (isNull(x)) return true\n  // if (isNaN(x)) return false\n  switch (typeof x) {\n    case STRING:\n    case NUMBER:\n    case SYMBOL:\n    case UNDEFINED:\n    case BOOLEAN:\n      return true\n    default:\n      return false\n  }\n}\n\n/**\n* Tests if an object has a specified method name.\n* @param {*} value - value to test\n* @param {*} property - property to test\n* @returns {boolean} boolean indicating if an object has a specified method name\n* @example\n* const obj = {\n*   key: myFunc,\n*   keyTwo: 'foobar'\n* }\n* isMethod(obj, 'key' ) // returns true\n* isMethod(obj, 'keyTwo' ) // returns false\n* isMethod(obj, 'otherThing' ) // returns false\n*/\nexport function isMethod(value, prop) {\n  return isObject(value) && isFunction(value[prop])\n}\n\n/**\n * Returns true if the input is a Promise.\n * @param {*} x - The input to test\n * @returns {boolean}\n */\nexport function isPromise(x) {\n  if (!x) return false\n  return !!(!isUndefined(Promise) && x instanceof Promise || x.then && isFunction(x.then))\n}\n\n/**\n * Returns true if the input is a generator.\n * @param {*} x - The input to test\n * @returns {boolean}\n */\nexport function isGenerator(x) {\n  return isObjectLike(x) && isFunction(x.throw) && isFunction(x.return) && isFunction(x.next)\n}\n\n/** \n * Is generator function\n * @param x\n * @return {boolean}\n * @example\n   isGeneratorFunction(() => {}) =>  false\n   isGeneratorFunction(function* () => {}) =>  true\n   isGeneratorFunction(function * () {\n     yield 'my-val'\n   }))\n */\nexport function isGeneratorFunction(x) {\n  return getType(x) === GENERATOR_FUNCTION\n}\n\n/** \n * Is async function\n * @param x\n * @return {boolean}\n * @example\n   isAsyncFunction(() => {}) =>  false\n   isAsyncFunction(async () => {}) =>  true\n */\nexport function isAsyncFunction(x) {\n  return getType(x) === ASYNC_FUNCTION\n}\n\n\nexport function ctorName(x) {\n  return isFunction(x.constructor) ? x.constructor.name : null\n}\n\n/**\n * Returns true if the input is a Set.\n * @param {*} x - The input to test\n * @returns {boolean}\n */\nexport function isSet(value) {\n  return value instanceof Set\n}\n\n/**\n * Returns true if the input is a Map.\n * @param {*} x - The input to test\n * @returns {boolean}\n */\nexport function isMap(value) {\n  return value instanceof Map\n}\n\n/**\n * Check if value is regexp\n * @param {*} value - Value to check\n * @return {boolean} \n */\nexport function isRegex(value) {\n  return value instanceof RegExp\n}\n\n/**\n * Check if value is Buffer\n * @param {*} value - Value to check\n * @return {boolean} \n */\nexport function isBuffer(val) {\n  if (val.constructor && isFunction(val.constructor.isBuffer)) {\n    return val.constructor.isBuffer(val)\n  }\n  return false\n}\n\n/**\n * Check if value is Error\n * @param x - Object to check\n * @return {Boolean} If value is error\n * @example\n * isError(new Error()) // True\n */\nexport function isError(x) {\n  return x instanceof Error || (isString(x.message) && x.constructor && isNumber(x.constructor.stackTraceLimit))\n}\n\n/**\n * Check if value error like (i.e has the name and message properties, both of which are strings)\n * @param obj - Object to check\n * @return {Boolean} If object is error-like\n * via https://github.com/Luke-zhang-04/utils/blob/master/src/typeGuards.ts#L62\n * @example\n *\n * ```js\n * isErrorLike(new Error()) // True\n * isErrorLike({name: \"Error!\", message: \"This is an error\", other: 0}) // True\n * isErrorLike({}) // False\n * isErrorLike({name: \"Error\", message: null}) // False\n *\n * // Works as a typguard\n * const something = {name: \"Error\", message: \"This is an error\"} as unknown\n *\n * if (isErrorLike(something)) {\n *   console.log(something.name) // No Typescript error\n * }\n * ```\n */\nexport function isErrorLike(obj) {\n  return isObjectLike(obj) && isString(obj.message) && isString(obj.name)\n}\n\nfunction errorType(ErrKind, value) {\n  if (typeof value !== 'object' || isNull(value)) return false\n  // Check for `TypeError` objects from the same realm (same Node.js `vm` or same `Window` object)...\n  if (value instanceof ErrKind) return true\n  const typeName = getType(new ErrKind(''))\n  // All `TypeError` objects are `Error` objects...\n  if (isError(value)) {\n    while (value) {\n      if (getType(value) === typeName) {\n        return true\n      }\n        value = Object.getPrototypeOf(value)\n    }\n  }\n  return false\n}\n\nexport const isTypeError = errorType.bind(null, TypeError)\n\nexport const isSyntaxError = errorType.bind(null, SyntaxError)\n\n/** \n * @param func - function to check if noOp\n * @return {Boolean} - is noOp\n * @examples\n   function foo() {}\n   isNoOp(foo) // true\n   isNoOp(() => { }) // true\n   isNoOp(() => { console.log('hi') }) // false\n */\nexport function isNoOp(func) {\n  if (!isFunction(func)) return false\n  const emptyFunc = /{(\\r|\\n|\\s)*}/gm\n  const noOpStr = noOp + ''\n  const funcString = (func.toString().match(emptyFunc) || [''])[0].replace(emptyFunc, noOpStr)\n  return noOpStr === funcString\n}\n\n/**\n * Check if value is function arguments\n * @param {*} val \n * @returns \n */\nexport function isArguments(val) {\n  try {\n    if (isNumber(val.length) && isFunction(val.callee)) return true\n  } catch (err) {\n    if (err.message.indexOf('callee') !== -1) return true\n  }\n  return false\n}\n\n/**\n * Check if value is truthy\n * @param {*} value \n * @return {Boolean} - is truthy value\n */\nexport function isTruthy(v) {\n  if (isString(v) && v.toLowerCase() === 'false') return false\n  return !!v\n}\n\n/**\n * Check if value is falsy\n * @param {*} x \n * @return {Boolean} - is falsy value\n * @example\n * isFalsy(false) // returns true\n * isFalsy(null) // returns true\n * isFalsy('') // returns true\n * isFalsy(0) // returns true\n * isFalsy(void 0) // returns true\n * isFalsy(NaN) // returns true\n * isFalsy([]) // returns false\n */\nexport function isFalsy(x) {\n  return x ? false : true\n}\n\n// ^ future updates https://github.com/thenativeweb/boolean\n\n/**\n * Check if value is true\n * @param {*} x \n * @return {Boolean} - is true\n */\nexport function isTrue(x) {\n  return x === true\n}\n\n/**\n * Check if value is true\n * @param {*} x \n * @return {Boolean} - is true\n */\nexport function isFalse(x) {\n  return x === false\n}\n\n/**\n * Check if value is email\n * @param {*} x \n * @return {Boolean} - is email like value\n */\nexport function isEmail(x) {\n  if (x.length > 320) return false\n  return REGEX_EMAIL.test(x)\n}\n\n/**\n * Check if valie is date\n * @param {*} val \n * @returns {Boolean}\n */\nexport function isDate(x) {\n  if (x instanceof Date) return true\n  return isFunction(x.toDateString) && isFunction(x.getDate) && isFunction(x.setDate)\n}\n\n/**\n * Check if value is ISO date e.g. '2022-01-02T06:45:28.547Z'\n * @param {*} x\n * @return {Boolean} - is email like value\n */\nexport function isIsoDate(x) {\n  return REGEX_ISO.test(x)\n}\n\n/**\n * Is value empty\n * @param {*} x \n * @returns {Boolean}\n * @example\n * isEmpty(null)\n *\n * isEmpty([1, 2, 3])\n * // => false\n *\n * isEmpty('abc')\n * // => false\n *\n * isEmpty({ 'a': 1 })\n * // => false\n */\nexport function isEmpty(x) {\n  if (isNull(x)) return true\n  if (isArray(x)) return !x.length\n  if (isSet(x) || isMap(x)) return !x.size\n  if (isObject(x)) return !Object.keys(x).length\n  return true\n}\n\n/* ────────────────────\nHTML Element checks\n─────────────────────── */\n\n/** \n * @param obj\n * @return {obj is NodeList}\n */\nexport function isNodeList(obj) {\n  return NodeList.prototype.isPrototypeOf(obj)\n}\n\n/**\n * Check if input is DOM element\n * @param {HTMLElement|*} element\n * @return {boolean} \n */\nexport function isElement(element, type) {\n  const isEl = element instanceof Element || element instanceof HTMLDocument\n  if (!isEl || !type) return isEl\n  return isNodeType(element, type)\n}\n\n/**\n * Check if element is specific DOMNode type\n * @param {HTMLElement|*} element\n * @param {String} type\n * @return {boolean}\n */\nexport function isNodeType(element, type = '') {\n  return element && element.nodeName === type.toUpperCase()\n}\n\nfunction bindArgs(fn, ...boundArgs) {\n  return function(...args) {\n    return fn(...args, ...boundArgs)\n  }\n}\n\n/**\n * Check if element is form element\n * @param {HTMLElement} element\n * @return {boolean} \n */\nexport const isForm = bindArgs(isElement, FORM)\n\n/**\n * Check if element is button element\n * @param {HTMLElement} element\n * @return {boolean} \n */\nexport const isButton = bindArgs(isElement, BUTTON)\n\n/**\n * Check if element is input element\n * @param {HTMLElement} element\n * @return {boolean} \n */\nexport const isInput = bindArgs(isElement, INPUT)\n\n/**\n * Check if element is select element\n * @param {HTMLElement} element\n * @return {boolean} \n */\nexport const isSelect = bindArgs(isElement, SELECT)\n\n/**\n * Check if DOM element is hidden\n * @param {HTMLElement|null|undefined} element\n * @param {HTMLElement|null|undefined} until\n * @return {boolean}\n */\nexport function isHidden(element, until) {\n  if (!element || getComputedStyle(element).visibility === HIDDEN) return true\n\n  while (element) {\n    if (until != null && element === until) return false\n    if (getComputedStyle(element).display === NONE) return true\n    element = element.parentElement\n  }\n\n  return false\n}\n\n/* ────────────────────\nEnsure Values\n─────────────────────── */\n\n/**\n * Ensure value returned is Array\n * @param {*} singleOrArray \n * @returns [*]\n */\nexport function ensureArray(singleOrArray) {\n  if (!singleOrArray) return []\n  if (isArray(singleOrArray)) return singleOrArray\n  return [singleOrArray]\n}\n"], "names": ["FUNCTION", "STRING", "UNDEFINED", "BOOLEAN", "OBJECT", "ARRAY", "NUMBER", "SYMBOL", "NULL", "ERROR", "TYPE_ERROR", "SYNTAX_ERROR", "ASYNC_FUNCTION", "GENERATOR_FUNCTION", "ASYNC_GENERATOR_FUNCTION", "noOp", "ANY", "ALL", "NONE", "HIDDEN", "PREFIX", "FORM", "INPUT", "BUTTON", "SELECT", "CHANGE", "SUBMIT", "REGEX_ISO", "REGEX_EMAIL", "REGEX_JSON", "PROCESS", "process", "ENV", "env", "NODE_ENV", "is<PERSON><PERSON>", "isStaging", "isDev", "<PERSON><PERSON><PERSON><PERSON>", "document", "isLocalHost", "window", "location", "hostname", "isNode", "versions", "node", "isDeno", "<PERSON><PERSON>", "core", "isWebWorker", "self", "constructor", "name", "isJsDom", "navigator", "userAgent", "includes", "text", "method", "s", "char<PERSON>t", "slice", "upper", "bind", "lower", "getTypeName", "val", "isNull", "ctorName", "Object", "prototype", "toString", "call", "getType", "toLowerCase", "type", "typeOf", "kind", "isFunction", "isString", "isUndefined", "isDefined", "x", "isBoolean", "isSymbol", "isNumber", "n", "isNaN", "isNumberLike", "parseFloat", "isClass", "test", "Function", "isArray", "isObject", "obj", "isObjectLike", "proto", "getPrototypeOf", "isJson", "JSON", "parse", "e", "isPrimitive", "isMethod", "value", "prop", "isPromise", "Promise", "then", "isGenerator", "next", "isGeneratorFunction", "isAsyncFunction", "isSet", "Set", "isMap", "Map", "isRegex", "RegExp", "<PERSON><PERSON><PERSON><PERSON>", "isError", "Error", "message", "stackTraceLimit", "isErrorLike", "errorType", "<PERSON><PERSON><PERSON><PERSON>", "typeName", "isTypeError", "TypeError", "isSyntaxError", "SyntaxError", "isNoOp", "func", "emptyFunc", "noOpStr", "match", "replace", "isArguments", "length", "callee", "err", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "v", "isFalsy", "isTrue", "isFalse", "isEmail", "isDate", "Date", "toDateString", "getDate", "setDate", "isIsoDate", "isEmpty", "size", "keys", "isNodeList", "NodeList", "isPrototypeOf", "isElement", "element", "isEl", "Element", "HTMLDocument", "isNodeType", "nodeName", "toUpperCase", "bind<PERSON><PERSON><PERSON>", "fn", "boundArgs", "isForm", "isButton", "isInput", "isSelect", "isHidden", "until", "getComputedStyle", "visibility", "display", "parentElement", "ensureArray", "singleOrArray"], "mappings": "AAKaA,IAAAA,EAAW,WACXC,EAAS,SACTC,EAAY,YACZC,EAAU,UACVC,EAAS,SACTC,EAAQ,QACRC,EAAS,SACTC,EAAS,SACTC,EAAO,OACPC,EAAQ,QACRC,EAAa,YACbC,EAAe,cACfC,EAAiB,gBACjBC,EAAqB,oBACrBC,EAA2B,yBAE3BC,EAAO,aACPC,EAAM,MACNC,EAAM,IACNC,EAAO,OACPC,EAAS,SACTC,EAAS,KAGTC,EAAO,OACPC,EAAQ,QACRC,EAAS,SACTC,EAAS,SACTC,EAAS,SACTC,EAAS,SAGTC,EAAY,6EACZC,EAAc,wIACdC,EAAa,8BAQpBC,EAxCmB,oBAwCFC,QAAwBA,QAAU,GAG5CC,EAAOF,EAAQG,KAAOH,EAAQG,IAAIC,UAAa,GAG/CC,EAAiB,eAARH,EAGTI,EAAoB,YAARJ,EAGZK,EAAgB,gBAARL,EAGRM,EAvDY,oBAuDOC,SAEnBC,EAAcF,GAA0C,cAA7BG,OAAOC,SAASC,SAG3CC,EAA6B,MAApBd,EAAQe,UAA6C,MAAzBf,EAAQe,SAASC,KAGtDC,EA/DY,oBA+DIC,WA/DJ,IA+DiCA,KAAKC,KAGlDC,EAhES,iBAgEYC,MAAmBA,KAAKC,aAAyC,+BAA1BD,KAAKC,YAAYC,KAG7EC,EAAWhB,GAA6B,WAAhBG,OAAOY,MArEnB,oBAqEiDE,gBArEjD,IAqEmFA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,UAM1N,SAASC,EAAKC,EAAQC,GACpB,OAAOA,EAAEC,OAAO,GAAGF,KAAYC,EAAEE,MAAM,GAGzC,IAAMC,EAAQL,EAAKM,KAAK,KAAM,eACxBC,EAAQP,EAAKM,KAAK,KAAM,wBAOdE,EAAYC,GAC1B,OAAIC,EAAOD,GAAaJ,EAlFN,QAJE,iBAuFLI,EAAkBE,GAASF,GAAOG,OAAOC,UAAUC,SAASC,KAAKN,GAAKL,MAAM,GAAI,YAQjFY,EAAQP,EAAKQ,YAAAA,IAAAA,GAAc,GACzC,IAAMC,EAAOV,EAAYC,GAEzB,OAAQQ,EAAeV,EAAMW,GAAQA,EAUvC,SAASC,EAAOC,EAAMX,GACpB,cAAcA,IAAQW,EAQXC,IAAAA,EAAaF,EAAOb,KAAK,KAzHd,YAgIXgB,EAAWH,EAAOb,KAAK,KA/Hd,UAsITiB,EAAcJ,EAAOb,KAAK,KArId,sBA4ITkB,EAAUC,GACxB,OAAQF,EAAYE,GAOTC,IAAAA,EAAYP,EAAOb,KAAK,KAnJd,WAyJVqB,EAAWR,EAAOb,KAAK,KArJd,mBA8JNI,EAAOe,GACrB,OAAa,OAANA,WAyBOG,GAASC,GACvB,MA1LoB,WA0Lbb,EAAQa,KAAkBC,MAAMD,YAGzBE,GAAaF,GAC3B,OAAQC,MAAME,WAAWH,aAQXI,GAAQR,GACtB,QAAIJ,EAAWI,cACIS,KAAKC,SAAStB,UAAUC,SAASC,KAAKU,aAU3CW,GAAQX,GACtB,MApNmB,UAoNZT,EAAQS,YAODY,GAASC,GACvB,IAAKC,GAAaD,GAAM,SAGxB,IADA,IAAIE,EAAQF,EAC4B,OAAjC1B,OAAO6B,eAAeD,IAC3BA,EAAQ5B,OAAO6B,eAAeD,GAGhC,OAAO5B,OAAO6B,eAAeH,KAASE,WAGxBD,GAAaD,GAC3B,OAAOA,IAxOa,iBAwOEA,GAA0B,OAARA,YAY1BI,GAAOjB,GACrB,IAAKH,EAASG,KAAOtD,EAAW+D,KAAKT,GAAI,SACzC,IACEkB,KAAKC,MAAMnB,GACX,MAAOoB,GACP,SAEF,kBAoBcC,GAAYrB,GAC1B,GAAIf,EAAOe,GAAI,SAEf,cAAeA,GACb,IAtRkB,SAuRlB,IAlRkB,SAmRlB,IAlRkB,SAmRlB,IAxRqB,YAyRrB,IAxRmB,UAyRjB,SACF,QACE,mBAkBUsB,GAASC,EAAOC,GAC9B,OAAOZ,GAASW,IAAU3B,EAAW2B,EAAMC,aAQ7BC,GAAUzB,GACxB,QAAKA,OACMF,EAAY4B,UAAY1B,aAAa0B,SAAW1B,EAAE2B,MAAQ/B,EAAWI,EAAE2B,gBAQpEC,GAAY5B,GAC1B,OAAOc,GAAad,IAAMJ,EAAWI,UAAYJ,EAAWI,WAAaJ,EAAWI,EAAE6B,eAcxEC,GAAoB9B,GAClC,MAtUgC,sBAsUzBT,EAAQS,YAWD+B,GAAgB/B,GAC9B,MAnV4B,kBAmVrBT,EAAQS,YAIDd,GAASc,GACvB,OAAOJ,EAAWI,EAAE/B,aAAe+B,EAAE/B,YAAYC,KAAO,cAQ1C8D,GAAMT,GACpB,OAAOA,aAAiBU,aAQVC,GAAMX,GACpB,OAAOA,aAAiBY,aAQVC,GAAQb,GACtB,OAAOA,aAAiBc,gBAQVC,GAAStD,GACvB,SAAIA,EAAIf,cAAe2B,EAAWZ,EAAIf,YAAYqE,YACzCtD,EAAIf,YAAYqE,SAAStD,YAYpBuD,GAAQvC,GACtB,OAAOA,aAAawC,OAAU3C,EAASG,EAAEyC,UAAYzC,EAAE/B,aAAekC,GAASH,EAAE/B,YAAYyE,0BAwB/EC,GAAY9B,GAC1B,OAAOC,GAAaD,IAAQhB,EAASgB,EAAI4B,UAAY5C,EAASgB,EAAI3C,MAGpE,SAAS0E,GAAUC,EAAStB,GAC1B,GAAqB,iBAAVA,GAAsBtC,EAAOsC,GAAQ,SAEhD,GAAIA,aAAiBsB,EAAS,SAC9B,IAAMC,EAAWvD,EAAQ,IAAIsD,EAAQ,KAErC,GAAIN,GAAQhB,GACV,KAAOA,GAAO,CACZ,GAAIhC,EAAQgC,KAAWuB,EACrB,SAEAvB,EAAQpC,OAAO6B,eAAeO,GAGpC,SAGWwB,IAAAA,GAAcH,GAAU/D,KAAK,KAAMmE,WAEnCC,GAAgBL,GAAU/D,KAAK,KAAMqE,sBAWlCC,GAAOC,GACrB,IAAKxD,EAAWwD,GAAO,SACvB,IAAMC,EAAY,kBACZC,EAAU1H,EAAO,GAEvB,OAAO0H,KADaF,EAAK/D,WAAWkE,MAAMF,IAAc,CAAC,KAAK,GAAGG,QAAQH,EAAWC,YAStEG,GAAYzE,GAC1B,IACE,GAAImB,GAASnB,EAAI0E,SAAW9D,EAAWZ,EAAI2E,QAAS,SACpD,MAAOC,GACP,IAAuC,IAAnCA,EAAInB,QAAQoB,QAAQ,UAAkB,SAE5C,kBAQcC,GAASC,GACvB,QAAIlE,EAASkE,IAA0B,UAApBA,EAAEvE,gBACZuE,YAgBKC,GAAQhE,GACtB,OAAOA,WAUOiE,GAAOjE,GACrB,OAAa,IAANA,WAQOkE,GAAQlE,GACtB,OAAa,IAANA,WAQOmE,GAAQnE,GACtB,QAAIA,EAAE0D,OAAS,MACRjH,EAAYgE,KAAKT,YAQVoE,GAAOpE,GACrB,OAAIA,aAAaqE,MACVzE,EAAWI,EAAEsE,eAAiB1E,EAAWI,EAAEuE,UAAY3E,EAAWI,EAAEwE,kBAQ7DC,GAAUzE,GACxB,OAAOxD,EAAUiE,KAAKT,YAmBR0E,GAAQ1E,GACtB,SAAIf,EAAOe,KACPW,GAAQX,GAAYA,EAAE0D,OACtB1B,GAAMhC,IAAMkC,GAAMlC,GAAYA,EAAE2E,KAChC/D,GAASZ,IAAYb,OAAOyF,KAAK5E,GAAG0D,kBAY1BmB,GAAWhE,GACzB,OAAOiE,SAAS1F,UAAU2F,cAAclE,YAQ1BmE,GAAUC,EAASxF,GACjC,IAAMyF,EAAOD,aAAmBE,SAAWF,aAAmBG,aAC9D,OAAKF,GAASzF,EACP4F,GAAWJ,EAASxF,GADAyF,WAUbG,GAAWJ,EAASxF,GAClC,gBADkCA,IAAAA,EAAO,IAClCwF,GAAWA,EAAQK,WAAa7F,EAAK8F,cAG9C,SAASC,GAASC,OAAOC,6BACvB,kBACE,OAAOD,+CAAeC,KASbC,IAAAA,GAASH,GAASR,GA9lBX,QAqmBPY,GAAWJ,GAASR,GAnmBX,UA0mBTa,GAAUL,GAASR,GA3mBX,SAknBRc,GAAWN,GAASR,GAhnBX,mBAwnBNe,GAASd,EAASe,GAChC,IAAKf,GAhoBe,WAgoBJgB,iBAAiBhB,GAASiB,WAAuB,SAEjE,KAAOjB,GAAS,CACd,GAAa,MAATe,GAAiBf,IAAYe,EAAO,SACxC,GAroBgB,SAqoBZC,iBAAiBhB,GAASkB,QAAkB,SAChDlB,EAAUA,EAAQmB,cAGpB,kBAYcC,GAAYC,GAC1B,OAAKA,EACD3F,GAAQ2F,GAAuBA,EAC5B,CAACA,GAFmB"}