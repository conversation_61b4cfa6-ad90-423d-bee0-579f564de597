var e=require("dlv"),r=require("@analytics/type-utils");function t(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=t(e);function o(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function i(e){if(!r.isBrowser)return!1;var t=e||document.referrer;if(t){var n=window.document.location.port,o=t.split("/")[2];return n&&(o=o.replace(":"+n,"")),o!==window.location.hostname}return!1}function a(e,r){var t=(e.split("?")||[,])[1];if(!t||-1===t.indexOf(r))return e;var n=new RegExp("(\\&|\\?)"+r+'([_A-Za-z0-9"+=.\\/\\-@%]+)',"g"),o=("?"+t).replace(n,"").replace(/^&/,"?");return e.replace("?"+t,o)}function u(e){return function(e){for(var r,t=Object.create(null),n=/([^&=]+)=?([^&]*)/g;r=n.exec(e);){var i=o(r[1]),a=o(r[2]);if(i)if("[]"===i.substring(i.length-2)){var u=t[i=i.substring(0,i.length-2)]||(t[i]=[]);t[i]=Array.isArray(u)?u:[],t[i].push(a)}else t[i]=""===a||a}for(var c in t){var l=c.split("[");l.length>1&&(s(t,l.map(function(e){return e.replace(/[?[\]\\ ]/g,"")}),t[c]),delete t[c])}return t}(function(e){if(e){var t=e.match(/\?(.*)/);return t&&t[1]?t[1].split("#")[0]:""}return r.isBrowser&&window.location.search.substring(1)}(e))}function s(e,r,t){for(var n=r.length-1,o=0;o<n;++o){var i=r[o];if("__proto__"===i||"constructor"===i)break;i in e||(e[i]={}),e=e[i]}e[r[n]]=t}function c(e){if(!r.isBrowser)return null;var t=document.createElement("a");return t.setAttribute("href",e),t.hostname}function l(e){return(c(e)||"").split(".").slice(-2).join(".")}function f(e){var r=e.split(".");return r.length>1?r.slice(0,-1).join("."):e}var m={trimTld:f,getDomainBase:l,getDomainHost:c},p="q",x={"daum.net":p,"eniro.se":"search_word","naver.com":"query","yahoo.com":"p","msn.com":p,"aol.com":p,"ask.com":p,"baidu.com":"wd","yandex.com":"text","rambler.ru":"words",google:p,"bing.com":{p:p,n:"live"}};Object.defineProperty(exports,"dotProp",{enumerable:!0,get:function(){return n.default}}),exports.decodeUri=o,exports.getBrowserLocale=function(){if(r.isBrowser){var e=navigator,t=e.languages;return e.userLanguage||(t&&t.length?t[0]:e.language)}},exports.getTimeZone=function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){}},exports.isExternalReferrer=i,exports.isScriptLoaded=function(e){if(!r.isBrowser)return!0;var t=document.getElementsByTagName("script");return!!Object.keys(t).filter(function(n){var o=t[n].src;return r.isString(e)?-1!==o.indexOf(e):!!r.isRegex(e)&&o.match(e)}).length},exports.paramsClean=a,exports.paramsGet=function(e,r){return o((RegExp(e+"=(.+?)(&|$)").exec(r)||[,""])[1])},exports.paramsParse=u,exports.paramsRemove=function(e,t){return r.isBrowser?new Promise(function(r,n){if(window.history&&window.history.replaceState){var o=window.location.href,i=a(o,e);o!==i&&history.replaceState({},"",i)}return t&&t(),r()}):Promise.resolve()},exports.parseReferrer=function(e,t){if(!r.isBrowser)return!1;var n={source:"(direct)",medium:"(none)",campaign:"(not set)"};e&&i(e)&&(n.referrer=e);var o=function(e){if(!e||!r.isBrowser)return!1;var t=l(e),n=document.createElement("a");if(n.href=e,n.hostname.indexOf("google")>-1&&(t="google"),x[t]){var o=x[t],a=new RegExp(("string"==typeof o?o:o.p)+"=.*?([^&#]*|$)","gi"),u=n.search.match(a);return{source:o.n||f(t),medium:"organic",term:(u?u[0].split("=")[1]:"")||"(not provided)"}}var s=i(e)?"referral":"internal";return{source:n.hostname,medium:s}}(e);o&&Object.keys(o).length&&(n=Object.assign({},n,o));var a=u(t),s=Object.keys(a);if(!s.length)return n;var c=s.reduce(function(e,r){return r.match(/^utm_/)&&(e[""+r.replace(/^utm_/,"")]=a[r]),r.match(/^(d|g)clid/)&&(e.source="google",e.medium=a.gclid?"cpc":"cpm",e[r]=a[r]),e},{});return Object.assign({},n,c)},exports.throttle=function(e,r){var t,n,o,i=null,a=0,u=function(){a=new Date,i=null,o=e.apply(t,n)};return function(){var s=new Date;a||(a=s);var c=r-(s-a);return t=this,n=arguments,c<=0?(clearTimeout(i),i=null,a=s,o=e.apply(t,n)):i||(i=setTimeout(u,c)),o}},exports.url=m,exports.uuid=function(){for(var e="",r=0,t=4294967295*Math.random()|0;r++<36;){var n="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"[r-1],o=15&t;e+="-"==n||"4"==n?n:("x"==n?o:3&o|8).toString(16),t=r%8==0?4294967295*Math.random()|0:t>>4}return e};
//# sourceMappingURL=analytics-utils.js.map
