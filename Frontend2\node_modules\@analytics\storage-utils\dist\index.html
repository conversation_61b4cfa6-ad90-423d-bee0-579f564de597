<!doctype html>
<html>
  <head>
    <title>Storage utils</title>
    <link rel="icon" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAIAAACQd1PeAAAADElEQVQI12P4//8/AAX+Av7czFnnAAAAAElFTkSuQmCC">
    <script type="text/javascript" src="./browser/storage-utils.js" charset="utf-8"></script>
    <style media="screen">
      html {
        padding: 0 20px;
      }
      body {
        display: flex;
        height: 200vh;
      }
      h3 b {
        color: green;
      }
      .demo {
        display: flex;
      }
      .data {
        margin-left: 30px;
        min-width: 360px;
      }
      .data pre {
        color: #637c84;
        font-size: 16px;
        padding: 16px 16px;
        background-color: #f4f5f6;
        min-height: 300px;
      }
      .github-corner svg {
        height: 80px;
        width: 80px;
        fill: #151513;
        color: #fff;
        position: absolute;
        top: 0;
        border: 0;
        right: 0
      }
      .github-corner:hover .octo-arm {
        animation: octocat-wave .56s ease-in-out
      }
      @keyframes octocat-wave {
        0%,to { transform: rotate(0) }
        20%,60% { transform: rotate(-25deg) }
        40%,80% { transform: rotate(10deg) }
      }
      @media (max-width: 720px) {
        .content, .content-left, iframe {
          flex-direction: column;
          max-width: 100%;
          min-width: 100%;
        }
        
        h1 { font-size: 24px; }
        #protected, #large-div div {
          font-size: 18px;
        }
        .github-corner svg {
          height: 60px;
          width: 60px;
        }
      }
      @media (max-width: 500px) {
        .github-corner:hover .octo-arm { animation:none }
        .github-corner .octo-arm { animation: octocat-wave .56s ease-in-out }
      }
    </style>
  </head>
  <body>
    <a href="https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-storage" class="github-corner" aria-label="View source on GitHub"><svg viewBox="0 0 250 250" aria-hidden="true"><path d="M0,0 L115,115 L130,115 L142,142 L250,250 L250,0 Z"></path><path d="M128.3,109.0 C113.8,99.7 119.0,89.6 119.0,89.6 C122.0,82.7 120.5,78.6 120.5,78.6 C119.2,72.0 123.4,76.3 123.4,76.3 C127.3,80.9 125.5,87.3 125.5,87.3 C122.9,97.6 130.6,101.9 134.4,103.2" fill="currentColor" style="transform-origin:130px 106px" class="octo-arm"></path><path d="M115.0,115.0 C114.9,115.1 118.7,116.5 119.8,115.4 L133.7,101.6 C136.9,99.2 139.9,98.4 142.2,98.6 C133.8,88.0 127.5,74.4 143.8,58.0 C148.5,53.4 154.0,51.2 159.7,51.0 C160.3,49.4 163.2,43.6 171.4,40.1 C171.4,40.1 176.1,42.5 178.8,56.2 C183.1,58.6 187.2,61.8 190.9,65.4 C194.5,69.0 197.7,73.2 200.1,77.6 C213.8,80.2 216.3,84.9 216.3,84.9 C212.7,93.1 206.9,96.0 205.4,96.6 C205.1,102.4 203.0,107.8 198.3,112.5 C181.9,128.9 168.3,122.5 157.7,114.1 C157.9,116.9 156.7,120.9 152.7,124.9 L141.0,136.5 C139.8,137.7 141.6,141.9 141.8,141.8 Z" fill="currentColor" class="octo-body"></path></svg></a>

    <div>
      <h2>Storage Utils</h2>
      <p>Tiny storage utility with fallbacks. Open console & view page source for more details!</p>
      <div class='demo'>
        <div class='controls'>
          <div>
            <h3>All <span id="value-all"></span></h3>
            <p>Set value in all locations</p>
            <button id="set-all">Set "key" value in all</button>
            <button id="get-all">Get "key" value in all</button>
            <button id="remove-all">Remove "key" value in all</button>
          </div>
          <div>
            <h3>LocalStorage <span id="value-localStorage"></span></h3>
            <button id="set-localStorage">Set "keyTwo" LocalStorage value</button>
            <button id="get-localStorage">Get "keyTwo" LocalStorage value</button>
            <button id="remove-localStorage">Remove "keyTwo" LocalStorage value</button>
          </div>
          <div>
            <h3>Cookies <span id="value-cookie"></span></h3>
            <button id="set-cookie">Set "keyThree" cookie value</button>
            <button id="get-cookie">Get "keyThree" cookie value</button>
            <button id="remove-cookie">Remove "keyThree" cookie value</button>
          </div>
          <div>
            <h3>SessionStorage <span id="value-session"></span></h3>
            <button id="set-session">Set "keySession" session value</button>
            <button id="get-session">Get "keySession" session value</button>
            <button id="remove-session">Remove "keySession" session value</button>
          </div>
          <div>
            <h3>Window <span id="value-window"></span></h3>
            <button id="set-window">Set "keyFour" window value</button>
            <button id="get-window">Get "keyFour" window value</button>
            <button id="remove-window">Remove "keyFour" window value</button>
          </div>
        </div>
        <div class='data'>
          <h3>Return value</h3>
          <pre id="preview"></pre>
        </div>
      </div>
    </div>
    
    <script type="text/javascript">
      const { getItem, setItem, removeItem, hasCookies, hasLocalStorage } = utilStorage
      console.log('utilStorage API', utilStorage)
      console.log('hasCookie', hasCookies())
      console.log('hasLocalStorage', hasLocalStorage())

      const preview = document.querySelector('#preview')

      const getButton = document.querySelector('#get-all')
      const updateButton = document.querySelector('#set-all')
      const removeButton = document.querySelector('#remove-all')


      const getButtonLs = document.querySelector('#get-localStorage')
      const updateButtonLs = document.querySelector('#set-localStorage')
      const removeButtonLs = document.querySelector('#remove-localStorage')

      const getButtonCookie = document.querySelector('#get-cookie')
      const updateButtonCookie = document.querySelector('#set-cookie')
      const removeButtonCookie = document.querySelector('#remove-cookie')

      const getButtonSession = document.querySelector('#get-session')
      const updateButtonSession = document.querySelector('#set-session')
      const removeButtonSession = document.querySelector('#remove-session')

      const getButtonWindow = document.querySelector('#get-window')
      const updateButtonWindow = document.querySelector('#set-window')
      const removeButtonWindow = document.querySelector('#remove-window')

      function renderData(data, value, e) {
        if (data) {
          preview.innerText = JSON.stringify(data, null, 2)
        }
        if (typeof value !== 'undefined' && e) {
          const type = (typeof e === 'string') ? e : e.target.id.replace(/^(.*)-/, '')
          const slug = `#value-${type}`
          console.log('slug', slug)
          if (value !== '') {
            document.querySelector(slug).innerHTML = `value = <b>"${value}"</b>`
          } else {
            document.querySelector(slug).innerHTML = ``
          }
        }
      }

      /* Set all */
      const keyOne = 'key'
      getButton.addEventListener('click', (e) => {
        const value = getItem(keyOne, '*')
        console.log('Get all values', value)
        renderData(value)
      })
      updateButton.addEventListener('click', (e) => {
        const updatedValue = new Date().getTime()
        const values = setItem(keyOne, updatedValue, '*')
        console.log('Update all values', values)
        renderData(values, updatedValue, e)
      })
      removeButton.addEventListener('click', (e) => {
        const value = removeItem(keyOne)
        renderData(value, '', e)
        console.log('Cleared all values', value)
      })

      /* LocalStorage Example */
      const keyTwo = 'keyTwo'
      getButtonLs.addEventListener('click', (e) => {
        const value = getItem(keyTwo, 'localStorage')
        console.log(`Get localStorage "${keyTwo}" value`, value)
        renderData(value)
      })
      updateButtonLs.addEventListener('click', (e) => {
        const updatedValue = new Date().getTime()
        const value = setItem(keyTwo, updatedValue, 'localStorage')
        console.log(`Update localStorage "${keyTwo}" value`, value)
        renderData(value, updatedValue, e)
      })
      removeButtonLs.addEventListener('click', (e) => {
        const value = removeItem(keyTwo, 'localStorage')
        renderData(value, '', e)
        console.log(`Cleared localStorage "${keyTwo}" value`, value)
      })

      /* Cookie Example */
      const keyThree = 'keyThree'
      getButtonCookie.addEventListener('click', (e) => {
        const value = getItem(keyThree, 'cookie')
        console.log(`Get cookie "${keyThree}" value`, value)
        renderData(value)
      })
      updateButtonCookie.addEventListener('click', (e) => {
        const updatedValue = new Date().getTime()
        const value = setItem(keyThree, updatedValue, 'cookie')
        console.log(`Update cookie "${keyThree}" value`, value)
        renderData(value, updatedValue, e)
      })
      removeButtonCookie.addEventListener('click', (e) => {
        const value = removeItem(keyThree, 'cookie')
        renderData(value, '', e)
        console.log(`Cleared cookie "${keyThree}" value`, value)
      })

      /* SessionStorage Example */
      const keyFive = 'keySession'
      getButtonSession.addEventListener('click', (e) => {
        const value = getItem(keyFive, 'sessionStorage')
        console.log(`Get sessionStorage "${keyFive}" value`, value)
        renderData(value)
      })
      updateButtonSession.addEventListener('click', (e) => {
        const updatedValue = new Date().getTime()
        const value = setItem(keyFive, updatedValue, 'sessionStorage')
        console.log(`Update sessionStorage "${keyFive}" value`, value)
        renderData(value, updatedValue, e)
      })
      removeButtonSession.addEventListener('click', (e) => {
        const value = removeItem(keyFive, 'sessionStorage')
        renderData(value, '', e)
        console.log(`Cleared sessionStorage "${keyFive}" value`, value)
      })

      /* Window Example */
      const keyFour = 'keyFour'
      getButtonWindow.addEventListener('click', (e) => {
        const value = getItem(keyFour, 'global')
        console.log(`Get window "${keyFour}" value`, value)
        renderData(value)
      })
      updateButtonWindow.addEventListener('click', (e) => {
        const updatedValue = new Date().getTime()
        const value = setItem(keyFour, updatedValue, 'global')
        console.log(`Update window "${keyFour}" value`, value)
        renderData(value, updatedValue, e)
      })
      removeButtonWindow.addEventListener('click', (e) => {
        const value = removeItem(keyFour, 'global')
        renderData(value, '', e)
        console.log(`Cleared window "${keyFour}" value`, value)
      })

      // Initial render
      renderData(null, getItem(keyOne, '*').localStorage, 'all')
      renderData(null, getItem(keyTwo, 'localStorage'), 'localStorage')
      renderData(null, getItem(keyThree, 'cookie'), 'cookie')
      renderData(null, getItem(keyFour, 'global'), 'window')
    ;</script>
  </body>
</html>
