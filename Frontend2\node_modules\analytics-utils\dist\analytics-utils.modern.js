export{default as dot<PERSON>rop}from"dlv";import{isBrowser as e,isString as t,isRegex as n}from"@analytics/type-utils";function r(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}function o(){if(!e)return;const{language:t,languages:n,userLanguage:r}=navigator;return r||(n&&n.length?n[0]:t)}function c(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){}}function i(t){if(!e)return!1;const n=t||document.referrer;if(n){const e=window.document.location.port;let t=n.split("/")[2];return e&&(t=t.replace(`:${e}`,"")),t!==window.location.hostname}return!1}function a(r){if(!e)return!0;const o=document.getElementsByTagName("script");return!!Object.keys(o).filter(e=>{const{src:c}=o[e];return t(r)?-1!==c.indexOf(r):!!n(r)&&c.match(r)}).length}function u(e,t){const n=(e.split("?")||[,])[1];if(!n||-1===n.indexOf(t))return e;const r=new RegExp(`(\\&|\\?)${t}([_A-Za-z0-9"+=.\\/\\-@%]+)`,"g"),o=`?${n}`.replace(r,"").replace(/^&/,"?");return e.replace(`?${n}`,o)}function s(e,t){return r((RegExp(`${e}=(.+?)(&|$)`).exec(t)||[,""])[1])}function l(t){return function(e){let t,n=Object.create(null);const o=/([^&=]+)=?([^&]*)/g;for(;t=o.exec(e);){var c=r(t[1]),i=r(t[2]);if(c)if("[]"===c.substring(c.length-2)){var a=n[c=c.substring(0,c.length-2)]||(n[c]=[]);n[c]=Array.isArray(a)?a:[],n[c].push(i)}else n[c]=""===i||i}for(var u in n){var s=u.split("[");s.length>1&&(m(n,s.map(e=>e.replace(/[?[\]\\ ]/g,"")),n[u]),delete n[u])}return n}(function(t){if(t){const e=t.match(/\?(.*)/);return e&&e[1]?e[1].split("#")[0]:""}return e&&window.location.search.substring(1)}(t))}function m(e,t,n){for(var r=t.length-1,o=0;o<r;++o){var c=t[o];if("__proto__"===c||"constructor"===c)break;c in e||(e[c]={}),e=e[c]}e[t[r]]=n}function f(t,n){return e?new Promise((e,r)=>{if(window.history&&window.history.replaceState){const e=window.location.href,n=u(e,t);e!==n&&history.replaceState({},"",n)}return n&&n(),e()}):Promise.resolve()}function g(t){if(!e)return null;const n=document.createElement("a");return n.setAttribute("href",t),n.hostname}function p(e){return(g(e)||"").split(".").slice(-2).join(".")}function x(e){const t=e.split(".");return t.length>1?t.slice(0,-1).join("."):e}var d={trimTld:x,getDomainBase:p,getDomainHost:g};function h(t,n){if(!e)return!1;let r={source:"(direct)",medium:"(none)",campaign:"(not set)"};t&&i(t)&&(r.referrer=t);const o=function(t){if(!t||!e)return!1;let n=p(t);const r=document.createElement("a");if(r.href=t,r.hostname.indexOf("google")>-1&&(n="google"),y[n]){const e=y[n],t=new RegExp(("string"==typeof e?e:e.p)+"=.*?([^&#]*|$)","gi"),o=r.search.match(t);return{source:e.n||x(n),medium:"organic",term:(o?o[0].split("=")[1]:"")||"(not provided)"}}const o=i(t)?"referral":"internal";return{source:r.hostname,medium:o}}(t);o&&Object.keys(o).length&&(r=Object.assign({},r,o));const c=l(n),a=Object.keys(c);if(!a.length)return r;const u=a.reduce((e,t)=>(t.match(/^utm_/)&&(e[`${t.replace(/^utm_/,"")}`]=c[t]),t.match(/^(d|g)clid/)&&(e.source="google",e.medium=c.gclid?"cpc":"cpm",e[t]=c[t]),e),{});return Object.assign({},r,u)}const w="q",y={"daum.net":w,"eniro.se":"search_word","naver.com":"query","yahoo.com":"p","msn.com":w,"aol.com":w,"ask.com":w,"baidu.com":"wd","yandex.com":"text","rambler.ru":"words",google:w,"bing.com":{p:w,n:"live"}};function v(){for(var e="",t=0,n=4294967295*Math.random()|0;t++<36;){var r="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"[t-1],o=15&n;e+="-"==r||"4"==r?r:("x"==r?o:3&o|8).toString(16),n=t%8==0?4294967295*Math.random()|0:n>>4}return e}function b(e,t){var n,r,o,c=null,i=0,a=function(){i=new Date,c=null,o=e.apply(n,r)};return function(){var u=new Date;i||(i=u);var s=t-(u-i);return n=this,r=arguments,s<=0?(clearTimeout(c),c=null,i=u,o=e.apply(n,r)):c||(c=setTimeout(a,s)),o}}export{r as decodeUri,o as getBrowserLocale,c as getTimeZone,i as isExternalReferrer,a as isScriptLoaded,u as paramsClean,s as paramsGet,l as paramsParse,f as paramsRemove,h as parseReferrer,b as throttle,d as url,v as uuid};
//# sourceMappingURL=analytics-utils.modern.js.map
