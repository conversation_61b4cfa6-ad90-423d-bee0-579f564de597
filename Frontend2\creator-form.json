{"formType": "creator_registration", "description": "Creator registration form for joining LawVriksh as a content creator", "fields": {"name": {"type": "text", "required": true, "placeholder": "Enter your name", "validation": {"minLength": 1, "maxLength": 100}}, "email": {"type": "email", "required": true, "placeholder": "Enter your email", "validation": {"format": "email"}}, "phone_number": {"type": "tel", "required": true, "placeholder": "Enter your phone number", "validation": {"pattern": "phone"}}, "gender": {"type": "select", "required": false, "options": [{"value": "", "label": "Select Gender"}, {"value": "Male", "label": "Male"}, {"value": "Female", "label": "Female"}, {"value": "Other", "label": "Other"}, {"value": "Prefer not to say", "label": "Prefer not to say"}]}, "profession": {"type": "select", "required": false, "options": [{"value": "", "label": "Select Profession"}, {"value": "Student", "label": "Student"}, {"value": "Lawyer", "label": "Lawyer"}, {"value": "Other", "label": "Other"}]}, "interest_reason": {"type": "textarea", "required": false, "placeholder": "Tell us what interests you about <PERSON><PERSON><PERSON><PERSON>...", "rows": 3, "validation": {"maxLength": 500}}, "user_type": {"type": "hidden", "value": "creator", "required": true}}, "submitButton": {"text": "Join as <PERSON><PERSON>", "loadingText": "Submitting..."}, "successMessage": "Thank you for joining as a creator! We'll be in touch soon with more details.", "apiEndpoint": "/api/register-creator"}