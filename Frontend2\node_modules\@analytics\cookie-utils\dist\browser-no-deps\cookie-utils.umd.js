!function(e,o){"object"==typeof exports&&"undefined"!=typeof module?o(exports,require("@analytics/global-storage-utils")):"function"==typeof define&&define.amd?define(["exports","@analytics/global-storage-utils"],o):o((e||self).utilCookies={},e.globalStorageUtils)}(this,function(e,o){var t="cookie",n=a(),i=u,r=u;function s(e){return n?u(e,"",-1):o.remove(e)}function a(){if(void 0!==n)return n;var e=t+t;try{u(e,e),n=-1!==document.cookie.indexOf(e),s(e)}catch(e){n=!1}return n}function u(e,t,i,r,s,a){if("undefined"!=typeof window){var u=arguments.length>1;return!1===n&&(u?o.set(e,t):o.get(e)),u?document.cookie=e+"="+encodeURIComponent(t)+(i?"; expires="+new Date(+new Date+1e3*i).toUTCString()+(r?"; path="+r:"")+(s?"; domain="+s:"")+(a?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+e+"=")[1]||"").split(";")[0])}}e.COOKIE=t,e.getCookie=i,e.hasCookies=a,e.removeCookie=s,e.setCookie=r});
//# sourceMappingURL=cookie-utils.umd.js.map
