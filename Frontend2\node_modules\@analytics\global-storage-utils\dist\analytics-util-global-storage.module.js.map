{"version": 3, "file": "analytics-util-global-storage.module.js", "sources": ["../src/index.js"], "sourcesContent": ["import { OBJECT, PREFIX, UNDEFINED } from '@analytics/type-utils'\n\nexport const GLOBAL = 'global'\n\nexport const KEY = PREFIX + GLOBAL + PREFIX\n\nexport const globalContext = (typeof self === OBJECT && self.self === self && self) || (typeof global === OBJECT && global[GLOBAL] === global && global) || this\n\n/* initialize global object */\nif (!globalContext[KEY]) {\n  globalContext[KEY] = {}\n}\n/**\n * Get value from global context\n * @param {string} key - Key of value to get\n * @returns {*} value\n */\nexport function get(key) {\n  return globalContext[KEY][key]\n}\n\n/**\n * Set value to global context\n * @param {string} key - Key of value to set\n * @param {*} value \n * @returns value\n */\nexport function set(key, value) {\n  return globalContext[KEY][key] = value\n}\n\n/**\n * Remove value to global context\n * @param {string} key - Key of value to remove\n */\nexport function remove(key) {\n  delete globalContext[KEY][key]\n}\n\n/**\n * Wrap localStorage & session storage checks\n * @param {string} type - localStorage or sessionStorage\n * @param {string} storageOperation - getItem, setItem, removeItem\n * @param {function} fallbackFunction - fallback function\n */\nexport function wrap(type, operation, fallback) {\n  let fn\n  try {\n    if (hasSupport(type)) {\n      const storage = window[type]\n      fn = storage[operation].bind(storage)\n    }\n  } catch(e) {}\n  return fn || fallback\n}\n\nconst cache = {}\nexport function hasSupport(type) {\n  if (typeof cache[type] !== UNDEFINED) {\n    return cache[type]\n  }\n  try {\n    const storage = window[type]\n    // test for private safari\n    storage.setItem(UNDEFINED, UNDEFINED)\n    storage.removeItem(UNDEFINED)\n  } catch (err) {\n    return cache[type] = false\n  }\n  return cache[type] = true\n}\n\n/*\n// () => localStorage)\n// () => sessionStorage)\nexport function isSupported(getStorage) {\n  try {\n    const testKey = '__' + undef\n    getStorage().setItem(testKey, testKey)\n    getStorage().removeItem(testKey)\n    return true\n  } catch (e) {\n    return false\n  }\n}\n*/\n"], "names": ["GLOBAL", "KEY", "PREFIX", "globalContext", "self", "OBJECT", "global", "this", "get", "key", "set", "value", "remove", "wrap", "type", "operation", "fallback", "fn", "hasSupport", "storage", "window", "bind", "e", "cache", "UNDEFINED", "setItem", "removeItem", "err"], "mappings": "0EAEaA,IAAAA,EAAS,SAETC,EAAMC,EAFG,SAEeA,EAExBC,SAAwBC,OAASC,GAAUD,KAAKA,OAASA,MAAQA,aAAiBE,SAAWD,GAAUC,OAAM,SAAaA,QAAUA,aAAWC,WAW5IC,EAAIC,GAClB,OAAON,EAAcF,GAAKQ,YASZC,EAAID,EAAKE,GACvB,OAAOR,EAAcF,GAAKQ,GAAOE,WAOnBC,EAAOH,UACdN,EAAcF,GAAKQ,YASZI,EAAKC,EAAMC,EAAWC,GACpC,IAAIC,EACJ,IACE,GAAIC,EAAWJ,GAAO,CACpB,IAAMK,EAAUC,OAAON,GACvBG,EAAKE,EAAQJ,GAAWM,KAAKF,IAE/B,MAAMG,IACR,OAAOL,GAAMD,EA5CVb,EAAcF,KACjBE,EAAcF,GAAO,IA8CvB,IAAMsB,EAAQ,YACEL,EAAWJ,GACzB,UAAWS,EAAMT,KAAUU,EACzB,OAAOD,EAAMT,GAEf,IACE,IAAMK,EAAUC,OAAON,GAEvBK,EAAQM,QAAQD,EAAWA,GAC3BL,EAAQO,WAAWF,GACnB,MAAOG,GACP,OAAOJ,EAAMT,IAAQ,EAEvB,OAAOS,EAAMT,IAAQ"}