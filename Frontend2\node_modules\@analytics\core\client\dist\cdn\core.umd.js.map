{"version": 3, "file": "core.umd.js", "sources": ["../../../../analytics-util-types/dist/analytics-util-types.module.js", "../../../../analytics-utils/dist/analytics-utils.module.js", "../../../../analytics-util-storage-global/dist/analytics-util-global-storage.module.js", "../../../src/vendor/redux/utils/defs.js", "../../../src/vendor/redux/createStore.js", "../../../src/vendor/redux/combineReducers.js", "../../../src/vendor/redux/compose.js", "../../../src/vendor/redux/applyMiddleware.js", "../../../src/constants.js", "../../../src/utils/internalConstants.js", "../../../src/events.js", "../../../src/middleware/initialize.js", "../../../src/modules/user.js", "../../../src/middleware/identify.js", "../../../src/utils/callback-stack.js", "../../../src/utils/waitForReady.js", "../../../src/utils/heartbeat.js", "../../../src/middleware/plugins/engine.js", "../../../src/middleware/plugins/index.js", "../../../src/utils/filterDisabled.js", "../../../src/middleware/storage.js", "../../../src/middleware/dynamic.js", "../../../src/modules/plugins.js", "../../../src/utils/serialize.js", "../../../src/modules/track.js", "../../../src/modules/queue.js", "../../../src/modules/page.js", "../../../src/modules/context.js", "../../../src/utils/getOSName/browser.js", "../../../src/utils/handleNetworkEvents.js", "../../../src/utils/debug.js", "../../../src/utils/ensureArray.js", "../../../src/utils/enrichMeta.js", "../../../src/utils/getCallback.js", "../../../src/utils/timestamp.js", "../../../src/index.js"], "sourcesContent": ["var n=\"function\",t=\"string\",e=\"undefined\",r=\"boolean\",o=\"object\",u=\"array\",i=\"number\",c=\"symbol\",a=\"null\",f=\"error\",s=\"typeError\",l=\"syntaxError\",d=\"asyncFunction\",p=\"generatorFunction\",y=\"asyncGeneratorFunction\",g=function(){},b=\"any\",m=\"*\",v=\"none\",h=\"hidden\",j=\"__\",O=\"form\",S=\"input\",A=\"button\",E=\"select\",N=\"change\",w=\"submit\",D=/^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/,z=/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z=/^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/,F=\"undefined\"!=typeof process?process:{},P=F.env&&F.env.NODE_ENV||\"\",x=\"production\"===P,C=\"staging\"===P,L=\"development\"===P,$=\"undefined\"!=typeof document,T=$&&\"localhost\"===window.location.hostname,_=null!=F.versions&&null!=F.versions.node,k=\"undefined\"!=typeof Deno&&void 0!==Deno.core,B=\"object\"==typeof self&&self.constructor&&\"DedicatedWorkerGlobalScope\"===self.constructor.name,G=$&&\"nodejs\"===window.name||\"undefined\"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes(\"Node.js\")||navigator.userAgent.includes(\"jsdom\"));function M(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=M.bind(null,\"toUpperCase\"),H=M.bind(null,\"toLowerCase\");function J(n){return Y(n)?U(\"null\"):\"object\"==typeof n?yn(n):Object.prototype.toString.call(n).slice(8,-1)}function R(n,t){void 0===t&&(t=!0);var e=J(n);return t?H(e):e}function V(n,t){return typeof t===n}var W=V.bind(null,\"function\"),q=V.bind(null,\"string\"),I=V.bind(null,\"undefined\");function K(n){return!I(n)}var Q=V.bind(null,\"boolean\"),X=V.bind(null,\"symbol\");function Y(n){return null===n}function nn(n){return\"number\"===R(n)&&!isNaN(n)}function tn(n){return!isNaN(parseFloat(n))}function en(n){return!!W(n)&&/^class /.test(Function.prototype.toString.call(n))}function rn(n){return\"array\"===R(n)}function on(n){if(!un(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function un(n){return n&&(\"object\"==typeof n||null!==n)}function cn(n){if(!q(n)||!Z.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0}function an(n){if(Y(n))return!0;switch(typeof n){case\"string\":case\"number\":case\"symbol\":case\"undefined\":case\"boolean\":return!0;default:return!1}}function fn(n,t){return on(n)&&W(n[t])}function sn(n){return!!n&&!!(!I(Promise)&&n instanceof Promise||n.then&&W(n.then))}function ln(n){return un(n)&&W(n.throw)&&W(n.return)&&W(n.next)}function dn(n){return\"generatorFunction\"===R(n)}function pn(n){return\"asyncFunction\"===R(n)}function yn(n){return W(n.constructor)?n.constructor.name:null}function gn(n){return n instanceof Set}function bn(n){return n instanceof Map}function mn(n){return n instanceof RegExp}function vn(n){return!(!n.constructor||!W(n.constructor.isBuffer))&&n.constructor.isBuffer(n)}function hn(n){return n instanceof Error||q(n.message)&&n.constructor&&nn(n.constructor.stackTraceLimit)}function jn(n){return un(n)&&q(n.message)&&q(n.name)}function On(n,t){if(\"object\"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=R(new n(\"\"));if(hn(t))for(;t;){if(R(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var Sn=On.bind(null,TypeError),An=On.bind(null,SyntaxError);function En(n){if(!W(n))return!1;var t=/{(\\r|\\n|\\s)*}/gm,e=g+\"\";return e===(n.toString().match(t)||[\"\"])[0].replace(t,e)}function Nn(n){try{if(nn(n.length)&&W(n.callee))return!0}catch(n){if(-1!==n.message.indexOf(\"callee\"))return!0}return!1}function wn(n){return!(q(n)&&\"false\"===n.toLowerCase()||!n)}function Dn(n){return!n}function zn(n){return!0===n}function Zn(n){return!1===n}function Fn(n){return!(n.length>320)&&z.test(n)}function Pn(n){return n instanceof Date||W(n.toDateString)&&W(n.getDate)&&W(n.setDate)}function xn(n){return D.test(n)}function Cn(n){return!(!Y(n)&&(rn(n)?n.length:gn(n)||bn(n)?n.size:on(n)&&Object.keys(n).length))}function Ln(n){return NodeList.prototype.isPrototypeOf(n)}function $n(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?Tn(n,t):e}function Tn(n,t){return void 0===t&&(t=\"\"),n&&n.nodeName===t.toUpperCase()}function _n(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var kn=_n($n,\"form\"),Bn=_n($n,\"button\"),Gn=_n($n,\"input\"),Mn=_n($n,\"select\");function Un(n,t){if(!n||\"hidden\"===getComputedStyle(n).visibility)return!0;for(;n;){if(null!=t&&n===t)return!1;if(\"none\"===getComputedStyle(n).display)return!0;n=n.parentElement}return!1}function Hn(n){return n?rn(n)?n:[n]:[]}export{m as ALL,b as ANY,u as ARRAY,d as ASYNC_FUNCTION,y as ASYNC_GENERATOR_FUNCTION,r as BOOLEAN,A as BUTTON,N as CHANGE,P as ENV,f as ERROR,O as FORM,n as FUNCTION,p as GENERATOR_FUNCTION,h as HIDDEN,S as INPUT,v as NONE,a as NULL,i as NUMBER,o as OBJECT,j as PREFIX,z as REGEX_EMAIL,D as REGEX_ISO,Z as REGEX_JSON,E as SELECT,t as STRING,w as SUBMIT,c as SYMBOL,l as SYNTAX_ERROR,s as TYPE_ERROR,e as UNDEFINED,yn as ctorName,Hn as ensureArray,R as getType,J as getTypeName,Nn as isArguments,rn as isArray,pn as isAsyncFunction,Q as isBoolean,$ as isBrowser,vn as isBuffer,Bn as isButton,en as isClass,Pn as isDate,K as isDefined,k as isDeno,L as isDev,$n as isElement,Fn as isEmail,Cn as isEmpty,hn as isError,jn as isErrorLike,Zn as isFalse,Dn as isFalsy,kn as isForm,W as isFunction,ln as isGenerator,dn as isGeneratorFunction,Un as isHidden,Gn as isInput,xn as isIsoDate,G as isJsDom,cn as isJson,T as isLocalHost,bn as isMap,fn as isMethod,En as isNoOp,_ as isNode,Ln as isNodeList,Tn as isNodeType,Y as isNull,nn as isNumber,tn as isNumberLike,on as isObject,un as isObjectLike,an as isPrimitive,x as isProd,sn as isPromise,mn as isRegex,Mn as isSelect,gn as isSet,C as isStaging,q as isString,X as isSymbol,An as isSyntaxError,zn as isTrue,wn as isTruthy,Sn as isTypeError,I as isUndefined,B as isWebWorker,g as noOp};\n//# sourceMappingURL=analytics-util-types.module.js.map\n", "export{default as dot<PERSON>rop}from\"dlv\";import{isBrowser as e,isString as r,isRegex as t}from\"@analytics/type-utils\";function n(e){try{return decodeURIComponent(e.replace(/\\+/g,\" \"))}catch(e){return null}}function o(){if(e){var r=navigator,t=r.languages;return r.userLanguage||(t&&t.length?t[0]:r.language)}}function a(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){}}function i(r){if(!e)return!1;var t=r||document.referrer;if(t){var n=window.document.location.port,o=t.split(\"/\")[2];return n&&(o=o.replace(\":\"+n,\"\")),o!==window.location.hostname}return!1}function u(n){if(!e)return!0;var o=document.getElementsByTagName(\"script\");return!!Object.keys(o).filter(function(e){var a=o[e].src;return r(n)?-1!==a.indexOf(n):!!t(n)&&a.match(n)}).length}function c(e,r){var t=(e.split(\"?\")||[,])[1];if(!t||-1===t.indexOf(r))return e;var n=new RegExp(\"(\\\\&|\\\\?)\"+r+'([_A-Za-z0-9\"+=.\\\\/\\\\-@%]+)',\"g\"),o=(\"?\"+t).replace(n,\"\").replace(/^&/,\"?\");return e.replace(\"?\"+t,o)}function l(e,r){return n((RegExp(e+\"=(.+?)(&|$)\").exec(r)||[,\"\"])[1])}function s(r){return function(e){for(var r,t=Object.create(null),o=/([^&=]+)=?([^&]*)/g;r=o.exec(e);){var a=n(r[1]),i=n(r[2]);if(a)if(\"[]\"===a.substring(a.length-2)){var u=t[a=a.substring(0,a.length-2)]||(t[a]=[]);t[a]=Array.isArray(u)?u:[],t[a].push(i)}else t[a]=\"\"===i||i}for(var c in t){var l=c.split(\"[\");l.length>1&&(m(t,l.map(function(e){return e.replace(/[?[\\]\\\\ ]/g,\"\")}),t[c]),delete t[c])}return t}(function(r){if(r){var t=r.match(/\\?(.*)/);return t&&t[1]?t[1].split(\"#\")[0]:\"\"}return e&&window.location.search.substring(1)}(r))}function m(e,r,t){for(var n=r.length-1,o=0;o<n;++o){var a=r[o];if(\"__proto__\"===a||\"constructor\"===a)break;a in e||(e[a]={}),e=e[a]}e[r[n]]=t}function f(r,t){return e?new Promise(function(e,n){if(window.history&&window.history.replaceState){var o=window.location.href,a=c(o,r);o!==a&&history.replaceState({},\"\",a)}return t&&t(),e()}):Promise.resolve()}function g(r){if(!e)return null;var t=document.createElement(\"a\");return t.setAttribute(\"href\",r),t.hostname}function p(e){return(g(e)||\"\").split(\".\").slice(-2).join(\".\")}function x(e){var r=e.split(\".\");return r.length>1?r.slice(0,-1).join(\".\"):e}var d={trimTld:x,getDomainBase:p,getDomainHost:g};function v(r,t){if(!e)return!1;var n={source:\"(direct)\",medium:\"(none)\",campaign:\"(not set)\"};r&&i(r)&&(n.referrer=r);var o=function(r){if(!r||!e)return!1;var t=p(r),n=document.createElement(\"a\");if(n.href=r,n.hostname.indexOf(\"google\")>-1&&(t=\"google\"),w[t]){var o=w[t],a=new RegExp((\"string\"==typeof o?o:o.p)+\"=.*?([^&#]*|$)\",\"gi\"),u=n.search.match(a);return{source:o.n||x(t),medium:\"organic\",term:(u?u[0].split(\"=\")[1]:\"\")||\"(not provided)\"}}var c=i(r)?\"referral\":\"internal\";return{source:n.hostname,medium:c}}(r);o&&Object.keys(o).length&&(n=Object.assign({},n,o));var a=s(t),u=Object.keys(a);if(!u.length)return n;var c=u.reduce(function(e,r){return r.match(/^utm_/)&&(e[\"\"+r.replace(/^utm_/,\"\")]=a[r]),r.match(/^(d|g)clid/)&&(e.source=\"google\",e.medium=a.gclid?\"cpc\":\"cpm\",e[r]=a[r]),e},{});return Object.assign({},n,c)}var h=\"q\",w={\"daum.net\":h,\"eniro.se\":\"search_word\",\"naver.com\":\"query\",\"yahoo.com\":\"p\",\"msn.com\":h,\"aol.com\":h,\"ask.com\":h,\"baidu.com\":\"wd\",\"yandex.com\":\"text\",\"rambler.ru\":\"words\",google:h,\"bing.com\":{p:h,n:\"live\"}};function y(){for(var e=\"\",r=0,t=4294967295*Math.random()|0;r++<36;){var n=\"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\"[r-1],o=15&t;e+=\"-\"==n||\"4\"==n?n:(\"x\"==n?o:3&o|8).toString(16),t=r%8==0?4294967295*Math.random()|0:t>>4}return e}function b(e,r){var t,n,o,a=null,i=0,u=function(){i=new Date,a=null,o=e.apply(t,n)};return function(){var c=new Date;i||(i=c);var l=r-(c-i);return t=this,n=arguments,l<=0?(clearTimeout(a),a=null,i=c,o=e.apply(t,n)):a||(a=setTimeout(u,l)),o}}export{n as decodeUri,o as getBrowserLocale,a as getTimeZone,i as isExternalReferrer,u as isScriptLoaded,c as paramsClean,l as paramsGet,s as paramsParse,f as paramsRemove,v as parseReferrer,b as throttle,d as url,y as uuid};\n//# sourceMappingURL=analytics-utils.module.js.map\n", "import{PREFIX as t,OBJECT as e,UNDEFINED as r}from\"@analytics/type-utils\";var l=\"global\",o=t+\"global\"+t,n=typeof self===e&&self.self===self&&self||typeof global===e&&global.global===global&&global||void 0;function a(t){return n[o][t]}function f(t,e){return n[o][t]=e}function i(t){delete n[o][t]}function u(t,e,r){var l;try{if(b(t)){var o=window[t];l=o[e].bind(o)}}catch(t){}return l||r}n[o]||(n[o]={});var c={};function b(t){if(typeof c[t]!==r)return c[t];try{var e=window[t];e.setItem(r,r),e.removeItem(r)}catch(e){return c[t]=!1}return c[t]=!0}export{l as GLOBAL,o as KEY,a as get,n as globalContext,b as hasSupport,i as remove,f as set,u as wrap};\n//# sourceMappingURL=analytics-util-global-storage.module.js.map\n", "export const FUNC = 'function'\nexport const UNDEF = 'undefined'\nexport const REDUCER = 'reducer'\n\nconst base = '@@redux/'\nexport const ACTION_INIT = base + 'INIT'\nexport const ACTION_TEST = base + Math.random().toString(36)\n", "import { isObject } from '@analytics/type-utils'\nimport { FUNC, UNDEF, ACTION_INIT, REDUCER } from './utils/defs'\n\n// eslint-disable-next-line\nconst $$observable = /* #__PURE__ */ (() => (typeof Symbol === FUNC && Symbol.observable) || '@@observable')();\n\n/*\n * Creates a Redux store that holds the state tree.\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nconst msg = ' != ' + FUNC\nexport default function createStore(reducer, preloadedState, enhancer) {\n  if (typeof preloadedState === FUNC && typeof enhancer === UNDEF) {\n    enhancer = preloadedState\n    preloadedState = undefined\n  }\n\n  if (typeof enhancer !== UNDEF) {\n    if (typeof enhancer !== FUNC) {\n      throw new Error('enhancer' + msg)\n    }\n\n    return enhancer(createStore)(reducer, preloadedState)\n  }\n\n  if (typeof reducer !== FUNC) {\n    throw new Error(REDUCER + msg)\n  }\n\n  let currentReducer = reducer\n  let currentState = preloadedState\n  let currentListeners = []\n  let nextListeners = currentListeners\n  let isDispatching = false\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice()\n    }\n  }\n\n  /*\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n  function getState() {\n    return currentState\n  }\n\n  /*\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n  function subscribe(listener) {\n    if (typeof listener !== FUNC) {\n      throw new Error('Listener' + msg)\n    }\n\n    let isSubscribed = true\n\n    ensureCanMutateNextListeners()\n    nextListeners.push(listener)\n\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return\n      }\n\n      isSubscribed = false\n\n      ensureCanMutateNextListeners()\n      const index = nextListeners.indexOf(listener)\n      nextListeners.splice(index, 1)\n    }\n  }\n\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n  function dispatch(action) {\n    /* // add default info to actions... \n    console.log('dispatch before', _action)\n    const action = {\n      ..._action,\n      ...{\n        action: {\n          ..._action.action,\n          ...{ customInfo: 'yoooo'} \n        }\n      }\n    }\n    console.log('dispatch after', action)\n    /** */\n\n    if (!isObject(action)) {\n      throw new Error('Act != obj')\n    }\n\n    if (typeof action.type === UNDEF) {\n      throw new Error('ActType ' + UNDEF)\n    }\n\n    if (isDispatching) {\n      throw new Error('Dispatch in ' + REDUCER)\n    }\n\n    try {\n      isDispatching = true\n      currentState = currentReducer(currentState, action)\n    } finally {\n      isDispatching = false\n    }\n\n    const listeners = currentListeners = nextListeners\n    for (let i = 0; i < listeners.length; i++) {\n      const listener = listeners[i]\n      listener()\n    }\n\n    return action\n  }\n\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== FUNC) {\n      throw new Error('next ' + REDUCER + msg)\n    }\n\n    currentReducer = nextReducer\n    dispatch({ type: ACTION_INIT })\n  }\n\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n  function observable() {\n    const outerSubscribe = subscribe\n    return {\n      /*\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer) {\n        if (typeof observer !== 'object') {\n          throw new TypeError('Observer != obj')\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState())\n          }\n        }\n\n        observeState()\n        const unsubscribe = outerSubscribe(observeState)\n        return { unsubscribe }\n      },\n\n      [$$observable]() {\n        return this\n      }\n    }\n  }\n\n  // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n  dispatch({ type: ACTION_INIT })\n\n  return {\n    dispatch,\n    subscribe,\n    getState,\n    replaceReducer,\n    [$$observable]: observable\n  }\n}\n", "import { isObject } from '@analytics/type-utils'\nimport warning from './utils/warning'\nimport { FUNC, UNDEF, REDUCER, ACTION_INIT, ACTION_TEST } from './utils/defs'\n\nfunction getUndefinedStateErrorMessage(key, action) {\n  const actionType = action && action.type\n  const actionName = (actionType && actionType.toString()) || '?'\n\n  return ('action ' + actionName + REDUCER + ' ' + key + ' returns ' + UNDEF)\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  const reducerKeys = Object.keys(reducers)\n  const argumentName = action && action.type === ACTION_INIT ? 'preloadedState arg passed to createStore' : 'previous state received by ' + REDUCER\n\n  if (reducerKeys.length === 0) {\n    return ('Store has no valid reducers')\n  }\n\n  if (!isObject(inputState)) {\n    return (\n      `The ${argumentName} has unexpected type of \"` +\n      ({}).toString.call(inputState).match(/\\s([a-z|A-Z]+)/)[1] +\n      `\". Expected argument to be an object with the following ` +\n      `keys: \"${reducerKeys.join('\", \"')}\"`\n    )\n  }\n\n  const unexpectedKeys = Object.keys(inputState).filter(key =>\n    !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]\n  )\n\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true\n  })\n\n  if (unexpectedKeys.length > 0) {\n    return (\n      `Unexpected keys ${unexpectedKeys.join('\", \"')} in ${argumentName}. ` +\n      `Expected to find 1 of the known reducer keys instead: ${reducerKeys.join('\", \"')}`\n    )\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key]\n    const initialState = reducer(undefined, { type: ACTION_INIT })\n    if (\n      typeof initialState === UNDEF ||\n      typeof reducer(undefined, { type: ACTION_TEST }) === UNDEF\n    ) {\n      throw new Error(REDUCER + ' ' + key + ' ' + UNDEF)\n    }\n  })\n}\n\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\nexport default function combineReducers(reducers) {\n  const reducerKeys = Object.keys(reducers)\n  const finalReducers = {}\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i]\n\n    if (NODE_ENV !== 'production') {\n      if (typeof reducers[key] === UNDEF) {\n        warning(`No reducer > ${key}`)\n      }\n    }\n\n    if (typeof reducers[key] === FUNC) {\n      finalReducers[key] = reducers[key]\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers)\n\n  let unexpectedKeyCache\n  if (NODE_ENV !== 'production') {\n    unexpectedKeyCache = {}\n  }\n\n  let shapeAssertionError\n  try {\n    assertReducerShape(finalReducers)\n  } catch (e) {\n    shapeAssertionError = e\n  }\n\n  return function combination(state = {}, action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError\n    }\n\n    if (NODE_ENV !== 'production') {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache)\n      if (warningMessage) {\n        warning(warningMessage)\n      }\n    }\n\n    let hasChanged = false\n    const nextState = {}\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i]\n      const reducer = finalReducers[key]\n      const previousStateForKey = state[key]\n      const nextStateForKey = reducer(previousStateForKey, action)\n      if (typeof nextStateForKey === UNDEF) {\n        const errorMessage = getUndefinedStateErrorMessage(key, action)\n        throw new Error(errorMessage)\n      }\n      nextState[key] = nextStateForKey\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey\n    }\n    return hasChanged ? nextState : state\n  }\n}\n", "/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\n\nexport default function compose(...funcs) {\n  if (funcs.length === 0) {\n    return arg => arg\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0]\n  }\n\n  return funcs.reduce((a, b) => (...args) => a(b(...args)))\n}\n", "import compose from './compose'\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\nexport default function applyMiddleware(...middlewares) {\n  return (createStore) => (reducer, preloadedState, enhancer) => {\n    const store = createStore(reducer, preloadedState, enhancer)\n    let dispatch = store.dispatch\n    let chain = []\n\n    const middlewareAPI = {\n      getState: store.getState,\n      dispatch: (action) => dispatch(action)\n    }\n    chain = middlewares.map(middleware => middleware(middlewareAPI))\n    dispatch = compose(...chain)(store.dispatch)\n\n    return {\n      ...store,\n      dispatch\n    }\n  }\n}\n", "/**\n * Core Analytic constants. These are exposed for third party plugins & listeners\n * @typedef {Object} constants\n * @property {ANON_ID} ANON_ID - Anonymous visitor Id localstorage key\n * @property {USER_ID} USER_ID - Visitor Id localstorage key\n * @property {USER_TRAITS} USER_TRAITS - Visitor traits localstorage key\n */\nimport { PREFIX } from '@analytics/type-utils'\n\n\n/**\n * Anonymous visitor Id localstorage key\n * @typedef {String} ANON_ID\n */\nexport const ANON_ID = PREFIX + 'anon_id' // __anon_id\n/**\n * Visitor Id localstorage key\n * @typedef {String} USER_ID\n */\nexport const USER_ID = PREFIX + 'user_id' // __user_id\n/**\n * Visitor traits localstorage key\n * @typedef {String} USER_TRAITS\n */\nexport const USER_TRAITS = PREFIX + 'user_traits' // __user_traits\n", "\nexport const LIB_NAME = 'analytics'\n\nexport const ID = 'userId'\n\nexport const ANONID = 'anonymousId'\n\nexport const ERROR_URL = 'https://lytics.dev/errors/'", "/* Core Analytic Events */\n\nexport const coreEvents = [\n  /**\n   * `bootstrap` - Fires when analytics library starts up.\n   * This is the first event fired. '.on/once' listeners are not allowed on bootstrap\n   * Plugins can attach logic to this event\n   */\n  'bootstrap',\n  /**\n   * `params` - Fires when analytics parses URL parameters\n   */\n  'params',\n  /**\n   * `campaign` - Fires if params contain \"utm\" parameters\n   */\n  'campaign',\n  /**\n   * `initializeStart` - Fires before 'initialize', allows for plugins to cancel loading of other plugins\n   */\n  'initializeStart',\n  /**\n   * `initialize` - Fires when analytics loads plugins\n   */\n  'initialize',\n  /**\n   * `initializeEnd` - Fires after initialize, allows for plugins to run logic after initialization methods run\n   */\n  'initializeEnd',\n  /**\n   * `ready` - Fires when all analytic providers are fully loaded. This waits for 'initialize' and 'loaded' to return true\n   */\n  'ready',\n  /**\n   * `resetStart` - Fires if analytic.reset() is called.\n   * Use this event to cancel reset based on a specific condition\n   */\n  'resetStart',\n  /**\n   * `reset` - Fires if analytic.reset() is called.\n   * Use this event to run custom cleanup logic (if needed)\n   */\n  'reset',\n  /**\n   * `resetEnd` - Fires after analytic.reset() is called.\n   * Use this event to run a callback after user data is reset\n   */\n  'resetEnd',\n  /******************\n   * Page Events\n   ******************/\n  /**\n   * `pageStart` - Fires before 'page' events fire.\n   *  This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'pageStart',\n  /**\n   * `page` - Core analytics hook for page views.\n   *  If your plugin or integration tracks page views, this is the event to fire on.\n   */\n  'page',\n  /**\n   * `pageEnd` - Fires after all registered 'page' methods fire.\n   */\n  'pageEnd',\n  /**\n   * `pageAborted` - Fires if 'page' call is cancelled by a plugin\n   */\n  'pageAborted',\n  /****************\n   * Track Events\n   ***************/\n  /**\n   * `trackStart` - Called before the 'track' events fires.\n   *  This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'trackStart',\n  /**\n   * `track` - Core analytics hook for event tracking.\n   *  If your plugin or integration tracks custom events, this is the event to fire on.\n   */\n  'track',\n  /**\n   * `trackEnd` - Fires after all registered 'track' events fire from plugins.\n   */\n  'trackEnd',\n  /**\n   * `trackAborted` - Fires if 'track' call is cancelled by a plugin\n   */\n  'trackAborted',\n  /******************\n   * Identify Events\n   ******************/\n  /**\n   * `identifyStart` - Called before the 'identify' events fires.\n   * This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'identifyStart',\n  /**\n   * `identify` - Core analytics hook for user identification.\n   *  If your plugin or integration identifies users or user traits, this is the event to fire on.\n   */\n  'identify',\n  /**\n   * `identifyEnd` - Fires after all registered 'identify' events fire from plugins.\n   */\n  'identifyEnd',\n  /**\n   * `identifyAborted` - Fires if 'track' call is cancelled by a plugin\n   */\n  'identifyAborted',\n  /**\n   * `userIdChanged` - Fires when a user id is updated\n   */\n  'userIdChanged',\n  /******************\n   * Plugin Events\n   ******************/\n  /**\n   * `registerPlugins` - Fires when analytics is registering plugins\n   */\n  'registerPlugins',\n  /**\n   * `enablePlugin` - Fires when 'analytics.plugins.enable()' is called\n   */\n  'enablePlugin',\n  /**\n   * `disablePlugin` - Fires when 'analytics.plugins.disable()' is called\n   */\n  'disablePlugin',\n  /*\n   * `loadPlugin` - Fires when 'analytics.loadPlugin()' is called\n   */\n  // 'loadPlugin',\n  /******************\n   * Browser activity events\n   ******************/\n  /**\n   * `online` - Fires when browser network goes online.\n   * This fires only when coming back online from an offline state.\n   */\n  'online',\n  /**\n   * `offline` - Fires when browser network goes offline.\n   */\n  'offline',\n  /******************\n   * Storage events\n   ******************/\n  /**\n   * `setItemStart` - Fires when analytics.storage.setItem is initialized.\n   * This event gives plugins the ability to intercept keys & values and alter them before they are persisted.\n   */\n  'setItemStart',\n  /**\n   * `setItem` - Fires when analytics.storage.setItem is called.\n   * This event gives plugins the ability to intercept keys & values and alter them before they are persisted.\n   */\n  'setItem',\n  /**\n   * `setItemEnd` - Fires when setItem storage is complete.\n   */\n  'setItemEnd',\n  /**\n   * `setItemAborted` - Fires when setItem storage is cancelled by a plugin.\n   */\n  'setItemAborted',\n  /**\n   * `removeItemStart` - Fires when analytics.storage.removeItem is initialized.\n   * This event gives plugins the ability to intercept removeItem calls and abort / alter them.\n   */\n  'removeItemStart',\n  /**\n   * `removeItem` - Fires when analytics.storage.removeItem is called.\n   * This event gives plugins the ability to intercept removeItem calls and abort / alter them.\n   */\n  'removeItem',\n  /**\n   * `removeItemEnd` - Fires when removeItem storage is complete.\n   */\n  'removeItemEnd',\n  /**\n   * `removeItemAborted` - Fires when removeItem storage is cancelled by a plugin.\n   */\n  'removeItemAborted',\n]\n\n/* Keys on a plugin that are not considered events */\nexport const nonEvents = ['name', 'EVENTS', 'config', 'loaded']\n\nconst pluginEvents = {\n  registerPluginType: (name) => `registerPlugin:${name}`,\n  pluginReadyType: (name) => `ready:${name}`,\n}\n\nconst EVENTS = coreEvents.reduce((acc, curr) => {\n  acc[curr] = curr\n  return acc\n}, pluginEvents)\n\nexport default EVENTS\n\nexport function isReservedAction(type) {\n  return coreEvents.includes(type)\n}\n", "/* eslint-disable camelcase */\nimport EVENTS from '../events'\nimport { ANON_ID, USER_ID, USER_TRAITS } from '../constants'\n\nconst utmRegex = /^utm_/\nconst propRegex = /^an_prop_/\nconst traitRegex = /^an_trait_/\n\n// Middleware runs during EVENTS.initialize\nexport default function initializeMiddleware(instance) {\n  const { setItem } = instance.storage\n  return store => next => action => {\n    /* Handle bootstrap event */\n    if (action.type === EVENTS.bootstrap) {\n      const { params, user, persistedUser, initialUser } = action\n      const isKnownId = persistedUser.userId === user.userId\n      /* 1. Set anonymous ID */\n      if (persistedUser.anonymousId !== user.anonymousId) {\n        setItem(ANON_ID, user.anonymousId)\n      }\n      /* 2. Set userId */\n      if (!isKnownId) {\n        setItem(USER_ID, user.userId)\n      }\n      /* 3. Set traits if they are different */\n      if (initialUser.traits) {\n         setItem(USER_TRAITS, {\n          ...(isKnownId && persistedUser.traits) ? persistedUser.traits : {},\n          ...initialUser.traits\n        })\n        /* TODO multi user setup\n        setItem(`${USER_TRAITS}.${user.userId}`, {\n          ...(isKnownId) ? existingTraits : {},\n          ...initialUser.traits\n        })\n        */\n      }\n      /* 4. Parse url params */\n      const paramsArray = Object.keys(action.params)\n      if (paramsArray.length) {\n        const { an_uid, an_event } = params\n        const groupedParams = paramsArray.reduce((acc, key) => {\n          // match utm params & dclid (display) & gclid (cpc)\n          if (key.match(utmRegex) || key.match(/^(d|g)clid/)) {\n            const cleanName = key.replace(utmRegex, '')\n            const keyName = (cleanName === 'campaign') ? 'name' : cleanName\n            acc.campaign[keyName] = params[key]\n          }\n          if (key.match(propRegex)) {\n            acc.props[key.replace(propRegex, '')] = params[key]\n          }\n          if (key.match(traitRegex)) {\n            acc.traits[key.replace(traitRegex, '')] = params[key]\n          }\n          return acc\n        }, {\n          campaign: {},\n          props: {},\n          traits: {}\n        })\n\n        store.dispatch({\n          type: EVENTS.params,\n          raw: params,\n          ...groupedParams,\n          ...(an_uid ? { userId: an_uid } : {}),\n        })\n\n        /* If userId set, call identify */\n        if (an_uid) {\n          // timeout to debounce and make sure integration is registered. Todo refactor\n          setTimeout(() => instance.identify(an_uid, groupedParams.traits), 0)\n        }\n\n        /* If tracking event set, call track */\n        if (an_event) {\n          // timeout to debounce and make sure integration is registered. Todo refactor\n          setTimeout(() => instance.track(an_event, groupedParams.props), 0)\n        }\n\n        // if url has utm params\n        if (Object.keys(groupedParams.campaign).length) {\n          store.dispatch({\n            type: EVENTS.campaign,\n            campaign: groupedParams.campaign\n          })\n        }\n      }\n    }\n    return next(action)\n  }\n}\n", "import { get } from '@analytics/global-storage-utils'\nimport { isObject, PREFIX } from '@analytics/type-utils'\nimport { ANON_ID, USER_ID, USER_TRAITS } from '../constants'\nimport EVENTS from '../events'\n\n/* user reducer */\nexport default function userReducer(storage) {\n  return function user(state = {}, action = {}) {\n\n    if (action.type === EVENTS.setItemEnd) {\n      // Set anonymousId if changed by storage.setItem\n      if (action.key === ANON_ID) {\n        return { ...state, ...{ anonymousId: action.value }}\n      }\n      // Set userId if changed by storage.setItem\n      if (action.key === USER_ID) {\n        return { ...state, ...{ userId: action.value }}\n      }\n    }\n\n    switch (action.type) {\n      case EVENTS.identify:\n        return Object.assign({}, state, {\n          userId: action.userId,\n          traits: {\n            ...state.traits,\n            ...action.traits\n          }\n        })\n      case EVENTS.reset:\n        // Side effect to fix race condition in Node. TODO refactor\n        // This is from default storage.removeItem: (key) => globalContext[key] = undefined\n        [ USER_ID, ANON_ID, USER_TRAITS ].forEach((key) => {\n          // sync storage, not instance.storage\n          storage.removeItem(key)\n        })\n        return Object.assign({}, state, {\n          userId: null,\n          // TODO reset anon id automatically?\n          anonymousId: null,\n          traits: {},\n        })\n      default:\n        return state\n    }\n  }\n}\n\nexport function getPersistedUserData(storage) {\n  return {\n    userId: storage.getItem(USER_ID),\n    anonymousId: storage.getItem(ANON_ID),\n    traits: storage.getItem(USER_TRAITS)\n  }\n}\n\nexport const tempKey = (key) => PREFIX + 'TEMP' + PREFIX + key\n\nexport function getUserPropFunc(storage) {\n  return function getUserProp(key, instance, payload) {\n    /* 1. Try current state */\n    const currentId = instance.getState('user')[key]\n    if (currentId) {\n      /*\n      console.log(`from state ${key}`, currentId)\n      /** */\n      return currentId\n    }\n\n    /* 2. Try event payload */\n    if (payload && isObject(payload) && payload[key]) {\n      /*\n      console.log(`from payload ${key}`, payload[key])\n      /** */\n      return payload[key]\n    }\n\n    /* 3. Try persisted data */\n    const persistedInfo = getPersistedUserData(storage)[key]\n    if (persistedInfo) {\n      /*\n      console.log(`from persistedInfo ${key}`, persistedInfo)\n      /** */\n      return persistedInfo\n    }\n\n    /* 4. Else, try to get in memory placeholder. TODO watch this for future issues */\n    return get(tempKey(key)) || null\n  }\n}\n", "import { uuid } from 'analytics-utils'\nimport { remove } from '@analytics/global-storage-utils'\nimport { tempKey } from '../modules/user'\nimport { USER_ID, USER_TRAITS, ANON_ID } from '../constants'\nimport { ID, ANONID } from '../utils/internalConstants'\nimport EVENTS from '../events'\n\nexport default function identifyMiddleware(instance) {\n  const { setItem, removeItem, getItem } = instance.storage\n  return store => next => action => {\n    const { userId, traits, options } = action\n    /* Reset user id and traits */\n    if (action.type === EVENTS.reset) {\n      // Remove stored data\n      [ USER_ID, USER_TRAITS, ANON_ID ].forEach((key) => {\n        // Fires async removeItem dispatch\n        removeItem(key)\n      });\n      [ ID, ANONID, 'traits' ].forEach((key) => {\n        // Remove from global context\n        remove(tempKey(key))\n      })\n    }\n\n    if (action.type === EVENTS.identify) {\n      /* If no anon id. Set it! */\n      if (!getItem(ANON_ID)) {\n        setItem(ANON_ID, uuid())\n      }\n\n      const currentId = getItem(USER_ID)\n      const currentTraits = getItem(USER_TRAITS) || {}\n\n      if (currentId && (currentId !== userId)) {\n        store.dispatch({\n          type: EVENTS.userIdChanged,\n          old: {\n            userId: currentId,\n            traits: currentTraits,\n          },\n          new: {\n            userId,\n            traits\n          },\n          options: options,\n        })\n      }\n\n      /* Save user id */\n      if (userId) {\n        setItem(USER_ID, userId)\n      }\n\n      /* Save user traits */\n      if (traits) {\n        setItem(USER_TRAITS, {\n          ...currentTraits,\n          ...traits\n        })\n      }\n    }\n    return next(action)\n  }\n}\n", "import { isFunction } from '@analytics/type-utils'\n\n// Stack to temporarily hold deferred promises/callbacks\nconst stack = {}\n\nfunction runCallback(id, payload) {\n  if (stack[id] && isFunction(stack[id])) {\n    // console.log(`run ${id}`)\n    stack[id](payload)\n    delete stack[id]\n  }\n}\n\nexport { stack, runCallback }", "/**\n * Wait until a given analytics provider is ready.\n * @param  {Object} data - passthrough resolve data\n * @param  {Function} predicate - function that resolves true\n * @param  {Number} timeout - max wait time\n * @return {Promise}\n */\nexport default function waitForReady(data, predicate, timeout) {\n  return new Promise((resolve, reject) => {\n    if (predicate()) {\n      return resolve(data)\n    }\n    // Timeout. Add to queue\n    if (timeout < 1) {\n      return reject({ ...data, queue: true }) // eslint-disable-line\n    }\n    // Else recursive retry\n    return pause(10).then(_ => {\n      return waitForReady(data, predicate, timeout - 10).then(resolve, reject)\n    })\n  })\n}\n\nfunction pause(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n", "import { isFunction, isObject } from '@analytics/type-utils'\nimport { ID, ANONID } from './internalConstants'\n\nfunction abort(reason) {\n  return { abort: reason }\n}\n\nexport function processQueue(store, getPlugins, instance) {\n  const abortedCalls = {}\n  const pluginMethods = getPlugins()\n  const { plugins, context, queue, user } = store.getState()\n  const isOnline = !context.offline\n  /* If network connection found and there is items in queue, process them all */\n  if (isOnline && queue && queue.actions && queue.actions.length) {\n    const pipeline = queue.actions.reduce((acc, item, index) => {\n      const isLoaded = plugins[item.plugin].loaded\n      if (isLoaded) {\n        acc.process.push(item)\n        acc.processIndex.push(index)\n      } else {\n        acc.requeue.push(item)\n        acc.requeueIndex.push(index)\n      }\n      return acc\n    }, {\n      processIndex: [],\n      process: [],\n      requeue: [],\n      requeueIndex: []\n    })\n\n    if (pipeline.processIndex && pipeline.processIndex.length) {\n      pipeline.processIndex.forEach((i) => {\n        const processAction = queue.actions[i]\n        // console.log('RePROCESS THIS>', processAction)\n        // Call methods directly right now\n        const currentPlugin = processAction.plugin\n        const currentMethod = processAction.payload.type\n        const method = pluginMethods[currentPlugin][currentMethod]\n        if (method && isFunction(method)) {\n          /* enrich queued payload with userId / anon id if missing */\n          /* TODO hoist enrich into where action queued? */\n          // console.log('before', processAction.payload)\n          const enrichedPayload = enrich(processAction.payload, user)\n          // console.log('user.userId', user.userId)\n          // console.log('user.anonymousId', user.anonymousId)\n          // console.log('after enrich', enrichedPayload)\n          let retVal\n          const isAborted = abortedCalls[enrichedPayload.meta.rid]\n          /* if not aborted call method */\n          if (!isAborted) {\n            // TODO make async\n            retVal = method({\n              payload: enrichedPayload,\n              config: plugins[currentPlugin].config,\n              instance,\n              abort\n            })\n            // If aborted, cancel the downstream calls\n            if (retVal && isObject(retVal) && retVal.abort) {\n              abortedCalls[enrichedPayload.meta.rid] = true\n              return\n            }\n          }\n\n          /* Then redispatch for .on listeners / other middleware */\n          if (!isAborted) {\n            const pluginEvent = `${currentMethod}:${currentPlugin}`\n            store.dispatch({\n              ...enrichedPayload,\n              type: pluginEvent,\n              /* Internal data for analytics engine */\n              _: {\n                called: pluginEvent,\n                from: 'queueDrain'\n              }\n            })\n          }\n        }\n      })\n\n      /* Removed processed actions */\n      const reQueueActions = queue.actions.filter((value, index) => {\n        // note !~ === return pipeline.processIndex.indexOf(index) === -1\n        return !~pipeline.processIndex.indexOf(index)\n      })\n\n      /* Set queue actions. TODO refactor to non mutatable or move out of redux */\n      queue.actions = reQueueActions\n\n      /*\n      if (!reQueueActions.length) {\n        console.log('Queue clears')\n        console.log('abortedCalls', abortedCalls)\n      }\n      /** */\n    }\n  }\n}\n\n/* Heartbeat retries queued events */\nexport default function heartBeat(store, getPlugins, instance) {\n  // 3e3 === 3000 ms\n  return setInterval(() => processQueue(store, getPlugins, instance), 3e3)\n}\n\n// Assign userId && anonymousId values if present in payload but null\nfunction enrich(payload = {}, user = {}) {\n  return [ ID, ANONID ].reduce((acc, key) => {\n    if (payload.hasOwnProperty(key) && user[key] && (user[key] !== payload[key])) {\n      // console.log(`${key} stale update with ${user[key]}`)\n      acc[key] = user[key]\n    }\n    return acc\n  }, payload)\n}\n", "import EVENTS from '../../events'\nimport fitlerDisabledPlugins from '../../utils/filterDisabled'\nimport { isFunction, isObject, isString } from '@analytics/type-utils'\nimport { runCallback } from '../../utils/callback-stack'\n\nconst endsWithStartRegex = /Start$/\nconst bootstrapRegex = /^bootstrap/\nconst readyRegex = /^ready/\n\nexport default async function (action, getPlugins, instance, store, eventsInfo) {\n  const pluginObject = isFunction(getPlugins) ? getPlugins() : getPlugins\n  const originalType = action.type\n  const updatedType = originalType.replace(endsWithStartRegex, '')\n\n  /* If action already dispatched exit early. This makes it so plugin methods are not fired twice. */\n  if (action._ && action._.called) {\n    // console.log('Already called', action._.called)\n    return action\n  }\n\n  const state = instance.getState()\n  /* Remove plugins that are disabled by options or by settings */\n  let activePlugins = fitlerDisabledPlugins(pluginObject, state.plugins, action.options)\n\n  /* If analytics.plugin.enable calls do special behavior */\n  if (originalType === EVENTS.initializeStart && action.fromEnable) {\n    // Return list of all enabled plugins that have NOT been initialized yet\n    activePlugins = Object.keys(state.plugins).filter((name) => {\n      const info = state.plugins[name]\n      return action.plugins.includes(name) && !info.initialized\n    }).map((name) => pluginObject[name])\n  }\n  // console.log(`engine activePlugins ${action.type}`, activePlugins)\n\n  const allActivePluginKeys = activePlugins.map((p) => p.name)\n  // console.log('allActivePluginKeys', allActivePluginKeys)\n  const allMatches = getAllMatchingCalls(originalType, activePlugins, pluginObject)\n  // console.log('allMatches', allMatches)\n\n  /* @TODO cache matches and purge on enable/disable/add plugin */\n\n  /**\n   * Process all 'actionBefore' hooks\n   * Example:\n   *  This is processes 'pageStart' methods from plugins and update the event to send through the chain\n   */\n  const actionBefore = await processEvent({\n    action: action,\n    data: {\n      exact: allMatches.before,\n      namespaced: allMatches.beforeNS\n    },\n    state: state,\n    allPlugins: pluginObject,\n    allMatches,\n    instance,\n    store,\n    EVENTS: eventsInfo\n  })\n  // console.log('____ actionBefore out', actionBefore)\n\n  /* Abort if ‘eventBefore’ returns abort data */\n  if (shouldAbortAll(actionBefore, allActivePluginKeys.length)) {\n    return actionBefore\n  }\n\n  /* Filter over the plugin method calls and remove aborted plugin by name */\n  // const activeAndNonAbortedCalls = activePlugins.filter((plugin) => {\n  //   if (shouldAbort(actionBefore, plugin.name)) return false\n  //   return true\n  // })\n  // console.log(`activeAndNonAbortedCalls ${action.type}`, activeAndNonAbortedCalls)\n\n  let actionDuring\n  if (originalType === updatedType) {\n    /* If type the same don't double process */\n    actionDuring = actionBefore\n  } else {\n    /**\n     * Process all 'action' hooks\n     * Example: This is process 'page' methods from plugins and update the event to send through\n     */\n    actionDuring = await processEvent({\n      action: {\n        ...actionBefore,\n        type: updatedType\n      },\n      data: {\n        exact: allMatches.during,\n        namespaced: allMatches.duringNS\n      },\n      state: state,\n      allPlugins: pluginObject,\n      allMatches,\n      instance,\n      store,\n      EVENTS: eventsInfo\n    })\n  }\n  // console.log('____ actionDuring', actionDuring)\n\n  /**\n   * Process all 'actionEnd' hooks\n   * Example:\n   *  This is process 'pageEnd' methods from plugins and update the event to send through\n   */\n  // Only trigger `eventTypeEnd` if originalAction has Start ending.\n  if (originalType.match(endsWithStartRegex)) {\n    const afterName = `${updatedType}End`\n    const actionAfter = await processEvent({\n      action: {\n        ...actionDuring,\n        type: afterName\n      },\n      data: {\n        exact: allMatches.after,\n        namespaced: allMatches.afterNS\n      },\n      state: state,\n      allPlugins: pluginObject,\n      allMatches,\n      instance,\n      store,\n      EVENTS: eventsInfo\n    })\n    // console.log('____ actionAfter', actionAfter)\n\n    /* Fire callback if supplied */\n    if (actionAfter.meta && actionAfter.meta.hasCallback) {\n      /*\n      console.log('End of engine action has callback')\n      console.log(actionAfter.meta)\n      console.log('stack', stack)\n      /** */\n\n      // @TODO figure out exact args calls and .on will get\n      runCallback(actionAfter.meta.rid, { payload: actionAfter })\n    }\n  }\n\n  return actionBefore\n}\n\n/**\n * Async reduce over matched plugin methods\n * Fires plugin functions\n */\nasync function processEvent({\n  data,\n  action,\n  instance,\n  state,\n  allPlugins,\n  allMatches,\n  store,\n  EVENTS\n}) {\n  const { plugins, context } = state\n  const method = action.type\n  const isStartEvent = method.match(endsWithStartRegex)\n  // console.log(`data ${method}`, data)\n  // console.log(`data allMatches ${method}`, allMatches)\n  let abortable = data.exact.map((x) => {\n    return x.pluginName\n  })\n\n  /* If abort is called from xyzStart */\n  if (isStartEvent) {\n    abortable = allMatches.during.map((x) => {\n      return x.pluginName\n    })\n  }\n\n  /* make args for functions to concume */\n  const makeArgs = argumentFactory(instance, abortable)\n  // console.log('makeArgs', makeArgs)\n\n  /* Check if plugin loaded, if not mark action for queue */\n  const queueData = data.exact.reduce((acc, thing) => {\n    const { pluginName, methodName } = thing\n    let addToQueue = false\n    // Queue actions if plugin not loaded except for initialize and reset\n    if (!methodName.match(/^initialize/) && !methodName.match(/^reset/)) {\n      addToQueue = !plugins[pluginName].loaded\n    }\n    /* If offline and its a core method. Add to queue */\n    if (context.offline && (methodName.match(/^(page|track|identify)/))) {\n      addToQueue = true\n    }\n    acc[`${pluginName}`] = addToQueue\n    return acc\n  }, {})\n\n  /* generate plugin specific payloads */\n  const payloads = await data.exact.reduce(async (scoped, curr, i) => {\n    const { pluginName } = curr\n    const curScope = await scoped\n    if (data.namespaced && data.namespaced[pluginName]) {\n      const scopedPayload = await data.namespaced[pluginName].reduce(async (acc, p, count) => {\n        // await value\n        const curScopeData = await acc\n        if (!p.method || !isFunction(p.method)) {\n          return curScopeData\n        }\n\n        /* Make sure plugins don’t call themselves */\n        validateMethod(p.methodName, p.pluginName)\n\n        function genAbort(currentAct, pname, otherPlug) {\n          return function (reason, plugins) {\n            const callsite = otherPlug || pname\n            // console.log(`__abort msg: ${reason}`)\n            // console.log(`__abort ${pname}`)\n            // console.log(`__abort xxx: ${plugins}`)\n            // console.log(`__abort otherPlug`, otherPlug)\n            return {\n              ...currentAct,\n              abort: {\n                reason: reason,\n                plugins: plugins || [pname],\n                caller: method,\n                from: callsite\n              }\n            }\n          }\n        }\n\n        const val = await p.method({\n          payload: curScopeData,\n          instance,\n          abort: genAbort(curScopeData, pluginName, p.pluginName),\n          config: getConfig(p.pluginName, plugins, allPlugins),\n          plugins: plugins\n        })\n        const returnValue = isObject(val) ? val : {}\n        return Promise.resolve({\n          ...curScopeData,\n          ...returnValue\n        })\n      }, Promise.resolve(action))\n\n      /* Set scoped payload */\n      curScope[pluginName] = scopedPayload\n    } else {\n      /* Set payload as default action */\n      curScope[pluginName] = action\n    }\n    return Promise.resolve(curScope)\n  }, Promise.resolve({}))\n  // console.log(`aaa scoped payloads ${action.type}`, payloads)\n\n  // Then call the normal methods with scoped payload\n  const resolvedAction = await data.exact.reduce(async (promise, curr, i) => {\n    const lastLoop = data.exact.length === (i + 1)\n    const { pluginName } = curr\n    const currentPlugin = allPlugins[pluginName]\n    const currentActionValue = await promise\n\n    let payloadValue = (payloads[pluginName]) ? payloads[pluginName] : {}\n    /* If eventStart, allow for value merging */\n    if (isStartEvent) {\n      payloadValue = currentActionValue\n    }\n\n    if (shouldAbort(payloadValue, pluginName)) {\n      // console.log(`> Abort from payload specific \"${pluginName}\" abort value`, payloadValue)\n      abortDispatch({\n        data: payloadValue,\n        method,\n        instance,\n        pluginName,\n        store\n      })\n      return Promise.resolve(currentActionValue)\n    }\n    if (shouldAbort(currentActionValue, pluginName)) {\n      // console.log(`> Abort from ${method} abort value`, currentActionValue)\n      if (lastLoop) {\n        abortDispatch({\n          data: currentActionValue,\n          method,\n          instance,\n          // pluginName,\n          store\n        })\n      }\n      return Promise.resolve(currentActionValue)\n    }\n\n    if (queueData.hasOwnProperty(pluginName) && queueData[pluginName] === true) {\n      // console.log('Queue this instead', pluginName)\n      store.dispatch({\n        type: `queue`,\n        plugin: pluginName,\n        payload: payloadValue,\n        /* Internal data for analytics engine */\n        _: {\n          called: `queue`,\n          from: 'queueMechanism' // for debugging\n        }\n      })\n      return Promise.resolve(currentActionValue)\n    }\n    /*\n    const checkForLoaded = () => {\n      const p = instance.getState('plugins')\n      return p[currentPlugin.name].loaded\n    }\n    // const p = instance.getState('plugins')\n    console.log(`loaded \"${currentPlugin.name}\" > ${method}:`, p[currentPlugin.name].loaded)\n    // await waitForReady(currentPlugin, checkForLoaded, 10000).then((d) => {\n    //   console.log(`Loaded ${method}`, currentPlugin.name)\n    // }).catch((e) => {\n    //   console.log(`Error ${method} ${currentPlugin.name}`, e)\n    //   // TODO dispatch failure\n    // })\n    */\n\n    // @TODO figure out if we want queuing semantics\n\n    const funcArgs = makeArgs(payloads[pluginName], allPlugins[pluginName])\n\n    // console.log(`funcArgs ${method} ${pluginName}`, funcArgs)\n\n    /* Run the plugin function */\n    const val = await currentPlugin[method]({\n      // currentPlugin: pluginName,\n      abort: funcArgs.abort,\n      // Send in original action value or scope payload\n      payload: payloadValue,\n      instance,\n      config: getConfig(pluginName, plugins, allPlugins),\n      plugins: plugins\n    })\n\n    const returnValue = isObject(val) ? val : {}\n    const merged = {\n      ...currentActionValue,\n      ...returnValue\n    }\n\n    const scopedPayload = payloads[pluginName] // || currentActionValue\n    if (shouldAbort(scopedPayload, pluginName)) {\n      // console.log(`>> HANDLE abort ${method} ${pluginName}`)\n      abortDispatch({\n        data: scopedPayload,\n        method,\n        instance,\n        pluginName,\n        store\n      })\n    } else {\n      const nameSpaceEvent = `${method}:${pluginName}`\n      const actionDepth = (nameSpaceEvent.match(/:/g) || []).length\n      if (actionDepth < 2 && !method.match(bootstrapRegex) && !method.match(readyRegex)) {\n        const updatedPayload = (isStartEvent) ? merged : payloadValue\n        // Dispatched for `.on('xyz') listeners.\n        instance.dispatch({\n          ...updatedPayload,\n          type: nameSpaceEvent,\n          _: {\n            called: nameSpaceEvent,\n            from: 'submethod'\n          }\n        })\n      }\n    }\n    // console.log('merged', merged)\n    return Promise.resolve(merged)\n  }, Promise.resolve(action))\n\n  // Dispatch End. Make sure actions don't get double dispatched. EG userIdChanged\n  if (!method.match(endsWithStartRegex) &&\n      !method.match(/^registerPlugin/) &&\n      // !method.match(/^disablePlugin/) &&\n      // !method.match(/^enablePlugin/) &&\n      !method.match(readyRegex) &&\n      !method.match(bootstrapRegex) &&\n      !method.match(/^params/) &&\n      !method.match(/^userIdChanged/)\n  ) {\n    if (EVENTS.plugins.includes(method)) {\n      // console.log(`Dont dispatch for ${method}`, resolvedAction)\n      // return resolvedAction\n    }\n    /*\n      Verify this original action setup.\n      It's intended to keep actions from double dispatching themselves\n    */\n    if (resolvedAction._ && resolvedAction._.originalAction === method) {\n      // console.log(`Dont dispatch for ${method}`, resolvedAction)\n      return resolvedAction\n    }\n\n    let endAction = {\n      ...resolvedAction,\n      ...{\n        _: {\n          originalAction: resolvedAction.type,\n          called: resolvedAction.type,\n          from: 'engineEnd'\n        }\n      }\n    }\n\n    /* If all plugins are aborted, dispatch xAborted */\n    if (shouldAbortAll(resolvedAction, data.exact.length) && !method.match(/End$/)) {\n      endAction = {\n        ...endAction,\n        ...{\n          type: resolvedAction.type + 'Aborted',\n        }\n      }\n    }\n\n    store.dispatch(endAction)\n  }\n\n  return resolvedAction\n}\n\nfunction abortDispatch({ data, method, instance, pluginName, store }) {\n  const postFix = (pluginName) ? ':' + pluginName : ''\n  const abortEvent = method + 'Aborted' + postFix\n  store.dispatch({\n    ...data,\n    type: abortEvent,\n    _: {\n      called: abortEvent,\n      from: 'abort'\n    }\n  })\n}\n\nfunction getConfig(name, pluginState, allPlugins) {\n  const pluginData = pluginState[name] || allPlugins[name]\n  if (pluginData && pluginData.config) {\n    return pluginData.config\n  }\n  return {}\n}\n\nfunction getPluginFunctions(methodName, plugins) {\n  return plugins.reduce((arr, plugin) => {\n    return (!plugin[methodName]) ? arr : arr.concat({\n      methodName: methodName,\n      pluginName: plugin.name,\n      method: plugin[methodName],\n    })\n  }, [])\n}\n\nfunction formatMethod(type) {\n  return type.replace(endsWithStartRegex, '')\n}\n\n/**\n * Return array of event names\n * @param  {String} eventType - original event type\n * @param  {String} namespace - optional namespace postfix\n * @return {array} - type, method, end\n */\nfunction getEventNames(eventType, namespace) {\n  const method = formatMethod(eventType)\n  const postFix = (namespace) ? `:${namespace}` : ''\n  // `typeStart:pluginName`\n  const type = `${eventType}${postFix}`\n  // `type:pluginName`\n  const methodName = `${method}${postFix}`\n  // `typeEnd:pluginName`\n  const end = `${method}End${postFix}`\n  return [ type, methodName, end ]\n}\n\n/* Collect all calls for a given event in the system */\nfunction getAllMatchingCalls(eventType, activePlugins, allPlugins) {\n  const eventNames = getEventNames(eventType)\n  // console.log('eventNames', eventNames)\n  // 'eventStart', 'event', & `eventEnd`\n  const core = eventNames.map((word) => {\n    return getPluginFunctions(word, activePlugins)\n  })\n  // Gather nameSpaced Events\n  return activePlugins.reduce((acc, plugin) => {\n    const { name } = plugin\n    const nameSpacedEvents = getEventNames(eventType, name)\n    // console.log('eventNames namespaced', nameSpacedEvents)\n    const [ beforeFuncs, duringFuncs, afterFuncs ] = nameSpacedEvents.map((word) => {\n      return getPluginFunctions(word, activePlugins)\n    })\n\n    if (beforeFuncs.length) {\n      acc.beforeNS[name] = beforeFuncs\n    }\n    if (duringFuncs.length) {\n      acc.duringNS[name] = duringFuncs\n    }\n    if (afterFuncs.length) {\n      acc.afterNS[name] = afterFuncs\n    }\n    return acc\n  }, {\n    before: core[0],\n    beforeNS: {},\n    during: core[1],\n    duringNS: {},\n    after: core[2],\n    afterNS: {}\n  })\n}\n\nfunction shouldAbort({ abort }, pluginName) {\n  if (!abort) return false\n  if (abort === true) return true\n  return includes(abort, pluginName) || (abort && includes(abort.plugins, pluginName))\n}\n\nfunction shouldAbortAll({ abort }, pluginsCount) {\n  if (!abort) return false\n  if (abort === true || isString(abort)) return true\n  const { plugins } = abort\n  return (isArray(abort) && (abort.length === pluginsCount)) || (isArray(plugins) && (plugins.length === pluginsCount))\n}\n\nfunction isArray(arr) {\n  return Array.isArray(arr)\n}\n\nfunction includes(arr, name) {\n  if (!arr || !isArray(arr)) return false\n  return arr.includes(name)\n}\n\n/**\n * Generate arguments to pass to plugin methods\n * @param  {Object} instance - analytics instance\n * @param  {array} abortablePlugins - plugins that can be cancelled by caller\n * @return {*} function to inject plugin params\n */\nfunction argumentFactory(instance, abortablePlugins) {\n  // console.log('abortablePlugins', abortablePlugins)\n  return function (action, plugin, otherPlugin) {\n    const { config, name } = plugin\n    let method = `${name}.${action.type}`\n    if (otherPlugin) {\n      method = otherPlugin.event\n    }\n\n    const abortF = (action.type.match(endsWithStartRegex))\n      ? abortFunction(name, method, abortablePlugins, otherPlugin, action)\n      : notAbortableError(action, method)\n\n    return {\n      /* self: plugin, for future maybe */\n      // clone objects to avoid reassign\n      payload: formatPayload(action),\n      instance: instance,\n      config: config || {},\n      abort: abortF\n    }\n  }\n}\n\nfunction abortFunction(pluginName, method, abortablePlugins, otherPlugin, action) {\n  return function (reason, plugins) {\n    const caller = (otherPlugin) ? otherPlugin.name : pluginName\n    let pluginsToAbort = (plugins && isArray(plugins)) ? plugins : abortablePlugins\n    if (otherPlugin) {\n      pluginsToAbort = (plugins && isArray(plugins)) ? plugins : [pluginName]\n      if (!pluginsToAbort.includes(pluginName) || pluginsToAbort.length !== 1) {\n        throw new Error(`Method ${method} can only abort ${pluginName} plugin. ${JSON.stringify(pluginsToAbort)} input valid`)\n      }\n    }\n    return {\n      ...action, // 🔥 todo verify this merge is ok\n      abort: {\n        reason: reason,\n        plugins: pluginsToAbort,\n        caller: method,\n        _: caller\n      }\n    }\n  }\n}\n\nfunction notAbortableError(action, method) {\n  return () => {\n    throw new Error(action.type + ' action not cancellable. Remove abort in ' + method)\n  }\n}\n\n/**\n * Verify plugin is not calling itself with whatever:myPluginName self refs\n */\nfunction validateMethod(actionName, pluginName) {\n  const text = getNameSpacedAction(actionName)\n  const methodCallMatchesPluginNamespace = text && (text.name === pluginName)\n  if (methodCallMatchesPluginNamespace) {\n    const sub = getNameSpacedAction(text.method)\n    const subText = (sub) ? 'or ' + sub.method : ''\n    throw new Error([ pluginName + ' plugin is calling method ' + actionName,\n      'Plugins cant call self',\n      `Use ${text.method} ${subText} in ${pluginName} plugin insteadof ${actionName}`]\n      .join('\\n')\n    )\n  }\n}\n\nfunction getNameSpacedAction(event) {\n  const split = event.match(/(.*):(.*)/)\n  if (!split) {\n    return false\n  }\n  return {\n    method: split[1],\n    name: split[2],\n  }\n}\n\nfunction formatPayload(action) {\n  return Object.keys(action).reduce((acc, key) => {\n    // redact type from { payload }\n    if (key === 'type') {\n      return acc\n    }\n    if (isObject(action[key])) {\n      acc[key] = Object.assign({}, action[key])\n    } else {\n      acc[key] = action[key]\n    }\n    return acc\n  }, {})\n}\n\n/*\nfunction getMatchingMethods(eventType, activePlugins) {\n  const exact = getPluginFunctions(eventType, activePlugins)\n  // console.log('exact', exact)\n  // Gather nameSpaced Events\n  return activePlugins.reduce((acc, plugin) => {\n    const { name } = plugin\n    const clean = getPluginFunctions(`${eventType}:${name}`, activePlugins)\n    if (clean.length) {\n      acc.namespaced[name] = clean\n    }\n    return acc\n  }, {\n    exact: exact,\n    namespaced: {}\n  })\n}\n*/\n", "import EVENTS, { nonEvents } from '../../events'\nimport { runCallback, stack } from '../../utils/callback-stack'\nimport waitForReady from '../../utils/waitForReady'\nimport { processQueue } from '../../utils/heartbeat'\nimport runPlugins from './engine'\n\nexport default function pluginMiddleware(instance, getPlugins, systemEvents) {\n  const isReady = {}\n  return store => next => async action => {\n    const { type, abort, plugins } = action\n    let updatedAction = action\n\n    if (abort) {\n      return next(action)\n    }\n\n    /* Analytics.plugins.enable called, we need to init the plugins */\n    if (type === EVENTS.enablePlugin) {\n      store.dispatch({\n        type: EVENTS.initializeStart,\n        plugins: plugins,\n        disabled: [],\n        fromEnable: true,\n        meta: action.meta\n      })\n    }\n    \n    if (type === EVENTS.disablePlugin) {\n      // If cached callback, resolve promise/run callback. debounced to fix race condition\n      setTimeout(() => runCallback(action.meta.rid, { payload: action }), 0)\n    }\n\n    /* @TODO implement\n    if (type === EVENTS.loadPlugin) {\n      // Rerun initialize calls in plugins\n      const allPlugins = getPlugins()\n      const pluginsToLoad = Object.keys(allPlugins).filter((name) => {\n        return plugins.includes(name)\n      }).reduce((acc, curr) => {\n        acc[curr] = allPlugins[curr]\n        return acc\n      }, {})\n      const initializeAction = {\n        type: EVENTS.initializeStart,\n        plugins: plugins\n      }\n      const updated = await runPlugins(initializeAction, pluginsToLoad, instance, store, systemEvents)\n      return next(updated)\n    }\n    */\n\n    //  || type.match(/^initializeAbort:/)\n    if (type === EVENTS.initializeEnd) {\n      const allPlugins = getPlugins()\n      const pluginsArray = Object.keys(allPlugins)\n      const allRegisteredPlugins = pluginsArray.filter((name) => {\n        return plugins.includes(name)\n      }).map((name) => {\n        return allPlugins[name]\n      })\n      let completed = []\n      let failed = []\n      let disabled = action.disabled\n      // console.log('allRegisteredPlugins', allRegisteredPlugins)\n      const waitForPluginsToLoad = allRegisteredPlugins.map((plugin) => {\n        const { loaded, name, config } = plugin\n        const loadedFn = () => loaded({ config }) // @TODO add in more to api to match other funcs?\n        /* Plugins will abort trying to load after 10 seconds. 1e4 === 10000 MS */\n        return waitForReady(plugin, loadedFn, 1e4).then((d) => {\n          if (!isReady[name]) {\n            // only dispatch namespaced rdy once\n            store.dispatch({\n              type: EVENTS.pluginReadyType(name), // `ready:${name}`\n              name: name,\n              events: Object.keys(plugin).filter((name) => {\n                return !nonEvents.includes(name)\n              })\n            })\n            isReady[name] = true\n          }\n          completed = completed.concat(name)\n\n          return plugin\n          // It's loaded! run the command\n        }).catch((e) => {\n          // Timeout Add to queue\n          // console.log('Error generic waitForReady. Push this to queue', e)\n          if (e instanceof Error) {\n            throw new Error(e)\n          }\n          failed = failed.concat(e.name)\n          // Failed to fire, add to queue\n          return e\n        })\n      })\n\n      Promise.all(waitForPluginsToLoad).then((calls) => {\n        // setTimeout to ensure runs after 'page'\n        const payload = {\n          plugins: completed,\n          failed: failed,\n          disabled: disabled\n        }\n        setTimeout(() => {\n          if (pluginsArray.length === (waitForPluginsToLoad.length + disabled.length)) {\n            store.dispatch({\n              ...{ type: EVENTS.ready },\n              ...payload,\n              \n            })\n          }\n        }, 0)\n      })\n    }\n\n    /* New plugin system */\n    if (type !== EVENTS.bootstrap) {\n      if (/^ready:([^:]*)$/.test(type)) {\n        // Immediately flush queue\n        setTimeout(() => processQueue(store, getPlugins, instance), 0)\n      }\n      const updated = await runPlugins(action, getPlugins, instance, store, systemEvents)\n      return next(updated)\n    }\n\n    return next(updatedAction)\n  }\n}\n", "import { isBoolean } from '@analytics/type-utils'\n\nexport default function fitlerDisabledPlugins(allPlugins, settings = {}, options = {}) {\n  return Object.keys(allPlugins).filter((name) => {\n    const fromCallOptions = options.plugins || {}\n    // If enabled/disabled by options. Override settings\n    if (isBoolean(fromCallOptions[name])) {\n      return fromCallOptions[name]\n    }\n    // If all: false disable everything unless true explicitly set\n    if (fromCallOptions.all === false) {\n      return false\n    }\n    // else use state.plugin settings\n    if (settings[name] && settings[name].enabled === false) {\n      return false\n    }\n    return true\n  }).map((name) => allPlugins[name])\n}\n", "import EVENTS from '../events'\n\nexport default function storageMiddleware(storage) {\n  return store => next => action => {\n    const { type, key, value, options } = action\n    if (type === EVENTS.setItem || type === EVENTS.removeItem) {\n      if (action.abort) {\n        return next(action)\n      }\n      // Run storage set or remove\n      if (type === EVENTS.setItem) {\n        storage.setItem(key, value, options)\n      } else {\n        storage.removeItem(key, options)\n      }\n    }\n    return next(action)\n  }\n}\n\n/*\n  Todo: emit events for keys we care about\n  window.addEventListener('storage', (event) => console.log(event));\n*/\n", "import { compose } from '../vendor/redux'\n\n/* Class to fix dynamic middlewares from conflicting with each other\nif more than one analytic instance is active */\nexport default class DynamicMiddleware {\n  before = []\n  after = []\n  addMiddleware = (middlewares, position) => {\n    this[position] = this[position].concat(middlewares)\n  }\n  removeMiddleware = (middleware, position) => {\n    const index = this[position].findIndex(d => d === middleware)\n    if (index === -1) return\n\n    this[position] = [\n      ...this[position].slice(0, index),\n      ...this[position].slice(index + 1),\n    ]\n  }\n  /* Not currently in use\n  resetMiddlewares = (position) => {\n    if (!position) {\n      this.before = []\n      this.after = []\n    } else {\n      this[position] = []\n    }\n  }\n  */\n  dynamicMiddlewares = (position) => {\n    return store => next => action => {\n      const middlewareAPI = {\n        getState: store.getState,\n        dispatch: (act) => store.dispatch(act),\n      }\n      const chain = this[position].map(middleware => middleware(middlewareAPI))\n      return compose(...chain)(next)(action)\n    }\n  }\n}\n", "// Integrations Reducer. Follows ducks pattern http://bit.ly/2DnERMc\nimport EVENTS from '../events'\n\nexport default function createReducer(getPlugins) {\n  return function plugins(state = {}, action) {\n    let newState = {}\n    if (action.type === 'initialize:aborted') {\n      return state\n    }\n    if (/^registerPlugin:([^:]*)$/.test(action.type)) {\n      const name = getNameFromEventType(action.type, 'registerPlugin')\n      const plugin = getPlugins()[name]\n      if (!plugin || !name) {\n        return state\n      }\n      const isEnabled = action.enabled\n      const config = plugin.config\n      newState[name] = {\n        enabled: isEnabled,\n        /* if no initialization method. Set initialized true */\n        initialized: (isEnabled) ? Boolean(!plugin.initialize) : false,\n        /* If plugin enabled === false, set loaded to false, else check plugin.loaded function */\n        loaded: (isEnabled) ? Boolean(plugin.loaded({ config })) : false,\n        config\n      }\n      return { ...state, ...newState }\n    }\n    if (/^initialize:([^:]*)$/.test(action.type)) {\n      const name = getNameFromEventType(action.type, EVENTS.initialize)\n      const plugin = getPlugins()[name]\n      if (!plugin || !name) {\n        return state\n      }\n      const config = plugin.config\n      newState[name] = {\n        ...state[name],\n        ...{\n          initialized: true,\n          /* check plugin.loaded function */\n          loaded: Boolean(plugin.loaded({ config }))\n        }\n      }\n      return { ...state, ...newState }\n    }\n    if (/^ready:([^:]*)$/.test(action.type)) {\n      // const name = getNameFromEventType(action.type, 'ready')\n      newState[action.name] = {\n        ...state[action.name],\n        ...{ loaded: true }\n      }\n      return { ...state, ...newState }\n    }\n    switch (action.type) {\n      /* case EVENTS.pluginFailed:\n        // console.log('PLUGIN_FAILED', action.name)\n        newState[action.name] = {\n          ...state[action.name],\n          ...{ loaded: false }\n        }\n        return { ...state, ...newState }\n      */\n      /* When analytics.plugins.disable called */\n      case EVENTS.disablePlugin:\n        return { \n          ...state,\n          ...togglePluginStatus(action.plugins, false, state)\n        }\n      /* When analytics.plugins.enable called */\n      case EVENTS.enablePlugin:\n        return {\n          ...state, \n          ...togglePluginStatus(action.plugins, true, state)\n        }\n      default:\n        return state\n    }\n  }\n}\n\nfunction getNameFromEventType(type, baseName) {\n  return type.substring(baseName.length + 1, type.length)\n}\n\nfunction togglePluginStatus(plugins, status, currentState) {\n  return plugins.reduce((acc, pluginKey) => {\n    acc[pluginKey] = {\n      ...currentState[pluginKey],\n      ...{\n        enabled: status \n      }\n    }\n    return acc\n  }, currentState)\n}\n", "export default function serialize(obj) {\n  try {\n   return JSON.parse(JSON.stringify(obj))\n  } catch (err) {}\n  return obj\n}", "// Track Module. Follows ducks pattern http://bit.ly/2DnERMc\nimport EVENTS from '../events'\nimport serialize from '../utils/serialize'\n\n// Track State\nconst initialState = {\n  last: {},\n  history: [],\n}\n\n// track reducer\nexport default function trackReducer(state = initialState, action) {\n  const { type, event, properties, options, meta } = action\n\n  switch (type) {\n    case EVENTS.track:\n      const trackEvent = serialize({\n        event,\n        properties,\n        ...(Object.keys(options).length) && { options: options },\n        meta\n      })\n      return {\n        ...state,\n        ...{\n          last: trackEvent,\n          // Todo prevent LARGE arrays https://bit.ly/2MnBwPT\n          history: state.history.concat(trackEvent)\n        }\n      }\n    default:\n      return state\n  }\n}\n", "import EVENTS from '../events'\n/*\nTODO figure out if this should live in state...\nQueue could be in mermory as well.\nBut also needs to be persisted to storage\n*/\n\nconst initialState = {\n  actions: [],\n}\n\nexport default function queueReducer(state = initialState, action) {\n  const { type, payload } = action\n\n  switch (type) {\n    case 'queue':\n      let actionChain\n      /* prioritize identify in event queue */\n      if (payload && payload.type && payload.type === EVENTS.identify) {\n        actionChain = [action].concat(state.actions)\n      } else {\n        actionChain = state.actions.concat(action)\n      }\n      return {\n        ...state,\n        actions: actionChain\n      }\n    case 'dequeue':\n      return []\n    // todo push events to history\n    default:\n      return state\n  }\n}\n\nexport const queueAction = (data, timestamp) => {\n  return {\n    type: 'queue',\n    timestamp: timestamp,\n    data: data\n  }\n}\n", "// Page View Reducer. Follows ducks pattern http://bit.ly/2DnERMc\nimport { isBrowser } from '@analytics/type-utils'\nimport serialize from '../utils/serialize'\n\nimport EVENTS from '../events'\n\nconst hashRegex = /#.*$/\n\nfunction canonicalUrl() {\n  if (!isBrowser) return\n  const tags = document.getElementsByTagName('link')\n  for (var i = 0, tag; tag = tags[i]; i++) {\n    if (tag.getAttribute('rel') === 'canonical') {\n      return tag.getAttribute('href')\n    }\n  }\n}\n\nfunction urlPath(url) {\n  const regex = /(http[s]?:\\/\\/)?([^\\/\\s]+\\/)(.*)/g\n  const matches = regex.exec(url)\n  const pathMatch = (matches && matches[3]) ? matches[3].split('?')[0].replace(hashRegex, '') : ''\n  return '/' + pathMatch\n}\n\n/**\n * Return the canonical URL and rmove the hash.\n * @param  {string} search - search param\n * @return {string} return current canonical URL\n */\nfunction currentUrl(search) {\n  const canonical = canonicalUrl()\n  if (!canonical) return window.location.href.replace(hashRegex, '')\n  return canonical.match(/\\?/) ? canonical : canonical + search\n}\n\n/**\n * Page data for overides\n * @typedef {object} PageData\n * @property {string} [title] - Page title\n * @property {string} [url] - Page url\n * @property {string} [path] - Page path\n * @property {string} [search] - Page search\n * @property {string} [width] - Page width\n * @property {string} [height] - Page height\n*/\n\n/**\n * Get information about current page\n * @typedef {Function} getPageData\n * @param  {PageData} [pageData = {}] - Page data overides\n * @return {PageData} resolved page data\n */\nexport const getPageData = (pageData = {}) => {\n  if (!isBrowser) return pageData\n  const { title, referrer } = document\n  const { location, innerWidth, innerHeight } = window\n  const { hash, search } = location\n  const url = currentUrl(search)\n  const page = {\n    title: title,\n    url: url,\n    path: urlPath(url),\n    hash: hash,\n    search: search,\n    width: innerWidth,\n    height: innerHeight,\n  }\n  if (referrer && referrer !== '') {\n    page.referrer = referrer\n  }\n\n  return {\n    ...page,\n    /* .page() user overrrides */\n    ...pageData\n  }\n}\n\nconst initialState = {\n  last: {},\n  history: [],\n}\n\n// page reducer\nexport default function page(state = initialState, action) {\n  const { properties, options, meta } = action\n  switch (action.type) {\n    case EVENTS.page:\n      const viewData = serialize({\n        properties,\n        meta,\n        ...(Object.keys(options).length) && { options: options },\n      })\n      return {\n        ...state,\n        ...{\n          last: viewData,\n          // Todo prevent LARGE arrays https://bit.ly/2MnBwPT\n          history: state.history.concat(viewData)\n        }\n      }\n    default:\n      return state\n  }\n}\n", "// Context Reducer.  Follows ducks pattern http://bit.ly/2DnERMc\nimport { getBrowserLocale, getTimeZone, uuid } from 'analytics-utils'\nimport { isBrowser } from '@analytics/type-utils'\nimport EVENTS from '../events'\nimport { LIB_NAME } from '../utils/internalConstants'\nimport getOSNameNode from '../utils/getOSName/node'\nimport getOSNameBrowser from '../utils/getOSName/browser'\n\nlet osName\nlet referrer\nlet locale\nlet timeZone\nif (BROWSER) {\n  osName = getOSNameBrowser()\n  referrer = (isBrowser) ? document.referrer : null\n  locale = getBrowserLocale()\n  timeZone = getTimeZone()\n} else {\n  osName = getOSNameNode()\n  referrer = {}\n}\n\nconst initialState = {\n  initialized: false,\n  sessionId: uuid(),\n  app: null,\n  version: null,\n  debug: false,\n  offline: (isBrowser) ? !navigator.onLine : false, // use node network is-online\n  os: {\n    name: osName,\n  },\n  userAgent: (isBrowser) ? navigator.userAgent : 'node', // https://github.com/bestiejs/platform.js\n  library: {\n    name: LIB_NAME,\n    // TODO fix version number. npm run publish:patch has wrong version\n    version: VERSION\n  },\n  timezone: timeZone,\n  locale: locale,\n  campaign: {},\n  referrer: referrer,\n}\n\n// context reducer\nexport default function context(state = initialState, action) {\n  const { initialized } = state\n  const { type, campaign } = action\n  switch (type) {\n    case EVENTS.campaign:\n      return {\n        ...state,\n        ...{ campaign: campaign }\n      }\n    case EVENTS.offline:\n      return {\n        ...state,\n        ...{ offline: true }\n      }\n    case EVENTS.online:\n      return {\n        ...state,\n        ...{ offline: false }\n      }\n    default:\n      if (!initialized) {\n        return {\n          ...initialState,\n          ...state,\n          ...{ initialized: true }\n        }\n      }\n      return state\n  }\n}\n\nconst excludeItems = ['plugins', 'reducers', 'storage']\n// Pull plugins and reducers off intital config\nexport function makeContext(config) {\n  return Object.keys(config).reduce((acc, current) => {\n    if (excludeItems.includes(current)) {\n      return acc\n    }\n    acc[current] = config[current]\n    return acc\n  }, {})\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\nexport default function getBrowserOS() {\n  if (!isBrowser) return false\n  const os = navigator.appVersion\n  // ~os bitwise operator to check if in navigator\n  if (~os.indexOf('Win')) return 'Windows'\n  if (~os.indexOf('Mac')) return 'MacOS'\n  if (~os.indexOf('X11')) return 'UNIX'\n  if (~os.indexOf('Linux')) return 'Linux'\n  // default\n  return 'Unknown OS'\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\nfunction listen(events, func, toAdd) {\n  if (!isBrowser) return\n  const fn = window[(toAdd ? 'add' : 'remove') + 'EventListener']\n  events.split(' ').forEach(ev => {\n    fn(ev, func)\n  })\n}\n\nexport function check() {\n  return Promise.resolve(!navigator.onLine)\n}\n\nexport function watch(cb) {\n  const fn = _ => check().then(cb)\n  const listener = listen.bind(null, 'online offline', fn)\n  listener(true)\n  // return unsubscribe function\n  return _ => listener(false)\n}\n", "import { set, globalContext, KEY } from '@analytics/global-storage-utils'\nimport { compose } from '../vendor/redux'\nimport { LIB_NAME } from './internalConstants'\n\nexport function Debug() {\n  // Global key is window.__global__.analytics\n  set(LIB_NAME, [])\n  // Return debugger\n  return (createStore) => {\n    return (reducer, preloadedState, enhancer) => {\n      const store = createStore(reducer, preloadedState, enhancer)\n      const origDispatch = store.dispatch\n      const dispatch = (action) => {\n        const a = action.action || action\n        globalContext[KEY][LIB_NAME].push(a)\n        return origDispatch(action)\n      }\n      return Object.assign(store, { dispatch: dispatch })\n    }\n  }\n}\n\nexport function composeWithDebug(config) {\n  return function () {\n    return compose(compose.apply(null, arguments), Debug(config))\n  }\n}\n", "import { isArray } from '@analytics/type-utils'\n\nexport default function ensureArray(singleOrArray) {\n  if (!singleOrArray) return []\n  if (isArray(singleOrArray)) return singleOrArray\n  return [singleOrArray] \n}", "import getCallback from './getCallback'\nimport { stack } from './callback-stack'\nimport timestamp from './timestamp'\nimport { uuid } from 'analytics-utils'\n\n// Async promise resolver\nfunction deferredPromiseResolver(resolver, callback) {\n  return (data) => {\n    if (callback) callback(data)\n    resolver(data)\n  }\n}\n\nexport default function generateMeta(meta = {}, resolve, possibleCallbacks) {\n    const rid = uuid()\n    if (resolve) {\n      // stack[`${rid}-info`] = meta\n      stack[rid] = deferredPromiseResolver(resolve, getCallback(possibleCallbacks))\n    }\n    return {\n      ...meta,\n      rid: rid,\n      ts: timestamp(),\n      ...(!resolve) ? {} : { hasCallback: true },\n    }\n  }", "import { isFunction } from '@analytics/type-utils'\n\n/**\n * Grab first function found from arguments\n * @param {array} [argArray] - arguments array to find first function\n * @returns {Function|undefined}\n */\nexport default function getCallbackFromArgs(argArray) {\n  const args = argArray || Array.prototype.slice.call(arguments)\n  let cb\n  for (let i = 0; i < args.length; i++) {\n    if (isFunction(args[i])) {\n      cb = args[i]; break;\n    }\n  }\n  return cb\n}", "\nexport default function timeStamp() {\n  return new Date().getTime()\n}\n", "import { uuid, paramsParse, dotProp } from 'analytics-utils'\nimport { get, set, remove } from '@analytics/global-storage-utils'\nimport { isBrowser, isFunction, isObject, isString } from '@analytics/type-utils'\nimport { createStore, combineReducers, applyMiddleware, compose } from './vendor/redux'\nimport * as CONSTANTS from './constants'\nimport { ID, ANONID, ERROR_URL } from './utils/internalConstants'\nimport EVENTS, { coreEvents, nonEvents, isReservedAction } from './events'\n// Middleware\nimport * as middleware from './middleware'\nimport DynamicMiddleware from './middleware/dynamic'\n// Modules\nimport pluginsMiddleware from './modules/plugins'\nimport track from './modules/track'\nimport queue from './modules/queue'\nimport page, { getPageData } from './modules/page'\nimport context, { makeContext } from './modules/context'\nimport user, { getUserPropFunc, tempKey, getPersistedUserData } from './modules/user'\n/* Utils */\nimport { watch } from './utils/handleNetworkEvents'\nimport { Debug, composeWithDebug } from './utils/debug'\nimport heartBeat from './utils/heartbeat'\nimport ensureArray from './utils/ensureArray'\nimport enrichMeta from './utils/enrichMeta'\nimport './pluginTypeDef'\n\n\n/**\n * Analytics library configuration\n *\n * After the library is initialized with config, the core API is exposed & ready for use in the application.\n *\n * @param {object} config - analytics core config\n * @param {string} [config.app] - Name of site / app\n * @param {string|number} [config.version] - Version of your app\n * @param {boolean} [config.debug] - Should analytics run in debug mode\n * @param {Array.<AnalyticsPlugin>}  [config.plugins] - Array of analytics plugins\n * @return {AnalyticsInstance} Analytics Instance\n * @example\n *\n * import Analytics from 'analytics'\n * import pluginABC from 'analytics-plugin-abc'\n * import pluginXYZ from 'analytics-plugin-xyz'\n *\n * // initialize analytics\n * const analytics = Analytics({\n *   app: 'my-awesome-app',\n *   plugins: [\n *     pluginABC,\n *     pluginXYZ\n *   ]\n * })\n *\n */\nfunction analytics(config = {}) {\n  const customReducers = config.reducers || {}\n  const initialUser = config.initialUser || {}\n  // @TODO add custom value reolvers for userId and anonId\n  // const resolvers = config.resolvers || {}\n  // if (BROWSER) {\n  //   console.log('INIT browser')\n  // }\n  // if (SERVER) {\n  //   console.log('INIT SERVER')\n  // }\n  /* Parse plugins array */\n  const parsedOptions = (config.plugins || []).reduce((acc, plugin) => {\n    if (isFunction(plugin)) {\n      /* Custom redux middleware */\n      acc.middlewares = acc.middlewares.concat(plugin)\n      return acc\n    }\n    // Legacy plugin with name\n    if (plugin.NAMESPACE) plugin.name = plugin.NAMESPACE\n    if (!plugin.name) {\n      /* Plugins must supply a \"name\" property. See error url for more details */\n      throw new Error(ERROR_URL + '1')\n    }\n    // Set config if empty\n    if (!plugin.config) plugin.config = {}\n    // if plugin exposes EVENTS capture available events\n    const definedEvents = (plugin.EVENTS) ? Object.keys(plugin.EVENTS).map((k) => {\n      return plugin.EVENTS[k]\n    }) : []\n\n    const enabledFromMerge = !(plugin.enabled === false)\n    const enabledFromPluginConfig = !(plugin.config.enabled === false)\n    // top level { enabled: false } takes presidence over { config: enabled: false }\n    acc.pluginEnabled[plugin.name] = enabledFromMerge && enabledFromPluginConfig\n    delete plugin.enabled\n\n    if (plugin.methods) {\n      acc.methods[plugin.name] = Object.keys(plugin.methods).reduce((a, c) => {\n        // enrich methods with analytics instance\n        a[c] = appendArguments(plugin.methods[c])\n        return a\n      }, {})\n      // Remove additional methods from plugins\n      delete plugin.methods\n    }\n    // Convert available methods into events\n    const methodsToEvents = Object.keys(plugin)\n    // Combine events\n    const allEvents = methodsToEvents.concat(definedEvents)\n    // Dedupe events list\n    const allEventsUnique = new Set(acc.events.concat(allEvents))\n    acc.events = Array.from(allEventsUnique)\n\n    acc.pluginsArray = acc.pluginsArray.concat(plugin)\n\n    if (acc.plugins[plugin.name]) {\n      throw new Error(plugin.name + 'AlreadyLoaded')\n    }\n    acc.plugins[plugin.name] = plugin\n    if (!acc.plugins[plugin.name].loaded) {\n      // set default loaded func\n      acc.plugins[plugin.name].loaded = () => true\n    }\n    return acc\n  }, {\n    plugins: {},\n    pluginEnabled: {},\n    methods: {},\n    pluginsArray: [],\n    middlewares: [],\n    events: []\n  })\n  \n  /* Storage by default is set to global & is not persisted */\n  const storage = (config.storage) ? config.storage : {\n    getItem: get,\n    setItem: set,\n    removeItem: remove\n  }\n\n  const getUserProp = getUserPropFunc(storage)\n\n  // mutable intregrations object for dynamic loading\n  let customPlugins = parsedOptions.plugins\n\n  /* Grab all registered events from plugins loaded */\n  const allPluginEvents = parsedOptions.events.filter((name) => {\n    return !nonEvents.includes(name)\n  }).sort()\n  const uniqueEvents = new Set(allPluginEvents.concat(coreEvents).filter((name) => {\n    return !nonEvents.includes(name)\n  }))\n  const allSystemEvents = Array.from(uniqueEvents).sort()\n\n  /* plugin methods(functions) must be kept out of state. thus they live here */\n  const getPlugins = () => customPlugins\n\n  const {\n    addMiddleware,\n    removeMiddleware,\n    dynamicMiddlewares\n  } = new DynamicMiddleware()\n\n  const nonAbortable = () => {\n    // throw new Error(`${ERROR_URL}3`)\n    throw new Error('Abort disabled inListener')\n  }\n  \n  // Parse URL parameters\n  const params = paramsParse()\n  // Initialize visitor information\n  const persistedUser = getPersistedUserData(storage)\n  const visitorInfo = {\n    ...persistedUser,\n    ...initialUser,\n    ...(!params.an_uid) ? {} : { userId: params.an_uid },\n    ...(!params.an_aid) ? {} : { anonymousId: params.an_aid },\n  }\n  // If no anon id set, create one\n  if (!visitorInfo.anonymousId) {\n    visitorInfo.anonymousId = uuid()\n  }\n\n  /**\n   * Async Management methods for plugins. \n   * \n   * This is also where [custom methods](https://bit.ly/329vFXy) are loaded into the instance.\n   * @typedef {Object} Plugins\n   * @property {EnablePlugin} enable - Set storage value\n   * @property {DisablePlugin} disable - Remove storage value\n   * @example\n   *\n   * // Enable a plugin by namespace\n   * analytics.plugins.enable('keenio')\n   *\n   * // Disable a plugin by namespace\n   * analytics.plugins.disable('google-analytics')\n   */\n  const plugins = {\n    /**\n     * Enable analytics plugin\n     * @typedef {Function} EnablePlugin\n     * @param  {string|string[]} plugins - name of plugins(s) to disable\n     * @param  {Function} [callback] - callback after enable runs\n     * @returns {Promise}\n     * @example\n     *\n     * analytics.plugins.enable('google-analytics').then(() => {\n     *   console.log('do stuff')\n     * })\n     *\n     * // Enable multiple plugins at once\n     * analytics.plugins.enable(['google-analytics', 'segment']).then(() => {\n     *   console.log('do stuff')\n     * })\n     */\n    enable: (plugins, callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.enablePlugin,\n          plugins: ensureArray(plugins),\n          _: { originalAction: EVENTS.enablePlugin },\n        }, resolve, [ callback ])\n      })\n    },\n    /**\n     * Disable analytics plugin\n     * @typedef {Function} DisablePlugin\n     * @param  {string|string[]} plugins - name of integration(s) to disable\n     * @param  {Function} [callback] - callback after disable runs\n     * @returns {Promise}\n     * @example\n     *\n     * analytics.plugins.disable('google').then(() => {\n     *   console.log('do stuff')\n     * })\n     *\n     * analytics.plugins.disable(['google', 'segment']).then(() => {\n     *   console.log('do stuff')\n     * })\n     */\n    disable: (plugins, callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.disablePlugin,\n          plugins: ensureArray(plugins),\n          _: { originalAction: EVENTS.disablePlugin },\n        }, resolve, [callback])\n      })\n    },\n    /*\n     * Load registered analytic providers.\n     * @param  {String} plugins - integration namespace\n     *\n     * @example\n     * analytics.plugins.load('segment')\n     @TODO implement\n    load: (plugins) => {\n      store.dispatch({\n        type: EVENTS.loadPlugin,\n        // Todo handle multiple plugins via array\n        plugins: (plugins) ? [plugins] : Object.keys(getPlugins()),\n      })\n    },\n    */\n    /* @TODO if it stays, state loaded needs to be set. Re PLUGIN_INIT above\n    add: (newPlugin) => {\n      if (typeof newPlugin !== 'object') return false\n      // Set on global integration object\n      customPlugins = Object.assign({}, customPlugins, {\n        [`${newPlugin.name}`]: newPlugin\n      })\n      // then add it, and init state key\n      store.dispatch({\n        type: EVENTS.pluginRegister,\n        name: newPlugin.name,\n        plugin: newPlugin\n      })\n    }, */\n    // Merge in custom plugin methods\n    ...parsedOptions.methods\n  }\n  \n  let readyCalled = false\n  /**\n   * Analytic instance returned from initialization\n   * @typedef {Object} AnalyticsInstance\n   * @property {Identify} identify - Identify a user\n   * @property {Track} track - Track an analytics event\n   * @property {Page} page - Trigger page view\n   * @property {User} user - Get user data\n   * @property {Reset} reset - Clear information about user & reset analytics\n   * @property {Ready} ready - Fire callback on analytics ready event\n   * @property {On} on - Fire callback on analytics lifecycle events.\n   * @property {Once} once - Fire callback on analytics lifecycle events once.\n   * @property {GetState} getState - Get data about user, activity, or context.\n   * @property {Storage} storage - storage methods\n   * @property {Plugins} plugins - plugin methods\n   */\n  const instance = {\n    /**\n    * Identify a user. This will trigger `identify` calls in any installed plugins and will set user data in localStorage\n    * @typedef {Function} Identify\n    * @param  {String}   userId  - Unique ID of user\n    * @param  {Object}   [traits]  - Object of user traits\n    * @param  {Object}   [options] - Options to pass to identify call\n    * @param  {Function} [callback] - Callback function after identify completes\n    * @returns {Promise}\n    * @api public\n    *\n    * @example\n    *\n    * // Basic user id identify\n    * analytics.identify('xyz-123')\n    *\n    * // Identify with additional traits\n    * analytics.identify('xyz-123', {\n    *   name: 'steve',\n    *   company: 'hello-clicky'\n    * })\n    *\n    * // Fire callback with 2nd or 3rd argument\n    * analytics.identify('xyz-123', () => {\n    *   console.log('do this after identify')\n    * })\n    *\n    * // Disable sending user data to specific analytic tools\n    * analytics.identify('xyz-123', {}, {\n    *   plugins: {\n    *     // disable sending this identify call to segment\n    *     segment: false\n    *   }\n    * })\n    *\n    * // Send user data to only to specific analytic tools\n    * analytics.identify('xyz-123', {}, {\n    *   plugins: {\n    *     // disable this specific identify in all plugins except customerio\n    *     all: false,\n    *     customerio: true\n    *   }\n    * })\n    */\n    identify: async (userId, traits, options, callback) => {\n      const id = isString(userId) ? userId : null\n      const data = isObject(userId) ? userId : traits\n      const opts = options || {}\n      const user = instance.user()\n\n      /* sets temporary in memory id. Not to be relied on */\n      set(tempKey(ID), id)\n\n      const resolvedId = id || data.userId || getUserProp(ID, instance, data)\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.identifyStart,\n          userId: resolvedId,\n          traits: data || {},\n          options: opts,\n          anonymousId: user.anonymousId,\n          // Add previousId if exists\n          ...(user.id && (user.id !== id) && { previousId: user.id }),\n        }, resolve, [traits, options, callback])\n      })\n    },\n    /**\n     * Track an analytics event. This will trigger `track` calls in any installed plugins\n     * @typedef {Function} Track\n     * @param  {String}   eventName - Event name\n     * @param  {Object}   [payload]   - Event payload\n     * @param  {Object}   [options]   - Event options\n     * @param  {Function} [callback]  - Callback to fire after tracking completes\n     * @returns {Promise}\n     * @api public\n     *\n     * @example\n     *\n     * // Basic event tracking\n     * analytics.track('buttonClicked')\n     *\n     * // Event tracking with payload\n     * analytics.track('itemPurchased', {\n     *   price: 11,\n     *   sku: '1234'\n     * })\n     *\n     * // Fire callback with 2nd or 3rd argument\n     * analytics.track('newsletterSubscribed', () => {\n     *   console.log('do this after track')\n     * })\n     *\n     * // Disable sending this event to specific analytic tools\n     * analytics.track('cartAbandoned', {\n     *   items: ['xyz', 'abc']\n     * }, {\n     *   plugins: {\n     *     // disable track event for segment\n     *     segment: false\n     *   }\n     * })\n     *\n     * // Send event to only to specific analytic tools\n     * analytics.track('customerIoOnlyEventExample', {\n     *   price: 11,\n     *   sku: '1234'\n     * }, {\n     *   plugins: {\n     *     // disable this specific track call all plugins except customerio\n     *     all: false,\n     *     customerio: true\n     *   }\n     * })\n     */\n    track: async (eventName, payload, options, callback) => {\n      const name = isObject(eventName) ? eventName.event : eventName\n      if (!name || !isString(name)) {\n        throw new Error('EventMissing')\n      }\n      const data = isObject(eventName) ? eventName : (payload || {})\n      const opts = isObject(options) ? options : {}\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.trackStart,\n          event: name,\n          properties: data,\n          options: opts,\n          userId: getUserProp(ID, instance, payload),\n          anonymousId: getUserProp(ANONID, instance, payload),\n        }, resolve, [payload, options, callback])\n      })\n    },\n    /**\n     * Trigger page view. This will trigger `page` calls in any installed plugins\n     * @typedef {Function} Page\n     * @param  {PageData} [data] - Page data overrides.\n     * @param  {Object}   [options] - Page tracking options\n     * @param  {Function} [callback] - Callback to fire after page view call completes\n     * @returns {Promise}\n     * @api public\n     *\n     * @example\n     *\n     * // Basic page tracking\n     * analytics.page()\n     *\n     * // Page tracking with page data overrides\n     * analytics.page({\n     *   url: 'https://google.com'\n     * })\n     *\n     * // Fire callback with 1st, 2nd or 3rd argument\n     * analytics.page(() => {\n     *   console.log('do this after page')\n     * })\n     *\n     * // Disable sending this pageview to specific analytic tools\n     * analytics.page({}, {\n     *   plugins: {\n     *     // disable page tracking event for segment\n     *     segment: false\n     *   }\n     * })\n     *\n     * // Send pageview to only to specific analytic tools\n     * analytics.page({}, {\n     *   plugins: {\n     *     // disable this specific page in all plugins except customerio\n     *     all: false,\n     *     customerio: true\n     *   }\n     * })\n     */\n    page: async (data, options, callback) => {\n      const d = isObject(data) ? data : {}\n      const opts = isObject(options) ? options : {}\n\n      /*\n      // @TODO add custom value reolvers for userId and anonId\n      if (resolvers.getUserId) {\n        const asyncUserId = await resolvers.getUserId()\n        console.log('x', x)\n      }\n      */\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.pageStart,\n          properties: getPageData(d),\n          options: opts,\n          userId: getUserProp(ID, instance, d),\n          anonymousId: getUserProp(ANONID, instance, d),\n        }, resolve, [data, options, callback])\n      })\n    },\n    /**\n     * Get user data\n     * @typedef {Function} User\n     * @param {string} [key] - dot.prop.path of user data. Example: 'traits.company.name'\n     * @returns {string|object} value of user data or null\n     *\n     * @example\n     *\n     * // Get all user data\n     * const userData = analytics.user()\n     *\n     * // Get user id\n     * const userId = analytics.user('userId')\n     *\n     * // Get user company name\n     * const companyName = analytics.user('traits.company.name')\n     */\n    user: (key) => {\n      if (key === ID || key === 'id') {\n        return getUserProp(ID, instance)\n      }\n      if (key === ANONID || key === 'anonId') {\n        return getUserProp(ANONID, instance)\n      }\n      const user = instance.getState('user')\n      if (!key) return user\n      return dotProp(user, key)\n    },\n    /**\n     * Clear all information about the visitor & reset analytic state.\n     * @typedef {Function} Reset\n     * @param {Function} [callback] - Handler to run after reset\n     * @returns {Promise}\n     * @example\n     *\n     * // Reset current visitor\n     * analytics.reset()\n     */\n    reset: (callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.resetStart\n        }, resolve, callback)\n      })\n    },\n    /**\n     * Fire callback on analytics ready event\n     * @typedef {Function} Ready\n     * @param  {Function} callback - function to trigger when all providers have loaded\n     * @returns {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * analytics.ready((payload) => {\n     *   console.log('all plugins have loaded or were skipped', payload);\n     * })\n     */\n    ready: (callback) => {\n      // If ready already fired. Call callback immediately\n      if (readyCalled) callback({ plugins, instance })\n      return instance.on(EVENTS.ready, (x) => {\n        callback(x)\n        readyCalled = true\n      })\n    },\n    /**\n     * Attach an event handler function for analytics lifecycle events.\n     * @typedef {Function} On\n     * @param  {String}   name - Name of event to listen to\n     * @param  {Function} callback - function to fire on event\n     * @return {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * // Fire function when 'track' calls happen\n     * analytics.on('track', ({ payload }) => {\n     *   console.log('track call just happened. Do stuff')\n     * })\n     *\n     * // Remove listener before it is called\n     * const removeListener = analytics.on('track', ({ payload }) => {\n     *   console.log('This will never get called')\n     * })\n     *\n     * // cleanup .on listener\n     * removeListener()\n     */\n    on: (name, callback) => {\n      if (!name || !isFunction(callback)) {\n        return false\n      }\n      if (name === EVENTS.bootstrap) {\n        throw new Error('.on disabled for ' + name)\n      }\n      const startRegex = /Start$|Start:/\n      if (name === '*') {\n        const beforeHandler = store => next => action => {\n          if (action.type.match(startRegex)) {\n            callback({ // eslint-disable-line\n              payload: action,\n              instance,\n              plugins: customPlugins\n            })\n          }\n          return next(action)\n        }\n        const afterHandler = store => next => action => {\n          if (!action.type.match(startRegex)) {\n            callback({ // eslint-disable-line\n              payload: action,\n              instance,\n              plugins: customPlugins\n            })\n          }\n          return next(action)\n        }\n        addMiddleware(beforeHandler, before)\n        addMiddleware(afterHandler, after)\n        /**\n         * Detach listeners\n         * @typedef {Function} DetachListeners\n         */\n        return () => {\n          removeMiddleware(beforeHandler, before)\n          removeMiddleware(afterHandler, after)\n        }\n      }\n\n      const position = (name.match(startRegex)) ? before : after // eslint-disable-line\n      const handler = store => next => action => {\n        // Subscribe to EVERYTHING\n        if (action.type === name) {\n          callback({ // eslint-disable-line\n            payload: action,\n            instance: instance,\n            plugins: customPlugins,\n            abort: nonAbortable\n          })\n        }\n        /* For future matching of event subpaths `track:*` etc\n        } else if (name.match(/\\*$/)) {\n          const match = (name === '*') ? '.' : name\n          const regex = new RegExp(`${match}`, 'g')\n        } */\n        return next(action)\n      }\n      addMiddleware(handler, position)\n      return () => removeMiddleware(handler, position)\n    },\n    /**\n     * Attach a handler function to an event and only trigger it once.\n     * @typedef {Function} Once\n     * @param  {String} name - Name of event to listen to\n     * @param  {Function} callback - function to fire on event\n     * @return {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * // Fire function only once per 'track'\n     * analytics.once('track', ({ payload }) => {\n     *   console.log('This is only triggered once when analytics.track() fires')\n     * })\n     *\n     * // Remove listener before it is called\n     * const listener = analytics.once('track', ({ payload }) => {\n     *   console.log('This will never get called b/c listener() is called')\n     * })\n     *\n     * // cleanup .once listener before it fires\n     * listener()\n     */\n    once: (name, callback) => {\n      if (!name || !isFunction(callback)) {\n        return false\n      }\n      if (name === EVENTS.bootstrap) {\n        throw new Error('.once disabled for ' + name)\n      }\n      const detachListener = instance.on(name, ({ payload }) => {\n        callback({ // eslint-disable-line\n          payload: payload,\n          instance: instance,\n          plugins: customPlugins,\n          abort: nonAbortable\n        })\n        // detach listener after its called once\n        detachListener()\n      })\n      return detachListener\n    },\n    /**\n     * Get data about user, activity, or context. Access sub-keys of state with `dot.prop` syntax.\n     * @typedef {Function} GetState\n     * @param  {string} [key] - dot.prop.path value of state\n     * @return {any}\n     *\n     * @example\n     *\n     * // Get the current state of analytics\n     * analytics.getState()\n     *\n     * // Get a subpath of state\n     * analytics.getState('context.offline')\n     */\n    getState: (key) => {\n      const state = store.getState()\n      if (key) return dotProp(state, key)\n      return Object.assign({}, state)\n    },\n    /*\n     * Emit events for other plugins or middleware to react to.\n     * @param  {Object} action - event to dispatch\n     */\n    dispatch: (action) => {\n      const actionData = isString(action) ? { type: action } : action\n      if (isReservedAction(actionData.type)) {\n        throw new Error('reserved action ' + actionData.type)\n      }\n      const _private = action._ || {}\n      // Dispatch actionStart\n      // const autoPrefixType = `${actionData.type.replace(/Start$/, '')}Start`\n\n      const dispatchData = {\n        ...actionData,\n        _: {\n          originalAction: actionData.type,\n          ..._private\n        }\n        // type: `${autoPrefixType}`\n      }\n      store.dispatch(dispatchData)\n    },\n    // Do not use. Will be removed. Here for Backwards compatiblity.\n    // Moved to analytics.plugins.enable\n    enablePlugin: plugins.enable,\n    /// Do not use. Will be removed. Here for Backwards compatiblity.\n    /// Moved to analytics.plugins.disable\n    disablePlugin: plugins.disable,\n    // Do not use. Will be removed. Here for Backwards compatiblity.\n    // New plugins api\n    plugins: plugins,\n    /**\n     * Storage utilities for persisting data.\n     * These methods will allow you to save data in localStorage, cookies, or to the window.\n     * @typedef {Object} Storage\n     * @property {GetItem} getItem - Get value from storage\n     * @property {SetItem} setItem - Set storage value\n     * @property {RemoveItem} removeItem - Remove storage value\n     *\n     * @example\n     *\n     * // Pull storage off analytics instance\n     * const { storage } = analytics\n     *\n     * // Get value\n     * storage.getItem('storage_key')\n     *\n     * // Set value\n     * storage.setItem('storage_key', 'value')\n     *\n     * // Remove value\n     * storage.removeItem('storage_key')\n     */\n    storage: {\n      /**\n       * Get value from storage\n       * @typedef {Function} GetItem\n       * @param {String} key - storage key\n       * @param {Object} [options] - storage options\n       * @return {Any}\n       *\n       * @example\n       *\n       * analytics.storage.getItem('storage_key')\n       */\n      getItem: storage.getItem,\n      /**\n       * Set storage value\n       * @typedef {Function} SetItem\n       * @param {String} key - storage key\n       * @param {any} value - storage value\n       * @param {Object} [options] - storage options\n       *\n       * @example\n       *\n       * analytics.storage.setItem('storage_key', 'value')\n       */\n      setItem: (key, value, options) => {\n        store.dispatch({\n          type: EVENTS.setItemStart,\n          key: key,\n          value: value,\n          options: options\n        })\n      },\n      /**\n       * Remove storage value\n       * @typedef {Function} RemoveItem\n       * @param {String} key - storage key\n       * @param {Object} [options] - storage options\n       *\n       * @example\n       *\n       * analytics.storage.removeItem('storage_key')\n       */\n      removeItem: (key, options) => {\n        store.dispatch({\n          type: EVENTS.removeItemStart,\n          key: key,\n          options: options\n        })\n      },\n    },\n    /*\n     * Set the anonymous ID of the visitor\n     * @param {String} anonymousId - anonymous Id to set\n     * @param {Object} [options] - storage options\n     *\n     * @example\n     *\n     * // Set anonymous ID\n     * analytics.setAnonymousId('1234567')\n     */\n    setAnonymousId: (anonymousId, options) => {\n      /* sets temporary in memory id. Not to be relied on */\n      // set(tempKey(ANONID), anonymousId)\n      instance.storage.setItem(CONSTANTS.ANON_ID, anonymousId, options)\n    },\n    /*\n     * Events exposed by core analytics library and all loaded plugins\n     * @type {Array}\n     */\n    events: {\n      core: coreEvents,\n      plugins: allPluginEvents,\n      // byType: (type) => {} @Todo grab logic from engine and give inspectable events\n    }\n  }\n  const enrichMiddleware = storeAPI => next => action => {\n    if (!action.meta) {\n      action.meta = enrichMeta()\n    }\n    return next(action)\n  }\n  const middlewares = parsedOptions.middlewares.concat([\n    enrichMiddleware,\n    /* Core analytics middleware */\n    dynamicMiddlewares(before), // Before dynamic middleware <-- fixed pageStart .on listener\n    /* Plugin engine */\n    middleware.plugins(instance, getPlugins, {\n      all: allSystemEvents,\n      plugins: allPluginEvents\n    }),\n    middleware.storage(storage),\n    middleware.initialize(instance),\n    middleware.identify(instance, storage),\n    /* after dynamic middleware */\n    dynamicMiddlewares(after)\n  ])\n\n  /* Initial analytics state keys */\n  const coreReducers = {\n    context: context,\n    user: user(storage),\n    page: page,\n    track: track,\n    plugins: pluginsMiddleware(getPlugins),\n    queue: queue\n  }\n\n  let composeEnhancers = compose\n  let composeWithGlobalDebug = compose\n  if (isBrowser && config.debug) {\n    const devTools = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\n    if (devTools) {\n      composeEnhancers = devTools({ trace: true, traceLimit: 25 })\n    }\n    composeWithGlobalDebug = function() {\n      if (arguments.length === 0) return Debug()\n      if (isObject(typeof arguments[0])) return composeWithDebug(arguments[0])\n      return composeWithDebug().apply(null, arguments)\n    }\n  }\n\n  const initialConfig = makeContext(config)\n\n  const intialPluginState = parsedOptions.pluginsArray.reduce((acc, plugin) => {\n    const { name, config, loaded } = plugin\n    const isEnabled = parsedOptions.pluginEnabled[name]\n    acc[name] = {\n      enabled: isEnabled,\n      // If plugin enabled & has no initialize method, set initialized to true, else false\n      initialized: (isEnabled) ? Boolean(!plugin.initialize) : false,\n      loaded: Boolean(loaded({ config })),\n      config\n    }\n    return acc\n  }, {})\n  \n  const initialState = {\n    context: initialConfig,\n    user: visitorInfo,\n    plugins: intialPluginState,\n    // Todo allow for more userland defined initial state?\n  }\n\n  /* Create analytics store! */\n  const store = createStore(\n    // register reducers\n    combineReducers({ ...coreReducers, ...customReducers }),\n    // set user defined initial state\n    initialState,\n    // register middleware & plugins used\n    composeWithGlobalDebug(\n      composeEnhancers(\n        applyMiddleware(...middlewares),\n      )\n    )\n  )\n\n  /* Supe up dispatch with callback promise resolver. Happens in enrichMeta */\n  function enhanceDispatch(fn) {\n    return function (event, resolver, callbacks) {\n      // console.log('original event', event)\n      const meta = enrichMeta(event.meta, resolver, ensureArray(callbacks))\n      // if (resolver) console.log('dispatch resolver', resolver)\n      // if (callbacks) console.log('dispatch callbacks', callbacks)\n      const newEvent = { ...event, ...{ meta: meta } }\n      // console.log('newEvent', newEvent)\n      return fn.apply(null, [ newEvent ])\n    }\n  }\n\n  // Automatically apply meta to dispatch calls\n  store.dispatch = enhanceDispatch(store.dispatch)\n\n  /* Synchronously call bootstrap & register Plugin methods */\n  const pluginKeys = Object.keys(customPlugins)\n\n  /* Bootstrap analytic plugins */\n  store.dispatch({\n    type: EVENTS.bootstrap,\n    plugins: pluginKeys,\n    config: initialConfig,\n    params: params,\n    user: visitorInfo,\n    initialUser,\n    persistedUser\n  })\n\n  const enabledPlugins = pluginKeys.filter((name) => parsedOptions.pluginEnabled[name])\n  const disabledPlugins = pluginKeys.filter((name) => !parsedOptions.pluginEnabled[name])\n \n  /* Register analytic plugins */\n  store.dispatch({\n    type: EVENTS.registerPlugins,\n    plugins: pluginKeys,\n    enabled: parsedOptions.pluginEnabled,\n  })\n\n  /* dispatch register for individual plugins */\n  parsedOptions.pluginsArray.map((plugin, i) => {\n    const { bootstrap, config, name } = plugin\n    if (bootstrap && isFunction(bootstrap)) {\n      bootstrap({ instance, config, payload: plugin })\n    }\n    /* Register plugins */\n    store.dispatch({\n      type: EVENTS.registerPluginType(name),\n      name: name,\n      enabled: parsedOptions.pluginEnabled[name],\n      plugin: plugin\n    })\n\n    /* All plugins registered initialize, is last loop */\n    if (parsedOptions.pluginsArray.length === (i + 1)) {\n      store.dispatch({\n        type: EVENTS.initializeStart,\n        plugins: enabledPlugins,\n        disabled: disabledPlugins\n      })\n    }\n  })\n\n  if (BROWSER) {\n    /* Watch for network events */\n    watch((offline) => {\n      store.dispatch({\n        type: (offline) ? EVENTS.offline : EVENTS.online,\n      })\n    })\n    /* Tick heartbeat for queued events */\n    heartBeat(store, getPlugins, instance)\n  }\n\n  function appendArguments(fn) {\n    return function () {\n      /* Get original args */\n      const args = Array.prototype.slice.call(arguments)\n      /* Create clone of args */\n      let newArgs = new Array(fn.length)\n      for (let i = 0; i < args.length; i++) {\n        newArgs[i] = args[i]\n      }\n      /* Append new arg to end */\n      newArgs[newArgs.length] = instance\n      // Set instance on extended methods\n      return fn.apply({ instance }, newArgs)\n    }\n  }\n\n  /* Return analytics instance */\n  return instance\n}\n\n// Duplicated strings\nconst before = 'before'\nconst after = 'after'\n\nexport default analytics\n\n/*\n * analytics.init exported for standalone browser build\n * CDN build exposes global _analytics variable\n *\n * Initialize instance with _analytics.init() or _analytics['default']()\n */\nexport { analytics as init }\n\n/*\n * analytics.Analytics exported for node usage\n *\n * Initialize instance with _analytics.init() or _analytics['default']()\n */\nexport { analytics as Analytics }\n/*\n * Core Analytic events. These are exposed for third party plugins & listeners\n * Use these magic strings to attach functions to event names.\n * @type {Object}\n */\nexport { EVENTS }\n\nexport { CONSTANTS }\n"], "names": ["OBJECT", "process", "<PERSON><PERSON><PERSON><PERSON>", "method", "s", "char<PERSON>t", "slice", "<PERSON><PERSON>", "window", "name", "navigator", "userAgent", "includes", "text", "bind", "lower", "val", "toLowerCase", "upper", "x", "constructor", "ctorName", "Object", "prototype", "toString", "call", "getTypeName", "type", "kind", "typeOf", "isString", "obj", "isObjectLike", "getPrototypeOf", "proto", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNull", "getType", "message", "n", "isNaN", "isNumber", "stackTraceLimit", "isError", "typeName", "element", "nodeName", "toUpperCase", "isNodeType", "isEl", "fn", "boundArgs", "replace", "e", "errorType", "TypeError", "SyntaxError", "bind<PERSON><PERSON><PERSON>", "isElement", "KEY", "PREFIX", "globalContext", "self", "global", "this", "key", "FUNC", "UNDEF", "base", "ACTION_INIT", "ACTION_TEST", "Math", "random", "$$observable", "Symbol", "observable", "msg", "createStore", "reducer", "preloadedState", "enhancer", "undefined", "Error", "currentReducer", "currentState", "currentListeners", "nextListeners", "isDispatching", "ensureCanMutateNextListeners", "getState", "subscribe", "listener", "isSubscribed", "push", "index", "indexOf", "splice", "dispatch", "action", "isObject", "listeners", "i", "length", "replaceReducer", "nextReducer", "outerSubscribe", "observer", "observeState", "next", "unsubscribe", "getUndefinedStateErrorMessage", "actionType", "compose", "funcs", "arg", "reduce", "a", "b", "applyMiddleware", "chain", "store", "middlewareAPI", "map", "middleware", "ANON_ID", "USER_ID", "USER_TRAITS", "LIB_NAME", "ID", "ANONID", "coreEvents", "nonEvents", "acc", "curr", "registerPluginType", "pluginReadyType", "utmRegex", "propRegex", "traitRegex", "initializeMiddleware", "instance", "setItem", "storage", "EVENTS", "bootstrap", "params", "user", "persisted<PERSON>ser", "initialUser", "isKnownId", "userId", "anonymousId", "traits", "paramsArray", "keys", "an_uid", "an_event", "groupedParams", "match", "cleanName", "campaign", "props", "raw", "setTimeout", "identify", "track", "userReducer", "state", "setItemEnd", "assign", "reset", "for<PERSON>ach", "removeItem", "getPersistedUserData", "getItem", "tempKey", "identifyMiddleware", "options", "remove", "uuid", "currentId", "currentTraits", "userIdChanged", "old", "new", "stack", "<PERSON><PERSON><PERSON><PERSON>", "id", "payload", "isFunction", "waitFor<PERSON><PERSON>y", "data", "predicate", "timeout", "Promise", "resolve", "reject", "queue", "then", "_", "abort", "reason", "processQueue", "getPlugins", "abortedCalls", "pluginMethods", "plugins", "context", "offline", "actions", "pipeline", "item", "plugin", "loaded", "processIndex", "requeue", "requeueIndex", "processAction", "currentPlugin", "currentMethod", "retVal", "enrichedPayload", "hasOwnProperty", "enrich", "isAborted", "meta", "rid", "config", "pluginEvent", "called", "from", "reQueueActions", "filter", "processEvent", "allPlugins", "allMatches", "isStartEvent", "endsWithStartRegex", "abortable", "exact", "pluginName", "during", "makeArgs", "abortablePlugins", "otherPlugin", "event", "abortF", "caller", "pluginsToAbort", "isArray", "JSON", "stringify", "abortFunction", "notAbortableError", "formatPayload", "argumentFactory", "queueData", "thing", "methodName", "addToQueue", "scoped", "curScope", "namespaced", "p", "count", "curScopeData", "actionName", "getNameSpacedAction", "sub", "join", "validate<PERSON><PERSON><PERSON>", "currentAct", "pname", "otherPlug", "getConfig", "returnValue", "scopedPayload", "payloads", "promise", "lastLoop", "currentActionValue", "payloadValue", "shouldAbort", "abortDispatch", "funcArgs", "merged", "nameSpaceEvent", "bootstrapRegex", "readyRegex", "resolvedAction", "originalAction", "endAction", "shouldAbortAll", "abortEvent", "pluginState", "pluginData", "getPluginFunctions", "arr", "concat", "getEventNames", "eventType", "namespace", "postFix", "pluginsCount", "Array", "split", "pluginMiddleware", "systemEvents", "isReady", "updatedAction", "enablePlugin", "initializeStart", "disabled", "fromEnable", "disablePlugin", "initializeEnd", "pluginsArray", "allRegisteredPlugins", "completed", "failed", "waitForPluginsToLoad", "d", "events", "all", "calls", "ready", "test", "eventsInfo", "pluginObject", "originalType", "updatedType", "activePlugins", "settings", "fromCallOptions", "isBoolean", "enabled", "info", "initialized", "allActivePluginKeys", "core", "word", "beforeFuncs", "duringFuncs", "afterFuncs", "beforeNS", "duringNS", "afterNS", "before", "after", "getAllMatchingCalls", "actionBefore", "actionDuring", "actionAfter", "<PERSON><PERSON><PERSON><PERSON>", "runPlugins", "updated", "storageMiddleware", "DynamicMiddleware", "addMiddleware", "middlewares", "position", "_this", "removeMiddleware", "findIndex", "dynamicMiddlewares", "act", "createReducer", "newState", "getNameFromEventType", "isEnabled", "Boolean", "initialize", "togglePluginStatus", "baseName", "substring", "status", "pluginKey", "serialize", "parse", "err", "initialState", "last", "history", "trackReducer", "trackEvent", "properties", "queueReducer", "action<PERSON>hain", "hashRegex", "url<PERSON><PERSON>", "url", "matches", "exec", "osName", "referrer", "locale", "timeZone", "getPageData", "pageData", "document", "title", "location", "innerWidth", "innerHeight", "hash", "search", "canonical", "tag", "tags", "getElementsByTagName", "getAttribute", "canonicalUrl", "href", "currentUrl", "page", "path", "width", "height", "viewData", "os", "appVersion", "getOSNameBrowser", "getBrowserLocale", "getTimeZone", "sessionId", "app", "version", "debug", "onLine", "library", "timezone", "online", "excludeItems", "listen", "func", "toAdd", "ev", "watch", "cb", "Debug", "set", "origDispatch", "composeWithDebug", "apply", "arguments", "ensureArray", "singleOrArray", "generateMeta", "possibleCallbacks", "resolver", "callback", "arg<PERSON><PERSON>y", "args", "get<PERSON>allback", "ts", "Date", "getTime", "analytics", "customReducers", "reducers", "parsedOptions", "NAMESPACE", "ERROR_URL", "definedEvents", "k", "pluginEnabled", "methods", "c", "newArgs", "allEvents", "allEventsUnique", "Set", "get", "getUserProp", "getUserPropFunc", "customPlugins", "allPluginEvents", "sort", "uniqueEvents", "allSystemEvents", "nonAbortable", "visitorInfo", "an_aid", "enable", "disable", "readyCalled", "opts", "resolvedId", "identifyStart", "previousId", "eventName", "trackStart", "pageStart", "dotProp", "resetStart", "on", "startRegex", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handler", "once", "detachListener", "actionData", "dispatchData", "setItemStart", "removeItemStart", "setAnonymousId", "CONSTANTS", "storeAPI", "enrichMeta", "coreReducers", "pluginsMiddleware", "composeEnhancers", "composeWithGlobalDebug", "devTools", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "trace", "traceLimit", "initialConfig", "current", "makeContext", "intialPluginState", "reducerKeys", "finalReducers", "shapeAssertionError", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "REDUCER", "assertReducerShape", "has<PERSON><PERSON>ed", "nextState", "previousStateForKey", "nextStateForKey", "errorMessage", "combineReducers", "callbacks", "newEvent", "pluginKeys", "enabledPlugins", "disabledPlugins", "registerPlugins", "setInterval", "heartBeat"], "mappings": "inBASaA,EAAS,SAFG,6BAwCsBC,YAelCC,EAvDY,6BA2EzB,WAAcC,EAAQC,GACpB,SAASC,OAAO,GAAGF,KAAYC,EAAEE,MAAM,GA5EhB,0BA+DiCC,KAMlCL,GAA6B,WAAhBM,OAAOC,MArEnB,oCAqEiDC,IAAkCA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,gBAU5MC,EAAKC,KAAK,KAAM,eACxBC,EAAQF,EAAKC,KAAK,KAAM,0BAiBNE,EAAKC,sBAC3B,MAlB4B,SAOFD,GAC1B,SAAWA,GAAaE,EAlFN,QAJE,4BA+VGC,GACvB,SAAkBA,EAAEC,aAAeD,EAAEC,YAAYX,KAAO,KAzQvBY,CAASL,GAAOM,OAAOC,UAAUC,SAASC,KAAKT,GAAKV,MAAM,GAAI,GASlFoB,CAAYV,GAEzB,SAAuBD,EAAMY,GAAQA,aAUvBC,EAAMZ,GACpB,kBAAsBY,QAQEC,EAAOf,KAAK,KAzHd,YAgIXgB,EAAWD,EAAOf,KAAK,KA/Hd,UAsIKe,EAAOf,KAAK,KArId,mBAoJAe,EAAOf,KAAK,KAnJd,WAID,WA8JCK,GACrB,cAAOA,aA0DgBY,GACvB,aAU2BA,GAC3B,WAxOoB,oBAwO4B,OAARA,GAXnCC,CAAaD,GAAM,SAGxB,IADA,MAAYA,EAC4B,OAAjCT,OAAOW,eAAeC,IAC3BA,EAAQZ,OAAOW,eAAeC,GAGhC,cAAcD,eAAeF,KAASG,aA0MrBC,EAASC,GAC1B,GAAqB,oBAAYC,EAAOD,GAAQ,SAEhD,GAAIA,eAA0B,SAC9B,MAAiBE,EAAQ,MAAY,KAErC,YAnCsBnB,GACtB,2BAA8BW,EAASX,EAAEoB,UAAYpB,EAAEC,sBAvNhCoB,GACvB,MA1LoB,WA0LbF,EAAQE,KAAkBC,MAAMD,GAsN+BE,CAASvB,EAAEC,YAAYuB,iBAkCzFC,CAAQR,GACV,KAAOA,GAAO,CACZ,GAAIE,EAAQF,KAAWS,EACrB,SAEAT,EAAQd,OAAOW,eAAeG,uBA8JZU,EAASnB,GACjC,MAAamB,sBAA8BA,0BAC3C,UAAcnB,WAUWmB,EAASnB,GAClC,gBADkCA,IAAAA,EAAO,IAClCmB,GAAWA,EAAQC,WAAapB,EAAKqB,cAVrCC,CAAWH,EAASnB,GADAuB,aAcXC,oCAChB,kBACE,sDAAsBC,gBCzmBAhD,GACxB,IACE,0BAA0BA,EAAEiD,QAAQ,MAAO,YACpCC,GACP,4XDoJoBzB,EAAOf,KAAK,KArJd,UA4bKyC,EAAUzC,KAAK,KAAM0C,WAEnBD,EAAUzC,KAAK,KAAM2C,aAiL5BC,EAASC,EA9lBX,QAqmBID,EAASC,EAnmBX,UA0mBCD,EAASC,EA3mBX,SAknBGD,EAASC,EAhnBX,cE5BTC,EAAMC,aAENC,gBAAiC9D,GAAU+D,KAAKA,OAASA,MAAQA,sBAA4B/D,GAAUgE,OAAMA,SAAaA,QAAUA,aAAAA,EAAWC,WAWxIC,GAClB,SAAqBN,GAAKM,cASRA,EAAK9B,GACvB,SAAqBwB,GAAKM,GAAO9B,aAOZ8B,YACAN,GAAKM,KA3BTN,KACjBE,EAAcF,GAAO,QCVVO,EAAO,WACPC,EAAQ,YAGfC,EAAO,WACAC,EAAcD,EAAO,OACrBE,EAAcF,EAAOG,KAAKC,SAASjD,SAAS,ICFnDkD,iBAAgC,yBAAcC,SAAWR,GAAQQ,OAAOC,YAAe,eAAvD,GA2BhCC,EAAM,OAASV,WACGW,EAAYC,EAASC,EAAgBC,SAM3D,UALWD,IAAmBb,UAAec,IAAab,IACxDa,EAAWD,EACXA,OAAiBE,UAGRD,IAAab,EAAO,CAC7B,UAAWa,IAAad,EACtB,UAAUgB,MAAM,WAAaN,GAG/B,OAAOI,EAASH,EAATG,CAAsBF,EAASC,GAGxC,UAAWD,IAAYZ,EACrB,UAAUgB,MD7CS,UC6CON,GAG5B,IAAIO,EAAiBL,EACjBM,EAAeL,EACfM,EAAmB,GACnBC,EAAgBD,EAChBE,GAAgB,EAEpB,SAASC,IACHF,IAAkBD,IACpBC,EAAgBD,EAAiBhF,SASrC,SAASoF,IACP,OAAOL,EA0BT,SAASM,EAAUC,GACjB,UAAWA,IAAazB,EACtB,UAAUgB,MAAM,WAAaN,GAG/B,IAAIgB,GAAe,EAKnB,OAHAJ,IACAF,EAAcO,KAAKF,cAGjB,GAAKC,EAAL,CAIAA,GAAe,EAEfJ,IACA,IAAMM,EAAQR,EAAcS,QAAQJ,GACpCL,EAAcU,OAAOF,EAAO,KA6BhC,SAASG,EAASC,GAehB,IAAKC,EAASD,GACZ,UAAUhB,MAAM,cAGlB,UAAWgB,EAAOxE,OAASyC,EACzB,UAAUe,MAAM,WAAaf,GAG/B,GAAIoB,EACF,UAAUL,MAAM,uBAGlB,IACEK,GAAgB,EAChBH,EAAeD,EAAeC,EAAcc,GAF9C,QAIEX,GAAgB,EAIlB,IADA,IAAMa,EAAYf,EAAmBC,EAC5Be,EAAI,EAAGA,EAAID,EAAUE,OAAQD,KAEpCV,EADiBS,EAAUC,MAI7B,OAAOH,EAkET,OAFAD,EAAS,CAAEvE,KAAM2C,QAGf4B,SAAAA,EACAP,UAAAA,EACAD,SAAAA,EACAc,eAzDF,SAAwBC,GACtB,UAAWA,IAAgBtC,EACzB,UAAUgB,MAAM,eAAoBN,GAGtCO,EAAiBqB,EACjBP,EAAS,CAAEvE,KAAM2C,OAoDhBI,GA3CH,iBACQgC,EAAiBf,EACvB,UASEA,mBAAUgB,GACR,GAAwB,iBAAbA,EACT,UAAUnD,UAAU,mBAGtB,SAASoD,IACHD,EAASE,MACXF,EAASE,KAAKnB,KAMlB,OAFAkB,IAEO,CAAEE,YADWJ,EAAeE,OAIpClC,cACC,kBC1OR,SAASqC,EAA8B7C,EAAKiC,GAC1C,IAAMa,EAAab,GAAUA,EAAOxE,KAGpC,MAAQ,WAFYqF,GAAcA,EAAWxF,YAAe,KAEpD,WAAyC0C,EAAM,YAAcE,WCG/C6C,QAAWC,2BACjC,OAAqB,IAAjBA,EAAMX,gBACDY,UAAOA,GAGK,IAAjBD,EAAMX,OACDW,EAAM,GAGRA,EAAME,OAAO,SAACC,EAAGC,4BAAmBD,EAAEC,sDCFvBC,oBACtB,gBAAQzC,mBAAiBC,EAASC,EAAgBC,GAChD,IAEIuC,EAFEC,EAAQ3C,EAAYC,EAASC,EAAgBC,GAC/CiB,EAAWuB,EAAMvB,SAGfwB,EAAgB,CACpBhC,SAAU+B,EAAM/B,SAChBQ,SAAU,SAACC,UAAWD,EAASC,KAKjC,OAHAqB,EAAQ,iBAAYG,IAAI,SAAAC,UAAcA,EAAWF,UAI5CD,GACHvB,SAJFA,EAAWe,eAAWO,EAAXP,CAAkBQ,EAAMvB,kBCf1B2B,EAAUhE,YAKViE,EAAUjE,YAKVkE,EAAclE,qECvBdmE,EAAW,YAEXC,EAAK,SAELC,EAAS,cCHTC,EAAa,CAMxB,YAIA,SAIA,WAIA,kBAIA,aAIA,gBAIA,QAKA,aAKA,QAKA,WAQA,YAKA,OAIA,UAIA,cAQA,aAKA,QAIA,WAIA,eAQA,gBAKA,WAIA,cAIA,kBAIA,gBAOA,kBAIA,eAIA,gBAYA,SAIA,UAQA,eAKA,UAIA,aAIA,iBAKA,kBAKA,aAIA,gBAIA,qBAIWC,EAAY,CAAC,OAAQ,SAAU,SAAU,YAOvCD,EAAWf,OAAO,SAACiB,EAAKC,GAErC,OADAD,EAAIC,GAAQA,EACLD,GAPY,CACnBE,mBAAoB,SAAC9H,2BAA2BA,GAChD+H,gBAAiB,SAAC/H,kBAAkBA,KC5LhCgI,EAAW,QACXC,EAAY,YACZC,EAAa,sBAGKC,EAAqBC,GAC3C,IAAQC,EAAYD,EAASE,QAArBD,QACR,gBAAOrB,mBAASZ,mBAAQV,GAEtB,GAAIA,EAAOxE,OAASqH,EAAOC,UAAW,CACpC,IAAQC,EAA6C/C,EAA7C+C,OAAQC,EAAqChD,EAArCgD,KAAMC,EAA+BjD,EAA/BiD,cAAeC,EAAgBlD,EAAhBkD,YAC/BC,EAAYF,EAAcG,SAAWJ,EAAKI,OAE5CH,EAAcI,cAAgBL,EAAKK,aACrCV,EAAQjB,EAASsB,EAAKK,aAGnBF,GACHR,EAAQhB,EAASqB,EAAKI,QAGpBF,EAAYI,QACbX,EAAQf,OACHuB,GAAaF,EAAcK,OAAUL,EAAcK,OAAS,GAC7DJ,EAAYI,SAUnB,IAAMC,EAAcpI,OAAOqI,KAAKxD,EAAO+C,QACvC,GAAIQ,EAAYnD,OAAQ,CACtB,IAAQqD,EAAqBV,EAArBU,OAAQC,EAAaX,EAAbW,SACVC,EAAgBJ,EAAYtC,OAAO,SAACiB,EAAKnE,GAE7C,GAAIA,EAAI6F,MAAMtB,IAAavE,EAAI6F,MAAM,cAAe,CAClD,IAAMC,EAAY9F,EAAIb,QAAQoF,EAAU,IAExCJ,EAAI4B,SAD2B,aAAdD,EAA4B,OAASA,GAC9Bd,EAAOhF,GAQjC,OANIA,EAAI6F,MAAMrB,KACZL,EAAI6B,MAAMhG,EAAIb,QAAQqF,EAAW,KAAOQ,EAAOhF,IAE7CA,EAAI6F,MAAMpB,KACZN,EAAIoB,OAAOvF,EAAIb,QAAQsF,EAAY,KAAOO,EAAOhF,IAE5CmE,GACN,CACD4B,SAAU,GACVC,MAAO,GACPT,OAAQ,KAGVhC,EAAMvB,YACJvE,KAAMqH,EAAOE,OACbiB,IAAKjB,GACFY,EACCF,EAAS,CAAEL,OAAQK,GAAW,KAIhCA,GAEFQ,WAAW,kBAAMvB,EAASwB,SAAST,EAAQE,EAAcL,SAAS,GAIhEI,GAEFO,WAAW,kBAAMvB,EAASyB,MAAMT,EAAUC,EAAcI,QAAQ,GAI9D5I,OAAOqI,KAAKG,EAAcG,UAAU1D,QACtCkB,EAAMvB,SAAS,CACbvE,KAAMqH,EAAOiB,SACbA,SAAUH,EAAcG,YAKhC,OAAOpD,EAAKV,eCnFQoE,EAAYxB,GAClC,gBAAqByB,EAAYrE,GAE/B,YAFmBqE,IAAAA,EAAQ,aAAIrE,IAAAA,EAAS,IAEpCA,EAAOxE,OAASqH,EAAOyB,WAAY,CAErC,GAAItE,EAAOjC,MAAQ2D,EACjB,YAAY2C,EAAU,CAAEhB,YAAarD,EAAO/D,QAG9C,GAAI+D,EAAOjC,MAAQ4D,EACjB,YAAY0C,EAAU,CAAEjB,OAAQpD,EAAO/D,QAI3C,OAAQ+D,EAAOxE,MACb,KAAKqH,EAAOqB,SACV,OAAO/I,OAAOoJ,OAAO,GAAIF,EAAO,CAC9BjB,OAAQpD,EAAOoD,OACfE,YACKe,EAAMf,OACNtD,EAAOsD,UAGhB,KAAKT,EAAO2B,MAOV,MAJA,CAAE7C,EAASD,EAASE,GAAc6C,QAAQ,SAAC1G,GAEzC6E,EAAQ8B,WAAW3G,KAEd5C,OAAOoJ,OAAO,GAAIF,EAAO,CAC9BjB,OAAQ,KAERC,YAAa,KACbC,OAAQ,KAEZ,QACE,OAAOe,aAKCM,EAAqB/B,GACnC,MAAO,CACLQ,OAAQR,EAAQgC,QAAQjD,GACxB0B,YAAaT,EAAQgC,QAAQlD,GAC7B4B,OAAQV,EAAQgC,QAAQhD,QAIfiD,GAAU,SAAC9G,SAAQL,WAA2BK,YCjDnC+G,GAAmBpC,GACzC,MAAyCA,EAASE,QAA1CD,IAAAA,QAAS+B,IAAAA,WAAYE,IAAAA,QAC7B,gBAAOtD,mBAASZ,mBAAQV,GACtB,IAAQoD,EAA4BpD,EAA5BoD,OAAQE,EAAoBtD,EAApBsD,OAAQyB,EAAY/E,EAAZ+E,QAcxB,GAZI/E,EAAOxE,OAASqH,EAAO2B,QAEzB,CAAE7C,EAASC,EAAaF,GAAU+C,QAAQ,SAAC1G,GAEzC2G,EAAW3G,KAEb,CAAE+D,EAAIC,EAAQ,UAAW0C,QAAQ,SAAC1G,GAEhCiH,EAAOH,GAAQ9G,OAIfiC,EAAOxE,OAASqH,EAAOqB,SAAU,CAE9BU,EAAQlD,IACXiB,EAAQjB,EAASuD,KAGnB,IAAMC,EAAYN,EAAQjD,GACpBwD,EAAgBP,EAAQhD,IAAgB,GAE1CsD,GAAcA,IAAc9B,GAC9B9B,EAAMvB,SAAS,CACbvE,KAAMqH,EAAOuC,cACbC,IAAK,CACHjC,OAAQ8B,EACR5B,OAAQ6B,GAEVG,IAAK,CACHlC,OAAAA,EACAE,OAAAA,GAEFyB,QAASA,IAKT3B,GACFT,EAAQhB,EAASyB,GAIfE,GACFX,EAAQf,OACHuD,EACA7B,IAIT,OAAO5C,EAAKV,MC1DhB,IAAMuF,GAAQ,GAEd,SAASC,GAAYC,EAAIC,GACnBH,GAAME,IAAOE,EAAWJ,GAAME,MAEhCF,GAAME,GAAIC,UACHH,GAAME,aCFOG,GAAaC,EAAMC,EAAWC,GACpD,WAAWC,QAAQ,SAACC,EAASC,GAC3B,OAAIJ,IACKG,EAAQJ,GAGbE,EAAU,EACLG,OAAYL,GAAMM,OAAO,SAUzBH,QAAQ,SAAAC,UAAWhC,WAAWgC,EAP1B,MAAIG,KAAK,SAAAC,GACpB,OAAOT,GAAaC,EAAMC,EAAWC,EAAU,IAAIK,KAAKH,EAASC,OCfvE,SAASI,GAAMC,GACb,MAAO,CAAED,MAAOC,YAGFC,GAAalF,EAAOmF,EAAY/D,GAC9C,IAAMgE,EAAe,GACfC,EAAgBF,MACoBnF,EAAM/B,WAAxCqH,IAAAA,QAAkBT,IAAAA,MAAOnD,IAAAA,KAGjC,MAHiB6D,QACSC,SAEVX,GAASA,EAAMY,SAAWZ,EAAMY,QAAQ3G,OAAQ,CAC9D,IAAM4G,EAAWb,EAAMY,QAAQ9F,OAAO,SAACiB,EAAK+E,EAAMrH,GAShD,OARiBgH,EAAQK,EAAKC,QAAQC,QAEpCjF,EAAIpI,QAAQ6F,KAAKsH,GACjB/E,EAAIkF,aAAazH,KAAKC,KAEtBsC,EAAImF,QAAQ1H,KAAKsH,GACjB/E,EAAIoF,aAAa3H,KAAKC,IAEjBsC,GACN,CACDkF,aAAc,GACdtN,QAAS,GACTuN,QAAS,GACTC,aAAc,KAGhB,GAAIN,EAASI,cAAgBJ,EAASI,aAAahH,OAAQ,CACzD4G,EAASI,aAAa3C,QAAQ,SAACtE,GAC7B,IAAMoH,EAAgBpB,EAAMY,QAAQ5G,GAG9BqH,EAAgBD,EAAcL,OAC9BO,EAAgBF,EAAc7B,QAAQlK,KACtCxB,EAAS2M,EAAca,GAAeC,GAC5C,GAAIzN,GAAU2L,EAAW3L,GAAS,CAIhC,IAII0N,EAJEC,EAgEhB,SAAgBjC,EAAc1C,GAC5B,gBADc0C,IAAAA,EAAU,aAAI1C,IAAAA,EAAO,IAC5B,CAAElB,EAAIC,GAASd,OAAO,SAACiB,EAAKnE,GAKjC,OAJI2H,EAAQkC,eAAe7J,IAAQiF,EAAKjF,IAASiF,EAAKjF,KAAS2H,EAAQ3H,KAErEmE,EAAInE,GAAOiF,EAAKjF,IAEXmE,GACNwD,GAvE6BmC,CAAON,EAAc7B,QAAS1C,GAKhD8E,EAAYpB,EAAaiB,EAAgBI,KAAKC,KAEpD,IAAKF,IAEHJ,EAAS1N,EAAO,CACd0L,QAASiC,EACTM,OAAQrB,EAAQY,GAAeS,OAC/BvF,SAAAA,EACA4D,MAAAA,OAGYrG,EAASyH,IAAWA,EAAOpB,MAEvC,YADAI,EAAaiB,EAAgBI,KAAKC,MAAO,GAM7C,IAAKF,EAAW,CACd,IAAMI,EAAiBT,MAAiBD,EACxClG,EAAMvB,cACD4H,GACHnM,KAAM0M,EAEN7B,EAAG,CACD8B,OAAQD,EACRE,KAAM,qBAQhB,IAAMC,EAAiBlC,EAAMY,QAAQuB,OAAO,SAACrM,EAAO2D,GAElD,QAASoH,EAASI,aAAavH,QAAQD,KAIzCuG,EAAMY,QAAUsB,QC2DPE,mBACb1C,IAAAA,KACA7F,IAAAA,OACA0C,IAAAA,SACA2B,IAAAA,MACAmE,IAAAA,WACAC,IAAAA,WACAnH,IAAAA,MACAuB,IAAAA,WAEA,IAAQ+D,EAAqBvC,EAArBuC,QAASC,EAAYxC,EAAZwC,QACX7M,EAASgG,EAAOxE,KAChBkN,EAAe1O,EAAO4J,MAAM+E,IAG9BC,EAAY/C,EAAKgD,MAAMrH,IAAI,SAACxG,GAC9B,OAAOA,EAAE8N,aAIPJ,IACFE,EAAYH,EAAWM,OAAOvH,IAAI,SAACxG,GACjC,OAAOA,EAAE8N,cAKb,IAAME,EA6WR,SAAyBtG,EAAUuG,GAEjC,gBAAiBjJ,EAAQkH,EAAQgC,GAC/B,IAAQjB,EAAiBf,EAAjBe,OAAQ3N,EAAS4M,EAAT5M,KACZN,EAAYM,MAAQ0F,EAAOxE,KAC3B0N,IACFlP,EAASkP,EAAYC,OAGvB,IAAMC,EAAUpJ,EAAOxE,KAAKoI,MAAM+E,IAetC,SAAuBG,EAAY9O,EAAQiP,EAAkBC,EAAalJ,GACxE,gBAAiBuG,EAAQK,GACvB,IAAMyC,EAAUH,EAAeA,EAAY5O,KAAOwO,EAC9CQ,EAAkB1C,GAAW2C,GAAQ3C,GAAYA,EAAUqC,EAC/D,GAAIC,MACFI,EAAkB1C,GAAW2C,GAAQ3C,GAAYA,EAAU,CAACkC,IACxCrO,SAASqO,IAAyC,IAA1BQ,EAAelJ,QACzD,UAAUpB,gBAAgBhF,qBAAyB8O,cAAsBU,KAAKC,UAAUH,mBAG5F,YACKtJ,GACHsG,MAAO,CACLC,OAAQA,EACRK,QAAS0C,EACTD,OAAQrP,EACRqM,EAAGgD,MA9BHK,CAAcpP,EAAMN,EAAQiP,EAAkBC,EAAalJ,GAoCnE,SAA2BA,EAAQhG,GACjC,kBACE,UAAUgF,MAAMgB,EAAOxE,KAAO,4CAA8CxB,IArCxE2P,CAAkB3J,EAAQhG,GAE9B,MAAO,CAGL0L,QAASkE,GAAc5J,GACvB0C,SAAUA,EACVuF,OAAQA,GAAU,GAClB3B,MAAO8C,IAhYMS,CAAgBnH,EAAUkG,GAIrCkB,EAAYjE,EAAKgD,MAAM5H,OAAO,SAACiB,EAAK6H,GACxC,IAAQjB,EAA2BiB,EAA3BjB,WAAYkB,EAAeD,EAAfC,WAChBC,GAAa,EAUjB,OARKD,EAAWpG,MAAM,gBAAmBoG,EAAWpG,MAAM,YACxDqG,GAAcrD,EAAQkC,GAAY3B,QAGhCN,EAAQC,SAAYkD,EAAWpG,MAAM,4BACvCqG,GAAa,GAEf/H,KAAO4G,GAAgBmB,EAChB/H,GACN,IAnCF,uBAsCsB2D,EAAKgD,MAAM5H,gBAAciJ,EAAQ/H,EAAMhC,OAC5D,IAAQ2I,EAAe3G,EAAf2G,WAD0D,uBAE3CoB,iBAAjBC,gBAmDN,OAAOnE,QAAQC,QAAQkE,uBAlDnBtE,EAAKuE,YAAcvE,EAAKuE,WAAWtB,0BACTjD,EAAKuE,WAAWtB,GAAY7H,gBAAciB,EAAKmI,EAAGC,8BAEjDpI,iBAArBqI,GACN,OAAKF,EAAErQ,QAAW2L,EAAW0E,EAAErQ,SAyYvC,SAAwBwQ,EAAY1B,GAClC,IAAMpO,EAAO+P,GAAoBD,GAEjC,GADyC9P,GAASA,EAAKJ,OAASwO,EAC1B,CACpC,IAAM4B,EAAMD,GAAoB/P,EAAKV,QAErC,UAAUgF,MAAM,CAAE8J,EAAa,6BAA+B0B,EAC5D,gCACO9P,EAAKV,YAHG0Q,EAAO,MAAQA,EAAI1Q,OAAS,WAGP8O,uBAA+B0B,GAClEG,KAAK,QA7YJC,CAAeP,EAAEL,WAAYK,EAAEvB,4BAqBbuB,EAAErQ,OAAO,CACzB0L,QAAS6E,EACT7H,SAAAA,EACA4D,OAtBgBuE,EAsBAN,EAtBYO,EAsBEhC,EAtBKiC,EAsBOV,EAAEvB,oBArB3BvC,EAAQK,GAMvB,YACKiE,GACHvE,MAAO,CACLC,OAAQA,EACRK,QAASA,GAAW,CAACkE,GACrBzB,OAAQrP,EACRoO,KAXa2C,GAAaD,OAqBhC7C,OAAQ+C,GAAUX,EAAEvB,WAAYlC,EAAS4B,GACzC5B,QAASA,mBALL/L,GAON,IAAMoQ,EAAchL,EAASpF,GAAOA,EAAM,GAC1C,OAAOmL,QAAQC,aACVsE,EACAU,OAnCIV,EAMT,IAAkBM,EAAYC,EAAOC,IAVX,oCAyCzB/E,QAAQC,QAAQjG,mBAzCbkL,GA4CNf,EAASrB,GAAcoC,IAGvBf,EAASrB,GAAc9I,sCAnDJ,oCAsDpBgG,QAAQC,QAAQ,oBAtDbkF,0BA0DuBtF,EAAKgD,MAAM5H,gBAAcmK,EAASjJ,EAAMhC,OACnE,IAAMkL,EAAWxF,EAAKgD,MAAMzI,SAAYD,EAAI,EACpC2I,EAAe3G,EAAf2G,WACFtB,EAAgBgB,EAAWM,GAHwC,uBAIxCsC,iBAA3BE,GAEN,IAAIC,EAAgBJ,EAASrC,GAAeqC,EAASrC,GAAc,GAMnE,GAJIJ,IACF6C,EAAeD,GAGbE,GAAYD,EAAczC,GAS5B,OAPA2C,GAAc,CACZ5F,KAAM0F,EACNvR,OAAAA,EACA0I,SAAAA,EACAoG,WAAAA,EACAxH,MAAAA,IAEK0E,QAAQC,QAAQqF,GAEzB,GAAIE,GAAYF,EAAoBxC,GAWlC,OATIuC,GACFI,GAAc,CACZ5F,KAAMyF,EACNtR,OAAAA,EACA0I,SAAAA,EAEApB,MAAAA,IAGG0E,QAAQC,QAAQqF,GAGzB,GAAIxB,EAAUlC,eAAekB,KAAyC,IAA1BgB,EAAUhB,GAYpD,OAVAxH,EAAMvB,SAAS,CACbvE,aACA0L,OAAQ4B,EACRpD,QAAS6F,EAETlF,EAAG,CACD8B,eACAC,KAAM,oBAGHpC,QAAQC,QAAQqF,GAmBzB,IAAMI,EAAW1C,EAASmC,EAASrC,GAAaN,EAAWM,IApEc,uBAyEvDtB,EAAcxN,GAAQ,CAEtCsM,MAAOoF,EAASpF,MAEhBZ,QAAS6F,EACT7I,SAAAA,EACAuF,OAAQ+C,GAAUlC,EAAYlC,EAAS4B,GACvC5B,QAASA,mBAPL/L,GAUN,IAAMoQ,EAAchL,EAASpF,GAAOA,EAAM,GACpC8Q,OACDL,EACAL,GAGCC,EAAgBC,EAASrC,GAC/B,GAAI0C,GAAYN,EAAepC,GAE7B2C,GAAc,CACZ5F,KAAMqF,EACNlR,OAAAA,EACA0I,SAAAA,EACAoG,WAAAA,EACAxH,MAAAA,QAEG,CACL,IAAMsK,EAAoB5R,MAAU8O,GACf8C,EAAehI,MAAM,OAAS,IAAIxD,OACrC,IAAMpG,EAAO4J,MAAMiI,MAAoB7R,EAAO4J,MAAMkI,KAGpEpJ,EAAS3C,cAFe2I,EAAgBiD,EAASJ,GAI/C/P,KAAMoQ,EACNvF,EAAG,CACD8B,OAAQyD,EACRxD,KAAM,gBAMd,OAAOpC,QAAQC,QAAQ0F,OApHI,oCAqH1B3F,QAAQC,QAAQjG,mBArHb+L,GAwHN,KAAK/R,EAAO4J,MAAM+E,KACb3O,EAAO4J,MAAM,oBAGb5J,EAAO4J,MAAMkI,KACb9R,EAAO4J,MAAMiI,KACb7R,EAAO4J,MAAM,YACb5J,EAAO4J,MAAM,mBAChB,CASA,GARIf,EAAO+D,QAAQnM,SAAST,GAQxB+R,EAAe1F,GAAK0F,EAAe1F,EAAE2F,iBAAmBhS,EAE1D,OAAO+R,EAGT,IAAIE,OACCF,EACA,CACD1F,EAAG,CACD2F,eAAgBD,EAAevQ,KAC/B2M,OAAQ4D,EAAevQ,KACvB4M,KAAM,eAMR8D,GAAeH,EAAgBlG,EAAKgD,MAAMzI,UAAYpG,EAAO4J,MAAM,UACrEqI,OACKA,EACA,CACDzQ,KAAMuQ,EAAevQ,KAAO,aAKlC8F,EAAMvB,SAASkM,GAGjB,OAAOF,0CA7ZHpD,GAAqB,SACrBkD,GAAiB,aACjBC,GAAa,SA8ZnB,SAASL,MAAgB5F,IAAwBiD,IAAAA,WAEzCqD,IAFuBnS,OAED,WADX8O,EAAc,IAAMA,EAAa,MADSxH,MAGrDvB,gBAHiB8F,MAKrBrK,KAAM2Q,EACN9F,EAAG,CACD8B,OAAQgE,EACR/D,KAAM,YAKZ,SAAS4C,GAAU1Q,EAAM8R,EAAa5D,GACpC,IAAM6D,EAAaD,EAAY9R,IAASkO,EAAWlO,GACnD,OAAI+R,GAAcA,EAAWpE,OACpBoE,EAAWpE,OAEb,GAGT,SAASqE,GAAmBtC,EAAYpD,GACtC,OAAOA,EAAQ3F,OAAO,SAACsL,EAAKrF,GAC1B,OAASA,EAAO8C,GAAqBuC,EAAIC,OAAO,CAC9CxC,WAAYA,EACZlB,WAAY5B,EAAO5M,KACnBN,OAAQkN,EAAO8C,KAHcuC,GAK9B,IAaL,SAASE,GAAcC,EAAWC,GAChC,IAAM3S,EAAsB0S,EAVhBxP,QAAQyL,GAAoB,IAWlCiE,EAAWD,MAAiBA,EAAc,GAOhD,MAAO,IALSD,EAAYE,KAEN5S,EAAS4S,EAEhB5S,QAAY4S,GAyC7B,SAASpB,KAAuB1C,OAATxC,IAAAA,MACrB,QAAKA,KACS,IAAVA,GACG7L,GAAS6L,EAAOwC,IAAgBxC,GAAS7L,GAAS6L,EAAMM,QAASkC,IAG1E,SAASoD,KAA0BW,OAATvG,IAAAA,MACxB,IAAKA,EAAO,SACZ,IAAc,IAAVA,GAAkB3K,EAAS2K,GAAQ,SACvC,IAAQM,EAAYN,EAAZM,QACR,OAAQ2C,GAAQjD,IAAWA,EAAMlG,SAAWyM,GAAmBtD,GAAQ3C,IAAaA,EAAQxG,SAAWyM,EAGzG,SAAStD,GAAQgD,GACf,OAAOO,MAAMvD,QAAQgD,GAGvB,SAAS9R,GAAS8R,EAAKjS,GACrB,SAAKiS,IAAQhD,GAAQgD,KACdA,EAAI9R,SAASH,GA8EtB,SAASmQ,GAAoBtB,GAC3B,IAAM4D,EAAQ5D,EAAMvF,MAAM,aAC1B,QAAKmJ,GAGE,CACL/S,OAAQ+S,EAAM,GACdzS,KAAMyS,EAAM,IAIhB,SAASnD,GAAc5J,GACrB,OAAO7E,OAAOqI,KAAKxD,GAAQiB,OAAO,SAACiB,EAAKnE,GAEtC,MAAY,SAARA,IAIFmE,EAAInE,GADFkC,EAASD,EAAOjC,IACP5C,OAAOoJ,OAAO,GAAIvE,EAAOjC,IAEzBiC,EAAOjC,IALXmE,GAQR,aCjnBmB8K,GAAiBtK,EAAU+D,EAAYwG,GAC7D,IAAMC,EAAU,GAChB,gBAAO5L,mBAASZ,mBAAcV,sCAqHrBU,EAAKyM,IApHJ3R,EAAyBwE,EAAzBxE,KAAaoL,EAAY5G,EAAZ4G,QACjBuG,EAAgBnN,EAEpB,GAHiCA,EAAnBsG,MAIZ,uBAAO5F,EAAKV,IAuCd,GAnCIxE,IAASqH,EAAOuK,cAClB9L,EAAMvB,SAAS,CACbvE,KAAMqH,EAAOwK,gBACbzG,QAASA,EACT0G,SAAU,GACVC,YAAY,EACZxF,KAAM/H,EAAO+H,OAIbvM,IAASqH,EAAO2K,eAElBvJ,WAAW,kBAAMuB,GAAYxF,EAAO+H,KAAKC,IAAK,CAAEtC,QAAS1F,KAAW,GAuBlExE,IAASqH,EAAO4K,cAAe,CACjC,IAAMjF,EAAa/B,IACbiH,EAAevS,OAAOqI,KAAKgF,GAC3BmF,EAAuBD,EAAapF,OAAO,SAAChO,GAChD,OAAOsM,EAAQnM,SAASH,KACvBkH,IAAI,SAAClH,GACN,OAAOkO,EAAWlO,KAEhBsT,EAAY,GACZC,EAAS,GACTP,EAAWtN,EAAOsN,SAEhBQ,EAAuBH,EAAqBnM,IAAI,SAAC0F,GACrD,IAAQC,EAAyBD,EAAzBC,OAAQ7M,EAAiB4M,EAAjB5M,KAAM2N,EAAWf,EAAXe,OAGtB,OAAOrC,GAAasB,EAFH,kBAAMC,EAAO,CAAEc,OAAAA,KAEM,KAAK7B,KAAK,SAAC2H,GAc/C,OAbKb,EAAQ5S,KAEXgH,EAAMvB,SAAS,CACbvE,KAAMqH,EAAOR,gBAAgB/H,GAC7BA,KAAMA,EACN0T,OAAQ7S,OAAOqI,KAAK0D,GAAQoB,OAAO,SAAChO,GAClC,OAAQ2H,EAAUxH,SAASH,OAG/B4S,EAAQ5S,IAAQ,GAElBsT,EAAYA,EAAUpB,OAAOlS,GAEtB4M,UAEA,SAAC/J,GAGR,GAAIA,aAAa6B,MACf,UAAUA,MAAM7B,GAIlB,OAFA0Q,EAASA,EAAOrB,OAAOrP,EAAE7C,MAElB6C,MAIX6I,QAAQiI,IAAIH,GAAsB1H,KAAK,SAAC8H,GAEtC,IAAMxI,EAAU,CACdkB,QAASgH,EACTC,OAAQA,EACRP,SAAUA,GAEZrJ,WAAW,WACLyJ,EAAatN,SAAY0N,EAAqB1N,OAASkN,EAASlN,QAClEkB,EAAMvB,cACD,CAAEvE,KAAMqH,EAAOsL,OACfzI,KAIN,KAvG+B,oBA4GlClK,IAASqH,EAAOC,UA5GkB,MA6GhC,kBAAkBsL,KAAK5S,IAEzByI,WAAW,kBAAMuC,GAAalF,EAAOmF,EAAY/D,IAAW,4BD9GrC1C,EAAQyG,EAAY/D,EAAUpB,EAAO+M,OAClE,IAAMC,EAAe3I,EAAWc,GAAcA,IAAeA,EACvD8H,EAAevO,EAAOxE,KACtBgT,EAAcD,EAAarR,QAAQyL,GAAoB,IAG7D,GAAI3I,EAAOqG,GAAKrG,EAAOqG,EAAE8B,OAEvB,uBAAOnI,GAGT,IAAMqE,EAAQ3B,EAASnD,WAEnBkP,GEpBwCjG,EFoBF8F,YEpBcI,EFoBArK,EAAMuC,WEpBN8H,EAAW,cAAI3J,EFoBA/E,EAAO+E,WEpBPA,EAAU,IAC1E5J,OAAOqI,KAAKgF,GAAYF,OAAO,SAAChO,GACrC,IAAMqU,EAAkB5J,EAAQ6B,SAAW,GAE3C,OAAIgI,EAAUD,EAAgBrU,IACrBqU,EAAgBrU,IAGG,IAAxBqU,EAAgBV,OAIhBS,EAASpU,KAAoC,IAA3BoU,EAASpU,GAAMuU,WAIpCrN,IAAI,SAAClH,UAASkO,EAAWlO,MFOxBiU,IAAiB1L,EAAOwK,iBAAmBrN,EAAOuN,aAEpDkB,EAAgBtT,OAAOqI,KAAKa,EAAMuC,SAAS0B,OAAO,SAAChO,GACjD,IAAMwU,EAAOzK,EAAMuC,QAAQtM,GAC3B,OAAO0F,EAAO4G,QAAQnM,SAASH,KAAUwU,EAAKC,cAC7CvN,IAAI,SAAClH,UAASgU,EAAahU,MAIhC,IAAM0U,EAAsBP,EAAcjN,IAAI,SAAC6I,UAAMA,EAAE/P,OAEjDmO,EAubR,SAA6BiE,EAAW+B,EAAejG,GACrD,IAGMyG,EAHaxC,GAAcC,GAGTlL,IAAI,SAAC0N,GAC3B,OAAO5C,GAAmB4C,EAAMT,KAGlC,OAAOA,EAAcxN,OAAO,SAACiB,EAAKgF,GAChC,IAAQ5M,EAAS4M,EAAT5M,OACiBmS,GAAcC,EAAWpS,GAEgBkH,IAAI,SAAC0N,GACrE,OAAO5C,GAAmB4C,EAAMT,KAD1BU,OAAaC,OAAaC,OAalC,OATIF,EAAY/O,SACd8B,EAAIoN,SAAShV,GAAQ6U,GAEnBC,EAAYhP,SACd8B,EAAIqN,SAASjV,GAAQ8U,GAEnBC,EAAWjP,SACb8B,EAAIsN,QAAQlV,GAAQ+U,GAEfnN,GACN,CACDuN,OAAQR,EAAK,GACbK,SAAU,GACVvG,OAAQkG,EAAK,GACbM,SAAU,GACVG,MAAOT,EAAK,GACZO,QAAS,KAvdQG,CAAoBpB,EAAcE,GA3ByB,uBAqCnDlG,GAAa,CACtCvI,OAAQA,EACR6F,KAAM,CACJgD,MAAOJ,EAAWgH,OAClBrF,WAAY3B,EAAW6G,UAEzBjL,MAAOA,EACPmE,WAAY8F,EACZ7F,WAAAA,EACA/F,SAAAA,EACApB,MAAAA,EACAuB,OAAQwL,mBAXJuB,oCA6DFrB,EAAa3K,MAAM+E,IAlGuD,uBAoGlDJ,GAAa,CACrCvI,YACK6P,GACHrU,KAJiBgT,UAMnB3I,KAAM,CACJgD,MAAOJ,EAAWiH,MAClBtF,WAAY3B,EAAW+G,SAEzBnL,MAAOA,EACPmE,WAAY8F,EACZ7F,WAAAA,EACA/F,SAAAA,EACApB,MAAAA,EACAuB,OAAQwL,mBAdJyB,GAmBFA,EAAY/H,MAAQ+H,EAAY/H,KAAKgI,aAQvCvK,GAAYsK,EAAY/H,KAAKC,IAAK,CAAEtC,QAASoK,4CAIjD,OAAOF,IAAAA,EA9EP,GAAI1D,GAAe0D,EAAcZ,EAAoB5O,QACnD,OAAOwP,EAUT,IAAIC,kBACAtB,IAAiBC,yBAQEjG,GAAa,CAChCvI,YACK4P,GACHpU,KAAMgT,IAER3I,KAAM,CACJgD,MAAOJ,EAAWM,OAClBqB,WAAY3B,EAAW8G,UAEzBlL,MAAOA,EACPmE,WAAY8F,EACZ7F,WAAAA,EACA/F,SAAAA,EACApB,MAAAA,EACAuB,OAAQwL,sBAdVwB,MANAA,EAAeD,sCAnEnB,sCEP8CpH,EAAYkG,EAAe3J,EDuH7CiL,CAAWhQ,EAAQyG,EAAY/D,EAAUpB,EAAO2L,kBAAhEgD,cACCvP,EAAKuP,yDAlHI,+CENEC,GAAkBtN,GACxC,gBAAOtB,mBAASZ,mBAAQV,GACtB,IAAQxE,EAA8BwE,EAA9BxE,KAAMuC,EAAwBiC,EAAxBjC,IAAK9B,EAAmB+D,EAAnB/D,MAAO8I,EAAY/E,EAAZ+E,QAC1B,GAAIvJ,IAASqH,EAAOF,SAAWnH,IAASqH,EAAO6B,WAAY,CACzD,GAAI1E,EAAOsG,MACT,OAAO5F,EAAKV,GAGVxE,IAASqH,EAAOF,QAClBC,EAAQD,QAAQ5E,EAAK9B,EAAO8I,GAE5BnC,EAAQ8B,WAAW3G,EAAKgH,GAG5B,OAAOrE,EAAKV,UCZKmQ,8BACnBV,OAAS,QACTC,MAAQ,QACRU,cAAgB,SAACC,EAAaC,GAC5BC,EAAKD,GAAYC,EAAKD,GAAU9D,OAAO6D,SAEzCG,iBAAmB,SAAC/O,EAAY6O,GAC9B,IAAM1Q,EAAQ2Q,EAAKD,GAAUG,UAAU,SAAA1C,UAAKA,IAAMtM,KACnC,IAAX7B,IAEJ2Q,EAAKD,aACAC,EAAKD,GAAUnW,MAAM,EAAGyF,GACxB2Q,EAAKD,GAAUnW,MAAMyF,EAAQ,WAapC8Q,mBAAqB,SAACJ,GACpB,gBAAOhP,mBAASZ,mBAAQV,GACtB,IAAMuB,EAAgB,CACpBhC,SAAU+B,EAAM/B,SAChBQ,SAAU,SAAC4Q,UAAQrP,EAAMvB,SAAS4Q,KAE9BtP,EAAQkP,EAAKD,GAAU9O,IAAI,SAAAC,UAAcA,EAAWF,KAC1D,OAAOT,eAAWO,EAAXP,CAAkBJ,EAAlBI,CAAwBd,iBCjCb4Q,GAAcnK,GACpC,gBAAwBpC,EAAYrE,YAAZqE,IAAAA,EAAQ,IAC9B,IAAIwM,EAAW,GACf,GAAoB,uBAAhB7Q,EAAOxE,KACT,OAAO6I,EAET,GAAI,2BAA2B+J,KAAKpO,EAAOxE,MAAO,CAChD,IAAMlB,EAAOwW,GAAqB9Q,EAAOxE,KAAM,kBACzC0L,EAAST,IAAanM,GAC5B,IAAK4M,IAAW5M,EACd,OAAO+J,EAET,IAAM0M,EAAY/Q,EAAO6O,QACnB5G,EAASf,EAAOe,OAStB,OARA4I,EAASvW,GAAQ,CACfuU,QAASkC,EAEThC,cAAcgC,GAAaC,SAAS9J,EAAO+J,YAE3C9J,SAAS4J,GAAaC,QAAQ9J,EAAOC,OAAO,CAAEc,OAAAA,KAC9CA,OAAAA,QAEU5D,EAAUwM,GAExB,GAAI,uBAAuBzC,KAAKpO,EAAOxE,MAAO,CAC5C,IAAMlB,EAAOwW,GAAqB9Q,EAAOxE,KAAMqH,EAAOoO,YAChD/J,EAAST,IAAanM,GAC5B,OAAK4M,GAAW5M,GAIhBuW,EAASvW,QACJ+J,EAAM/J,GACN,CACDyU,aAAa,EAEb5H,OAAQ6J,QAAQ9J,EAAOC,OAAO,CAAEc,OANrBf,EAAOe,iBASV5D,EAAUwM,IAXbxM,EAaX,GAAI,kBAAkB+J,KAAKpO,EAAOxE,MAMhC,OAJAqV,EAAS7Q,EAAO1F,WACX+J,EAAMrE,EAAO1F,MACb,CAAE6M,QAAQ,SAEH9C,EAAUwM,GAExB,OAAQ7Q,EAAOxE,MAUb,KAAKqH,EAAO2K,cACV,YACKnJ,EACA6M,GAAmBlR,EAAO4G,SAAS,EAAOvC,IAGjD,KAAKxB,EAAOuK,aACV,YACK/I,EACA6M,GAAmBlR,EAAO4G,SAAS,EAAMvC,IAEhD,QACE,OAAOA,IAKf,SAASyM,GAAqBtV,EAAM2V,GAClC,OAAO3V,EAAK4V,UAAUD,EAAS/Q,OAAS,EAAG5E,EAAK4E,QAGlD,SAAS8Q,GAAmBtK,EAASyK,EAAQnS,GAC3C,OAAO0H,EAAQ3F,OAAO,SAACiB,EAAKoP,GAO1B,OANApP,EAAIoP,QACCpS,EAAaoS,GACb,CACDzC,QAASwC,IAGNnP,GACNhD,YC5FmBqS,GAAU3V,GAChC,IACC,OAAO4N,KAAKgI,MAAMhI,KAAKC,UAAU7N,IAChC,MAAO6V,IACT,OAAO7V,ECCT,IAAM8V,GAAe,CACnBC,KAAM,GACNC,QAAS,aAIaC,GAAaxN,EAAsBrE,YAAtBqE,IAAAA,EAAQqN,IAC3C,IAAiC3M,EAAkB/E,EAAlB+E,QAASgD,EAAS/H,EAAT+H,KAE1C,GAFmD/H,EAA3CxE,OAGDqH,EAAOsB,MAAZ,CACE,IAAM2N,EAAaP,MACjBpI,MAL6CnJ,EAArCmJ,MAMR4I,WAN6C/R,EAA9B+R,YAOX5W,OAAOqI,KAAKuB,GAAS3E,QAAW,CAAE2E,QAASA,IAC/CgD,KAAAA,KAEF,YACK1D,EACA,CACDsN,KAAMG,EAENF,QAASvN,EAAMuN,QAAQpF,OAAOsF,KAIlC,OAAOzN,ECxBb,IAAMqN,GAAe,CACnB3K,QAAS,aAGaiL,GAAa3N,EAAsBrE,YAAtBqE,IAAAA,EAAQqN,IAC3C,IAAchM,EAAY1F,EAAZ0F,QAEd,OAF0B1F,EAAlBxE,MAGN,IAAK,QACH,IAAIyW,EAOJ,OAJEA,EADEvM,GAAWA,EAAQlK,MAAQkK,EAAQlK,OAASqH,EAAOqB,SACvC,CAAClE,GAAQwM,OAAOnI,EAAM0C,SAEtB1C,EAAM0C,QAAQyF,OAAOxM,QAGhCqE,GACH0C,QAASkL,IAEb,IAAK,UACH,MAAO,GAET,QACE,OAAO5N,GCzBb,IAAM6N,GAAY,OAYlB,SAASC,GAAQC,GACf,IACMC,EADQ,oCACQC,KAAKF,GAE3B,MAAO,KADYC,GAAWA,EAAQ,GAAMA,EAAQ,GAAGtF,MAAM,KAAK,GAAG7P,QAAQgV,GAAW,IAAM,QCb5FK,GACAC,GACAC,GACAC,GD0CSC,GAAc,SAACC,GAC1B,YAD0BA,IAAAA,EAAW,KAChC7Y,EAAW,OAAO6Y,EACvB,MAA4BC,SAApBC,IAAAA,MAAON,IAAAA,WAC+BnY,OAAtC0Y,IAAAA,SAAUC,IAAAA,WAAYC,IAAAA,YACtBC,EAAiBH,EAAjBG,KAAMC,EAAWJ,EAAXI,OACRf,EA5BR,SAAoBe,GAClB,IAAMC,EAvBR,WACE,GAAKrZ,EAEL,IADA,IACgBsZ,EADVC,EAAOT,SAASU,qBAAqB,QAClCpT,EAAI,EAAQkT,EAAMC,EAAKnT,GAAIA,IAClC,GAAgC,cAA5BkT,EAAIG,aAAa,OACnB,OAAOH,EAAIG,aAAa,QAkBVC,GAClB,OAAKL,EACEA,EAAUxP,MAAM,MAAQwP,EAAYA,EAAYD,EADhC9Y,OAAO0Y,SAASW,KAAKxW,QAAQgV,GAAW,IA0BnDyB,CAAWR,GACjBS,EAAO,CACXd,MAAOA,EACPV,IAAKA,EACLyB,KAAM1B,GAAQC,GACdc,KAAMA,EACNC,OAAQA,EACRW,MAAOd,EACPe,OAAQd,GAMV,OAJIT,GAAyB,KAAbA,IACdoB,EAAKpB,SAAWA,QAIboB,EAEAhB,IAIDlB,GAAe,CACnBC,KAAM,GACNC,QAAS,aAIagC,GAAKvP,EAAsBrE,YAAtBqE,IAAAA,EAAQqN,IACnC,IAAoB3M,EAAkB/E,EAAlB+E,QACpB,GAAQ/E,EAAOxE,OACRqH,EAAO+Q,KAAZ,CACE,IAAMI,EAAWzC,MACfQ,WAJgC/R,EAA9B+R,WAKFhK,KALgC/H,EAAT+H,MAMnB5M,OAAOqI,KAAKuB,GAAS3E,QAAW,CAAE2E,QAASA,KAEjD,YACKV,EACA,CACDsN,KAAMqC,EAENpC,QAASvN,EAAMuN,QAAQpF,OAAOwH,KAIlC,OAAO3P,EC1FXkO,cCVA,IAAKxY,EAAW,SAChB,IAAMka,EAAK1Z,UAAU2Z,WAErB,OAAKD,EAAGpU,QAAQ,OAAe,WAC1BoU,EAAGpU,QAAQ,OAAe,SAC1BoU,EAAGpU,QAAQ,OAAe,QAC1BoU,EAAGpU,QAAQ,SAAiB,QAE1B,aDEEsU,GACT3B,GAAYzY,EAAa8Y,SAASL,SAAW,KAC7CC,wGAAS2B,GACT1B,qFAAW2B,GAMb,IAAM3C,GAAe,CACnB3C,aAAa,EACbuF,UAAWrP,IACXsP,IAAK,KACLC,QAAS,KACTC,OAAO,EACP3N,UAAU/M,IAAcQ,UAAUma,OAClCT,GAAI,CACF3Z,KAAMiY,IAER/X,UAAYT,EAAaQ,UAAUC,UAAY,OAC/Cma,QAAS,CACPra,KAAMuH,EAEN2S,QApCJ,WAsCEI,SAAUlC,GACVD,OAAQA,GACR3O,SAAU,GACV0O,SAAUA,aAIY3L,GAAQxC,EAAsBrE,YAAtBqE,IAAAA,EAAQqN,IACtC,IAAQ3C,EAAgB1K,EAAhB0K,YACMjL,EAAa9D,EAAb8D,SACd,OAD2B9D,EAAnBxE,MAEN,KAAKqH,EAAOiB,SACV,YACKO,EACA,CAAEP,SAAUA,IAEnB,KAAKjB,EAAOiE,QACV,YACKzC,EACA,CAAEyC,SAAS,IAElB,KAAKjE,EAAOgS,OACV,YACKxQ,EACA,CAAEyC,SAAS,IAElB,QACE,OAAKiI,EAOE1K,OALAqN,GACArN,EACA,CAAE0K,aAAa,KAO5B,IAAM+F,GAAe,CAAC,UAAW,WAAY,WE1E7C,SAASC,GAAO/G,EAAQgH,EAAMC,GAC5B,GAAKlb,EAAL,CACA,IAAMiD,EAAK3C,QAAQ4a,EAAQ,MAAQ,UAAY,iBAC/CjH,EAAOjB,MAAM,KAAKtI,QAAQ,SAAAyQ,GACxBlY,EAAGkY,EAAIF,eAQKG,GAAMC,GACpB,IACM3V,EAAWsV,GAAOpa,KAAK,KAAM,iBADxB,SAAA0L,UAJJL,QAAQC,SAAS1L,UAAUma,QAIVtO,KAAKgP,KAI7B,OAFA3V,GAAS,YAEF4G,UAAK5G,GAAS,aCfP4V,KAId,OAFAC,EAAIzT,EAAU,aAENlD,GACN,gBAAQC,EAASC,EAAgBC,GAC/B,IAAMwC,EAAQ3C,EAAYC,EAASC,EAAgBC,GAC7CyW,EAAejU,EAAMvB,SAM3B,OAAO5E,OAAOoJ,OAAOjD,EAAO,CAAEvB,SALb,SAACC,GAGhB,OADArC,EAAcF,GAAd,UAA6BkC,KADnBK,EAAOA,QAAUA,GAEpBuV,EAAavV,iBAOZwV,GAAiBvN,GAC/B,kBACE,OAAOnH,EAAQA,EAAQ2U,MAAM,KAAMC,WAAYL,gBCtB3BM,GAAYC,GAClC,OAAKA,W/B0NiB5a,GACtB,MApNmB,UAoNZmB,EAAQnB,G+B1NXuO,CAAQqM,GAAuBA,EAC5B,CAACA,GAFmB,YCULC,GAAa9N,EAAW9B,EAAS6P,YAApB/N,IAAAA,EAAO,IACxC,IAR6BgO,EAAUC,EAQjChO,EAAM/C,IAKZ,OAJIgB,IAEFV,GAAMyC,IAXqB+N,EAWU9P,EAXA+P,WCCCC,GAG1C,IAFA,IACIb,EADEc,EAAOD,GAAYnJ,MAAM1R,UAAUjB,MAAMmB,KAAKoa,WAE3CvV,EAAI,EAAGA,EAAI+V,EAAK9V,OAAQD,IAC/B,GAAIwF,EAAWuQ,EAAK/V,IAAK,CACvBiV,EAAKc,EAAK/V,GAAI,MAGlB,OAAOiV,EDE2Ce,CAAYL,YAVtDjQ,GACFmQ,GAAUA,EAASnQ,GACvBkQ,EAASlQ,WAWJkC,GACHC,IAAKA,EACLoO,QEpBOC,MAAOC,WFqBTrQ,EAAgB,CAAE8J,aAAa,GAApB,IG8BtB,SAASwG,GAAUtO,YAAAA,IAAAA,EAAS,IAC1B,IAAMuO,EAAiBvO,EAAOwO,UAAY,GACpCvT,EAAc+E,EAAO/E,aAAe,GAUpCwT,GAAiBzO,EAAOrB,SAAW,IAAI3F,OAAO,SAACiB,EAAKgF,GACxD,GAAIvB,EAAWuB,GAGb,OADAhF,EAAImO,YAAcnO,EAAImO,YAAY7D,OAAOtF,GAClChF,EAIT,GADIgF,EAAOyP,YAAWzP,EAAO5M,KAAO4M,EAAOyP,YACtCzP,EAAO5M,KAEV,UAAU0E,MAAM4X,+BAGb1P,EAAOe,SAAQf,EAAOe,OAAS,IAEpC,IAAM4O,EAAiB3P,EAAOrE,OAAU1H,OAAOqI,KAAK0D,EAAOrE,QAAQrB,IAAI,SAACsV,GACtE,OAAO5P,EAAOrE,OAAOiU,KAClB,GAKL5U,EAAI6U,cAAc7P,EAAO5M,SAHqB,IAAnB4M,EAAO2H,UAC0B,IAA1B3H,EAAOe,OAAO4G,gBAGzC3H,EAAO2H,QAEV3H,EAAO8P,UACT9U,EAAI8U,QAAQ9P,EAAO5M,MAAQa,OAAOqI,KAAK0D,EAAO8P,SAAS/V,OAAO,SAACC,EAAG+V,GA83BtE,IAAyBja,EA33BnB,OADAkE,EAAE+V,IA43BiBja,EA53BIkK,EAAO8P,QAAQC,cAk4BxC,IAHA,IAAMf,EAAOpJ,MAAM1R,UAAUjB,MAAMmB,KAAKoa,WAEpCwB,EAAU,IAAIpK,MAAM9P,EAAGoD,QAClBD,EAAI,EAAGA,EAAI+V,EAAK9V,OAAQD,IAC/B+W,EAAQ/W,GAAK+V,EAAK/V,GAKpB,OAFA+W,EAAQA,EAAQ9W,QAAUsC,EAEnB1F,EAAGyY,MAAM,CAAE/S,SAAAA,GAAYwU,KAv4BrBhW,GACN,WAEIgG,EAAO8P,SAGhB,IAEMG,EAFkBhc,OAAOqI,KAAK0D,GAEFsF,OAAOqK,GAEnCO,EAAkB,IAAIC,IAAInV,EAAI8L,OAAOxB,OAAO2K,IAKlD,GAJAjV,EAAI8L,OAASlB,MAAM1E,KAAKgP,GAExBlV,EAAIwL,aAAexL,EAAIwL,aAAalB,OAAOtF,GAEvChF,EAAI0E,QAAQM,EAAO5M,MACrB,UAAU0E,MAAMkI,EAAO5M,KAAO,iBAOhC,OALA4H,EAAI0E,QAAQM,EAAO5M,MAAQ4M,EACtBhF,EAAI0E,QAAQM,EAAO5M,MAAM6M,SAE5BjF,EAAI0E,QAAQM,EAAO5M,MAAM6M,OAAS,sBAE7BjF,GACN,CACD0E,QAAS,GACTmQ,cAAe,GACfC,QAAS,GACTtJ,aAAc,GACd2C,YAAa,GACbrC,OAAQ,KAIJpL,EAAWqF,EAAOrF,QAAWqF,EAAOrF,QAAU,CAClDgC,QAAS0S,EACT3U,QAAS2S,EACT5Q,WAAYM,GAGRuS,WvB5EwB3U,GAC9B,gBAA4B7E,EAAK2E,EAAUgD,GAGzC,OADkBhD,EAASnD,SAAS,QAAQxB,KASxC2H,GAAWzF,EAASyF,IAAYA,EAAQ3H,GAInC2H,EAAQ3H,GAIK4G,EAAqB/B,GAAS7E,IAS7CuZ,EAAIzS,GAAQ9G,KAAS,OuB+CVyZ,CAAgB5U,GAGhC6U,EAAgBf,EAAc9P,QAG5B8Q,EAAkBhB,EAAc1I,OAAO1F,OAAO,SAAChO,GACnD,OAAQ2H,EAAUxH,SAASH,KAC1Bqd,OACGC,EAAe,IAAIP,IAAIK,EAAgBlL,OAAOxK,GAAYsG,OAAO,SAAChO,GACtE,OAAQ2H,EAAUxH,SAASH,MAEvBud,EAAkB/K,MAAM1E,KAAKwP,GAAcD,OAG3ClR,EAAa,kBAAMgR,KAMrB,IAAItH,GAHNC,IAAAA,cACAI,IAAAA,iBACAE,IAAAA,mBAGIoH,EAAe,WAEnB,UAAU9Y,MAAM,8BAIZ+D,8aAEAE,EAAgB0B,EAAqB/B,GACrCmV,OACD9U,EACAC,EACEH,EAAOU,OAAe,CAAEL,OAAQL,EAAOU,QAAtB,GACjBV,EAAOiV,OAAe,CAAE3U,YAAaN,EAAOiV,QAA3B,IAGnBD,EAAY1U,cACf0U,EAAY1U,YAAc4B,KAkB5B,IAAM2B,KAkBJqR,OAAQ,SAACrR,EAASoP,GAChB,WAAWhQ,QAAQ,SAACC,GAClB3E,GAAMvB,SAAS,CACbvE,KAAMqH,EAAOuK,aACbxG,QAAS+O,GAAY/O,GACrBP,EAAG,CAAE2F,eAAgBnJ,EAAOuK,eAC3BnH,EAAS,CAAE+P,OAmBlBkC,QAAS,SAACtR,EAASoP,GACjB,WAAWhQ,QAAQ,SAACC,GAClB3E,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO2K,cACb5G,QAAS+O,GAAY/O,GACrBP,EAAG,CAAE2F,eAAgBnJ,EAAO2K,gBAC3BvH,EAAS,CAAC+P,QAiCdU,EAAcM,SAGfmB,GAAc,EAgBZzV,EAAW,CA4CfwB,kBAAiBd,EAAQE,EAAQyB,EAASiR,OACxC,IAAMvQ,EAAK9J,EAASyH,GAAUA,EAAS,KACjCyC,EAAO5F,EAASmD,GAAUA,EAASE,EACnC8U,EAAOrT,GAAW,GAClB/B,EAAON,EAASM,OAGtBsS,EAAIzQ,GAAQ/C,GAAK2D,GAEjB,IAAM4S,EAAa5S,GAAMI,EAAKzC,QAAUmU,EAAYzV,EAAIY,EAAUmD,GAElE,uBAAO,IAAIG,QAAQ,SAACC,GAClB3E,GAAMvB,YACJvE,KAAMqH,EAAOyV,cACblV,OAAQiV,EACR/U,OAAQuC,GAAQ,GAChBd,QAASqT,EACT/U,YAAaL,EAAKK,aAEdL,EAAKyC,IAAOzC,EAAKyC,KAAOA,GAAO,CAAE8S,WAAYvV,EAAKyC,KACrDQ,EAAS,CAAC3C,EAAQyB,EAASiR,OApB1B,oCAuER7R,eAAcqU,EAAW9S,EAASX,EAASiR,OACzC,IAAM1b,EAAO2F,EAASuY,GAAaA,EAAUrP,MAAQqP,EACrD,IAAKle,IAASqB,EAASrB,GACrB,UAAU0E,MAAM,gBAElB,IAAM6G,EAAO5F,EAASuY,GAAaA,EAAa9S,GAAW,GACrD0S,EAAOnY,EAAS8E,GAAWA,EAAU,GAE3C,uBAAO,IAAIiB,QAAQ,SAACC,GAClB3E,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO4V,WACbtP,MAAO7O,EACPyX,WAAYlM,EACZd,QAASqT,EACThV,OAAQmU,EAAYzV,EAAIY,EAAUgD,GAClCrC,YAAakU,EAAYxV,EAAQW,EAAUgD,IAC1CO,EAAS,CAACP,EAASX,EAASiR,OAhB9B,oCA4DLpC,cAAa/N,EAAMd,EAASiR,OAC1B,IAAMjI,EAAI9N,EAAS4F,GAAQA,EAAO,GAC5BuS,EAAOnY,EAAS8E,GAAWA,EAAU,GAU3C,uBAAO,IAAIiB,QAAQ,SAACC,GAClB3E,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO6V,UACb3G,WAAYY,GAAY5E,GACxBhJ,QAASqT,EACThV,OAAQmU,EAAYzV,EAAIY,EAAUqL,GAClC1K,YAAakU,EAAYxV,EAAQW,EAAUqL,IAC1C9H,EAAS,CAACJ,EAAMd,EAASiR,OAnB5B,oCAuCJhT,KAAM,SAACjF,GACL,GAAIA,IAAQ+D,GAAc,OAAR/D,EAChB,OAAOwZ,EAAYzV,EAAIY,GAEzB,GAAI3E,IAAQgE,GAAkB,WAARhE,EACpB,OAAOwZ,EAAYxV,EAAQW,GAE7B,IAAMM,EAAON,EAASnD,SAAS,QAC/B,OAAKxB,EACE4a,EAAQ3V,EAAMjF,GADJiF,GAanBwB,MAAO,SAACwR,GACN,WAAWhQ,QAAQ,SAACC,GAClB3E,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO+V,YACZ3S,EAAS+P,MAehB7H,MAAO,SAAC6H,GAGN,OADImC,GAAanC,EAAS,CAAEpP,QAAAA,EAASlE,SAAAA,IAC9BA,EAASmW,GAAGhW,EAAOsL,MAAO,SAACnT,GAChCgb,EAAShb,GACTmd,GAAc,KAyBlBU,GAAI,SAACve,EAAM0b,GACT,IAAK1b,IAASqL,EAAWqQ,GACvB,SAEF,GAAI1b,IAASuI,EAAOC,UAClB,UAAU9D,MAAM,oBAAsB1E,GAExC,IAAMwe,EAAa,gBACnB,GAAa,MAATxe,EAAc,CAChB,IAAMye,EAAgB,SAAAzX,mBAASZ,mBAAQV,GAQrC,OAPIA,EAAOxE,KAAKoI,MAAMkV,IACpB9C,EAAS,CACPtQ,QAAS1F,EACT0C,SAAAA,EACAkE,QAAS6Q,IAGN/W,EAAKV,MAERgZ,EAAe,SAAA1X,mBAASZ,mBAAQV,GAQpC,OAPKA,EAAOxE,KAAKoI,MAAMkV,IACrB9C,EAAS,CACPtQ,QAAS1F,EACT0C,SAAAA,EACAkE,QAAS6Q,IAGN/W,EAAKV,MAQd,OANAoQ,EAAc2I,EAAetJ,IAC7BW,EAAc4I,EAActJ,eAM1Bc,EAAiBuI,EAAetJ,IAChCe,EAAiBwI,EAActJ,KAInC,IAAMY,EAAYhW,EAAKsJ,MAAMkV,GAAerJ,GAASC,GAC/CuJ,EAAU,SAAA3X,mBAASZ,mBAAQV,GAe/B,OAbIA,EAAOxE,OAASlB,GAClB0b,EAAS,CACPtQ,QAAS1F,EACT0C,SAAUA,EACVkE,QAAS6Q,EACTnR,MAAOwR,IAQJpX,EAAKV,MAGd,OADAoQ,EAAc6I,EAAS3I,qBACVE,EAAiByI,EAAS3I,KAwBzC4I,KAAM,SAAC5e,EAAM0b,GACX,IAAK1b,IAASqL,EAAWqQ,GACvB,SAEF,GAAI1b,IAASuI,EAAOC,UAClB,UAAU9D,MAAM,sBAAwB1E,GAE1C,IAAM6e,EAAiBzW,EAASmW,GAAGve,EAAM,YACvC0b,EAAS,CACPtQ,UAFwCA,QAGxChD,SAAUA,EACVkE,QAAS6Q,EACTnR,MAAOwR,IAGTqB,MAEF,OAAOA,GAgBT5Z,SAAU,SAACxB,GACT,IAAMsG,EAAQ/C,GAAM/B,WACpB,OAAIxB,EAAY4a,EAAQtU,EAAOtG,GACxB5C,OAAOoJ,OAAO,GAAIF,IAM3BtE,SAAU,SAACC,GACT,IAAMoZ,EAAazd,EAASqE,GAAU,CAAExE,KAAMwE,GAAWA,EACzD,GzBtfGgC,EAAWvH,SyBsfO2e,EAAW5d,MAC9B,UAAUwD,MAAM,mBAAqBoa,EAAW5d,MAElD,IAIM6d,OACDD,GACH/S,KACE2F,eAAgBoN,EAAW5d,MAPdwE,EAAOqG,GAAK,MAY7B/E,GAAMvB,SAASsZ,IAIjBjM,aAAcxG,EAAQqR,OAGtBzK,cAAe5G,EAAQsR,QAGvBtR,QAASA,EAuBThE,QAAS,CAYPgC,QAAShC,EAAQgC,QAYjBjC,QAAS,SAAC5E,EAAK9B,EAAO8I,GACpBzD,GAAMvB,SAAS,CACbvE,KAAMqH,EAAOyW,aACbvb,IAAKA,EACL9B,MAAOA,EACP8I,QAASA,KAabL,WAAY,SAAC3G,EAAKgH,GAChBzD,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO0W,gBACbxb,IAAKA,EACLgH,QAASA,MAcfyU,eAAgB,SAACnW,EAAa0B,GAG5BrC,EAASE,QAAQD,QAAQ8W,EAAmBpW,EAAa0B,IAM3DiJ,OAAQ,CACNiB,KAAMjN,EACN4E,QAAS8Q,IAUPrH,EAAcqG,EAAcrG,YAAY7D,OAAO,CAN5B,SAAAkN,mBAAYhZ,mBAAQV,GAI3C,OAHKA,EAAO+H,OACV/H,EAAO+H,KAAO4R,MAETjZ,EAAKV,MAKZ0Q,EAAmBjB,IAEnBhO,GAAmBiB,EAAU+D,EAAY,CACvCwH,IAAK4J,EACLjR,QAAS8Q,IAEXjW,GAAmBmB,GACnBnB,EAAsBiB,GACtBjB,GAAoBiB,GAEpBgO,EAAmBhB,MAIfkK,EAAe,CACnB/S,QAASA,GACT7D,KAAMA,EAAKJ,GACXgR,KAAMA,GACNzP,MAAOA,GACPyC,QAASiT,GAAkBpT,GAC3BN,MAAOA,IAGL2T,EAAmBhZ,EACnBiZ,GAAyBjZ,EAC7B,GAAI/G,GAAakO,EAAOwM,MAAO,CAC7B,IAAMuF,GAAW3f,OAAO4f,qCACpBD,KACFF,EAAmBE,GAAS,CAAEE,OAAO,EAAMC,WAAY,MAEzDJ,GAAyB,WACvB,OAAyB,IAArBrE,UAAUtV,OAAqBiV,KAC/BpV,SAAgByV,UAAU,IAAYF,KACnCA,KAAmBC,MAAM,KAAMC,YAI1C,IAqCyB1Y,GArCnBod,YR5xBoBnS,GAC1B,OAAO9M,OAAOqI,KAAKyE,GAAQhH,OAAO,SAACiB,EAAKmY,GACtC,OAAIvF,GAAara,SAAS4f,KAG1BnY,EAAImY,GAAWpS,EAAOoS,IAFbnY,GAIR,IQqxBmBoY,CAAYrS,GAE5BsS,GAAoB7D,EAAchJ,aAAazM,OAAO,SAACiB,EAAKgF,GAChE,IAAQ5M,EAAyB4M,EAAzB5M,KAAM2N,EAAmBf,EAAnBe,OAAQd,EAAWD,EAAXC,OAChB4J,EAAY2F,EAAcK,cAAczc,GAQ9C,OAPA4H,EAAI5H,GAAQ,CACVuU,QAASkC,EAEThC,cAAcgC,GAAaC,SAAS9J,EAAO+J,YAC3C9J,OAAQ6J,QAAQ7J,EAAO,CAAEc,OAAAA,KACzBA,OAAAA,GAEK/F,GACN,IAEGwP,GAAe,CACnB7K,QAASuT,GACTpX,KAAM+U,EACNnR,QAAS2T,IAKLjZ,GAAQ3C,W9BxzBwB8X,GAGtC,IAFA,IAAM+D,EAAcrf,OAAOqI,KAAKiT,GAC1BgE,EAAgB,GACbta,EAAI,EAAGA,EAAIqa,EAAYpa,OAAQD,IAAK,CAC3C,IAAMpC,EAAMyc,EAAYra,UAQbsW,EAAS1Y,KAASC,IAC3Byc,EAAc1c,GAAO0Y,EAAS1Y,IAGlC,IAOI2c,EAPEC,EAAmBxf,OAAOqI,KAAKiX,GAQrC,KArDF,SAA4BhE,GAC1Btb,OAAOqI,KAAKiT,GAAUhS,QAAQ,SAAA1G,GAC5B,IAAMa,EAAU6X,EAAS1Y,GAEzB,UADqBa,OAAQG,EAAW,CAAEvD,KAAM2C,MAEtBF,UACjBW,OAAQG,EAAW,CAAEvD,KAAM4C,MAAmBH,EAErD,UAAUe,MAAM4b,WAAgB7c,EAAM,IAAME,KA8C9C4c,CAAmBJ,GACnB,MAAOtd,GACPud,EAAsBvd,EAGxB,gBAA4BkH,EAAYrE,GACtC,YAD0BqE,IAAAA,EAAQ,IAC9BqW,EACF,MAAMA,EAYR,IAFA,IAAII,GAAa,EACXC,EAAY,GACT5a,EAAI,EAAGA,EAAIwa,EAAiBva,OAAQD,IAAK,CAChD,IAAMpC,EAAM4c,EAAiBxa,GAEvB6a,EAAsB3W,EAAMtG,GAC5Bkd,GAAkBrc,EAFR6b,EAAc1c,IAEEid,EAAqBhb,GACrD,UAAWib,IAAoBhd,EAAO,CACpC,IAAMid,EAAeta,EAA8B7C,EAAKiC,GACxD,UAAUhB,MAAMkc,GAElBH,EAAUhd,GAAOkd,EACjBH,EAAaA,GAAcG,IAAoBD,EAEjD,OAAOF,EAAaC,EAAY1W,G8BkwBhC8W,MAAqBvB,EAAiBpD,IAEtC9E,GAEAqI,GACED,EACE1Y,eAAmBiP,MAmBzB/O,GAAMvB,UAbmB/C,GAaQsE,GAAMvB,kBAZpBoJ,EAAO4M,EAAUqF,GAEhC,IAGMC,OAAgBlS,EAAU,CAAEpB,KAHrB4R,GAAWxQ,EAAMpB,KAAMgO,EAAUJ,GAAYyF,MAK1D,OAAOpe,GAAGyY,MAAM,KAAM,CAAE4F,MAQ5B,IAAMC,GAAangB,OAAOqI,KAAKiU,GAG/BnW,GAAMvB,SAAS,CACbvE,KAAMqH,EAAOC,UACb8D,QAAS0U,GACTrT,OAAQmS,GACRrX,OAAQA,EACRC,KAAM+U,EACN7U,YAAAA,EACAD,cAAAA,IAGF,IAAMsY,GAAiBD,GAAWhT,OAAO,SAAChO,UAASoc,EAAcK,cAAczc,KACzEkhB,GAAkBF,GAAWhT,OAAO,SAAChO,UAAUoc,EAAcK,cAAczc,KA6DjF,OA1DAgH,GAAMvB,SAAS,CACbvE,KAAMqH,EAAO4Y,gBACb7U,QAAS0U,GACTzM,QAAS6H,EAAcK,gBAIzBL,EAAchJ,aAAalM,IAAI,SAAC0F,EAAQ/G,GACtC,IAAQ2C,EAA4BoE,EAA5BpE,UAAWmF,EAAiBf,EAAjBe,OAAQ3N,EAAS4M,EAAT5M,KACvBwI,GAAa6C,EAAW7C,IAC1BA,EAAU,CAAEJ,SAAAA,EAAUuF,OAAAA,EAAQvC,QAASwB,IAGzC5F,GAAMvB,SAAS,CACbvE,KAAMqH,EAAOT,mBAAmB9H,GAChCA,KAAMA,EACNuU,QAAS6H,EAAcK,cAAczc,GACrC4M,OAAQA,IAINwP,EAAchJ,aAAatN,SAAYD,EAAI,GAC7CmB,GAAMvB,SAAS,CACbvE,KAAMqH,EAAOwK,gBACbzG,QAAS2U,GACTjO,SAAUkO,OAOdrG,GAAM,SAACrO,GACLxF,GAAMvB,SAAS,CACbvE,KAAOsL,EAAWjE,EAAOiE,QAAUjE,EAAOgS,oBnB72BhBvT,EAAOmF,EAAY/D,GAE5CgZ,YAAY,kBAAMlV,GAAalF,EAAOmF,EAAY/D,IAAW,KmB+2BlEiZ,CAAUra,GAAOmF,EAAY/D,GAoBxBA,EAIT,IAAM+M,GAAS,SACTC,GAAQ"}