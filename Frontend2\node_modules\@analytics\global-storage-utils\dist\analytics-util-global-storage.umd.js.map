{"version": 3, "file": "analytics-util-global-storage.umd.js", "sources": ["../src/index.js"], "sourcesContent": ["import { OBJECT, PREFIX, UNDEFINED } from '@analytics/type-utils'\n\nexport const GLOBAL = 'global'\n\nexport const KEY = PREFIX + GLOBAL + PREFIX\n\nexport const globalContext = (typeof self === OBJECT && self.self === self && self) || (typeof global === OBJECT && global[GLOBAL] === global && global) || this\n\n/* initialize global object */\nif (!globalContext[KEY]) {\n  globalContext[KEY] = {}\n}\n/**\n * Get value from global context\n * @param {string} key - Key of value to get\n * @returns {*} value\n */\nexport function get(key) {\n  return globalContext[KEY][key]\n}\n\n/**\n * Set value to global context\n * @param {string} key - Key of value to set\n * @param {*} value \n * @returns value\n */\nexport function set(key, value) {\n  return globalContext[KEY][key] = value\n}\n\n/**\n * Remove value to global context\n * @param {string} key - Key of value to remove\n */\nexport function remove(key) {\n  delete globalContext[KEY][key]\n}\n\n/**\n * Wrap localStorage & session storage checks\n * @param {string} type - localStorage or sessionStorage\n * @param {string} storageOperation - getItem, setItem, removeItem\n * @param {function} fallbackFunction - fallback function\n */\nexport function wrap(type, operation, fallback) {\n  let fn\n  try {\n    if (hasSupport(type)) {\n      const storage = window[type]\n      fn = storage[operation].bind(storage)\n    }\n  } catch(e) {}\n  return fn || fallback\n}\n\nconst cache = {}\nexport function hasSupport(type) {\n  if (typeof cache[type] !== UNDEFINED) {\n    return cache[type]\n  }\n  try {\n    const storage = window[type]\n    // test for private safari\n    storage.setItem(UNDEFINED, UNDEFINED)\n    storage.removeItem(UNDEFINED)\n  } catch (err) {\n    return cache[type] = false\n  }\n  return cache[type] = true\n}\n\n/*\n// () => localStorage)\n// () => sessionStorage)\nexport function isSupported(getStorage) {\n  try {\n    const testKey = '__' + undef\n    getStorage().setItem(testKey, testKey)\n    getStorage().removeItem(testKey)\n    return true\n  } catch (e) {\n    return false\n  }\n}\n*/\n"], "names": ["GLOBAL", "KEY", "PREFIX", "globalContext", "self", "OBJECT", "global", "this", "cache", "hasSupport", "type", "UNDEFINED", "storage", "window", "setItem", "removeItem", "err", "key", "value", "operation", "fallback", "fn", "bind", "e"], "mappings": "sTAEaA,EAAS,SAETC,EAAMC,SAASF,EAASE,SAExBC,SAAwBC,OAASC,UAAUD,KAAKA,OAASA,MAAQA,aAAiBE,SAAWD,UAAUC,OAAM,SAAaA,QAAUA,aAAWC,EAGvJJ,EAAcF,KACjBE,EAAcF,GAAO,IA8CvB,IAAMO,EAAQ,YACEC,EAAWC,GACzB,UAAWF,EAAME,KAAUC,YACzB,OAAOH,EAAME,GAEf,IACE,IAAME,EAAUC,OAAOH,GAEvBE,EAAQE,QAAQH,YAAWA,aAC3BC,EAAQG,WAAWJ,aACnB,MAAOK,GACP,OAAOR,EAAME,IAAQ,EAEvB,OAAOF,EAAME,IAAQ,oCApDHO,GAClB,OAAOd,EAAcF,GAAKgB,uDAiBLA,UACdd,EAAcF,GAAKgB,mBATRA,EAAKC,GACvB,OAAOf,EAAcF,GAAKgB,GAAOC,mBAiBdR,EAAMS,EAAWC,GACpC,IAAIC,EACJ,IACE,GAAIZ,EAAWC,GAAO,CACpB,IAAME,EAAUC,OAAOH,GACvBW,EAAKT,EAAQO,GAAWG,KAAKV,IAE/B,MAAMW,IACR,OAAOF,GAAMD"}