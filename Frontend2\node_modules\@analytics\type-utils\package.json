{"name": "@analytics/type-utils", "version": "0.6.2", "description": "Tiny runtime type checking utils", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types#readme", "repository": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types", "keywords": ["analytics", "analytics-project", "analytics-util", "events", "types"], "amdName": "utilTypes", "source": "src/index.js", "main": "dist/analytics-util-types.js", "module": "dist/analytics-util-types.module.js", "unpkg": "dist/analytics-util-types.umd.js", "sideEffects": false, "scripts": {"test": "uvu tests -r esm -i setup", "types": "echo '<PERSON><PERSON>EN COMMAND tsc --noEmit false --emitDeclarationOnly true'", "types:broken": "tsc -p --noEmit false --emitDeclarationOnly true", "start": "npm run sync && concurrently 'npm:watch:*' 'npm:copy' 'npm:serve'", "serve": "servor dist index.html 8081 --reload --browse", "copy": "watchlist examples -- npm run sync", "sync": "cp examples/index.html dist", "watch:dev": "microbundle watch --external none -f iife,umd -o dist/browser --no-compress", "watch:test": "watchlist src tests examples -- npm t", "build": "microbundle && npm run build:dev", "build:dev": "microbundle build --external none -f iife,umd -o dist/browser", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish", "deploy": "npm run build:dev && npm run sync && netlify deploy --prod --dir dist --site ac2aee7b-fd8f-45da-9458-3c903c81b2da"}, "files": ["lib", "dist", "package.json", "package-lock.json", "README.md"], "devDependencies": {"concurrently": "^6.1.0", "esm": "^3.2.25", "microbundle": "^0.14.2", "servor": "^4.0.2", "uvu": "^0.5.1", "watchlist": "^0.2.3"}, "gitHead": "904e8bc88a9de0d9e55ecbe1e87ac2ee2a454e32"}