var utilTypes=function(n){var t="function",e="string",r="undefined",o="boolean",i="object",u="array",s="number",c="symbol",a="null",f="asyncFunction",l="generatorFunction",p=function(){},d="none",y="hidden",N="form",E="input",g="button",m="select",O=/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)$/,b=/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,v=/^\{[\s\S]*\}$|^\[[\s\S]*\]$/,A=typeof process!==r?process:{},S=A.env&&A.env.NODE_ENV||"",T="production"===S,R="staging"===S,D="development"===S,h=typeof document!==r,F=h&&"localhost"===window.location.hostname,L=null!=A.versions&&null!=A.versions.node,C=typeof Deno!==r&&typeof Deno.core!==r,I=typeof self===i&&self.constructor&&"DedicatedWorkerGlobalScope"===self.constructor.name,j=h&&"nodejs"===window.name||typeof navigator!==r&&typeof navigator.userAgent!==r&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));function P(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=P.bind(null,"toUpperCase"),_=P.bind(null,"toLowerCase");function B(n){return Y(n)?U(a):typeof n===i?W(n):Object.prototype.toString.call(n).slice(8,-1)}function G(n,t){void 0===t&&(t=!0);var e=B(n);return t?_(e):e}function w(n,t){return typeof t===n}var M=w.bind(null,t),z=w.bind(null,e),Z=w.bind(null,r),k=w.bind(null,o),x=w.bind(null,c);function Y(n){return null===n}function H(n){return G(n)===s&&!isNaN(n)}function J(n){return G(n)===u}function X(n){if(!$(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function $(n){return n&&(typeof n===i||null!==n)}function W(n){return M(n.constructor)?n.constructor.name:null}function V(n){return n instanceof Set}function q(n){return n instanceof Map}function K(n){return n instanceof Error||z(n.message)&&n.constructor&&H(n.constructor.stackTraceLimit)}function Q(n,t){if("object"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=G(new n(""));if(K(t))for(;t;){if(G(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var nn=Q.bind(null,TypeError),tn=Q.bind(null,SyntaxError);function en(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?rn(n,t):e}function rn(n,t){return void 0===t&&(t=""),n&&n.nodeName===t.toUpperCase()}function on(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var un=on(en,N),sn=on(en,g),cn=on(en,E),an=on(en,m);return n.ALL="*",n.ANY="any",n.ARRAY=u,n.ASYNC_FUNCTION=f,n.ASYNC_GENERATOR_FUNCTION="asyncGeneratorFunction",n.BOOLEAN=o,n.BUTTON=g,n.CHANGE="change",n.ENV=S,n.ERROR="error",n.FORM=N,n.FUNCTION=t,n.GENERATOR_FUNCTION=l,n.HIDDEN=y,n.INPUT=E,n.NONE=d,n.NULL=a,n.NUMBER=s,n.OBJECT=i,n.PREFIX="__",n.REGEX_EMAIL=b,n.REGEX_ISO=O,n.REGEX_JSON=v,n.SELECT=m,n.STRING=e,n.SUBMIT="submit",n.SYMBOL=c,n.SYNTAX_ERROR="syntaxError",n.TYPE_ERROR="typeError",n.UNDEFINED=r,n.ctorName=W,n.ensureArray=function(n){return n?J(n)?n:[n]:[]},n.getType=G,n.getTypeName=B,n.isArguments=function(n){try{if(H(n.length)&&M(n.callee))return!0}catch(n){if(-1!==n.message.indexOf("callee"))return!0}return!1},n.isArray=J,n.isAsyncFunction=function(n){return G(n)===f},n.isBoolean=k,n.isBrowser=h,n.isBuffer=function(n){return!(!n.constructor||!M(n.constructor.isBuffer))&&n.constructor.isBuffer(n)},n.isButton=sn,n.isClass=function(n){return!!M(n)&&/^class /.test(Function.prototype.toString.call(n))},n.isDate=function(n){return n instanceof Date||M(n.toDateString)&&M(n.getDate)&&M(n.setDate)},n.isDefined=function(n){return!Z(n)},n.isDeno=C,n.isDev=D,n.isElement=en,n.isEmail=function(n){return!(n.length>320)&&b.test(n)},n.isEmpty=function(n){return!(!Y(n)&&(J(n)?n.length:V(n)||q(n)?n.size:X(n)&&Object.keys(n).length))},n.isError=K,n.isErrorLike=function(n){return $(n)&&z(n.message)&&z(n.name)},n.isFalse=function(n){return!1===n},n.isFalsy=function(n){return!n},n.isForm=un,n.isFunction=M,n.isGenerator=function(n){return $(n)&&M(n.throw)&&M(n.return)&&M(n.next)},n.isGeneratorFunction=function(n){return G(n)===l},n.isHidden=function(n,t){if(!n||getComputedStyle(n).visibility===y)return!0;for(;n;){if(null!=t&&n===t)return!1;if(getComputedStyle(n).display===d)return!0;n=n.parentElement}return!1},n.isInput=cn,n.isIsoDate=function(n){return O.test(n)},n.isJsDom=j,n.isJson=function(n){if(!z(n)||!v.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0},n.isLocalHost=F,n.isMap=q,n.isMethod=function(n,t){return X(n)&&M(n[t])},n.isNoOp=function(n){if(!M(n))return!1;var t=/{(\r|\n|\s)*}/gm,e=p+"";return e===(n.toString().match(t)||[""])[0].replace(t,e)},n.isNode=L,n.isNodeList=function(n){return NodeList.prototype.isPrototypeOf(n)},n.isNodeType=rn,n.isNull=Y,n.isNumber=H,n.isNumberLike=function(n){return!isNaN(parseFloat(n))},n.isObject=X,n.isObjectLike=$,n.isPrimitive=function(n){if(Y(n))return!0;switch(typeof n){case e:case s:case c:case r:case o:return!0;default:return!1}},n.isProd=T,n.isPromise=function(n){return!!n&&!!(!Z(Promise)&&n instanceof Promise||n.then&&M(n.then))},n.isRegex=function(n){return n instanceof RegExp},n.isSelect=an,n.isSet=V,n.isStaging=R,n.isString=z,n.isSymbol=x,n.isSyntaxError=tn,n.isTrue=function(n){return!0===n},n.isTruthy=function(n){return!(z(n)&&"false"===n.toLowerCase()||!n)},n.isTypeError=nn,n.isUndefined=Z,n.isWebWorker=I,n.noOp=p,n}({});
//# sourceMappingURL=type-utils.js.map
