{"version": 3, "file": "analytics-util-localstorage.modern.js", "sources": ["../src/index.js"], "sourcesContent": ["import { get, set, remove, hasSupport, wrap } from '@analytics/global-storage-utils'\n\nconst LOCAL_STORAGE = 'localStorage'\n\n/**\n * Check if browser has access to LocalStorage\n * @returns {Boolean}\n */\nconst hasLocalStorage = hasSupport.bind(null, LOCAL_STORAGE)\n\n/**\n * Get value from localStorage or fallback to global window\n * @param {string} key - Key of value to get\n * @returns {*} value\n */\nconst getItem = wrap(LOCAL_STORAGE, 'getItem', get)\n\n/**\n * Set value to localStorage or fallback to global window\n * @param {string} key - Key of value to set\n * @param {*} value \n * @returns value\n */\nconst setItem = wrap(LOCAL_STORAGE, 'setItem', set)\n\n/**\n * Remove value from localStorage or fallback to global window\n * @param {string} key - Key of value to remove\n */\nconst removeItem = wrap(LOCAL_STORAGE, 'removeItem', remove)\n\nexport {\n  LOCAL_STORAGE,\n  hasLocalStorage,\n  getItem,\n  setItem,\n  removeItem\n}"], "names": ["LOCAL_STORAGE", "hasLocalStorage", "hasSupport", "bind", "getItem", "wrap", "get", "setItem", "set", "removeItem", "remove"], "mappings": "qGAEMA,MAAAA,EAAgB,eAMhBC,EAAkBC,EAAWC,KAAK,KANlB,gBAahBC,EAAUC,EAbM,eAac,UAAWC,GAQzCC,EAAUF,EArBM,eAqBc,UAAWG,GAMzCC,EAAaJ,EA3BG,eA2BiB,aAAcK"}