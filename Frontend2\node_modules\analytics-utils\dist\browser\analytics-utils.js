var analyticUtil=function(e){var n,r,t=(n=function(e,n){e.exports=function(e,n,r,t,o){for(n=n.split?n.split("."):n,t=0;t<n.length;t++)e=e?e[n[t]]:o;return e===o?r:e}},n(r={exports:{}}),r.exports);function o(e){try{return decodeURIComponent(e.replace(/\+/g," "))}catch(e){return null}}"undefined"==typeof process||process;var i="undefined"!=typeof document;function u(e,n){return n.charAt(0)[e]()+n.slice(1)}"undefined"!=typeof Deno&&Deno,i&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var a=u.bind(null,"toUpperCase"),c=u.bind(null,"toLowerCase");function l(e,n){void 0===n&&(n=!0);var r=function(e){return m(e)?a("null"):"object"==typeof e?function(e){return f(e.constructor)?e.constructor.name:null}(e):Object.prototype.toString.call(e).slice(8,-1)}(e);return n?c(r):r}function s(e,n){return typeof n===e}var f=s.bind(null,"function"),d=s.bind(null,"string");function m(e){return null===e}function p(e,n){if("object"!=typeof n||m(n))return!1;if(n instanceof e)return!0;var r=l(new e(""));if(function(e){return e instanceof Error||d(e.message)&&e.constructor&&function(e){return"number"===l(e)&&!isNaN(e)}(e.constructor.stackTraceLimit)}(n))for(;n;){if(l(n)===r)return!0;n=Object.getPrototypeOf(n)}return!1}function g(e,n){var r=e instanceof Element||e instanceof HTMLDocument;return r&&n?function(e,n){return void 0===n&&(n=""),e&&e.nodeName===n.toUpperCase()}(e,n):r}function v(e){var n=[].slice.call(arguments,1);return function(){return e.apply(void 0,[].slice.call(arguments).concat(n))}}function x(e){if(!i)return!1;var n=e||document.referrer;if(n){var r=window.document.location.port,t=n.split("/")[2];return r&&(t=t.replace(":"+r,"")),t!==window.location.hostname}return!1}function h(e,n){var r=(e.split("?")||[,])[1];if(!r||-1===r.indexOf(n))return e;var t=new RegExp("(\\&|\\?)"+n+'([_A-Za-z0-9"+=.\\/\\-@%]+)',"g"),o=("?"+r).replace(t,"").replace(/^&/,"?");return e.replace("?"+r,o)}function y(e){return function(e){for(var n,r=Object.create(null),t=/([^&=]+)=?([^&]*)/g;n=t.exec(e);){var i=o(n[1]),u=o(n[2]);if(i)if("[]"===i.substring(i.length-2)){var a=r[i=i.substring(0,i.length-2)]||(r[i]=[]);r[i]=Array.isArray(a)?a:[],r[i].push(u)}else r[i]=""===u||u}for(var c in r){var l=c.split("[");l.length>1&&(b(r,l.map(function(e){return e.replace(/[?[\]\\ ]/g,"")}),r[c]),delete r[c])}return r}(function(e){if(e){var n=e.match(/\?(.*)/);return n&&n[1]?n[1].split("#")[0]:""}return i&&window.location.search.substring(1)}(e))}function b(e,n,r){for(var t=n.length-1,o=0;o<t;++o){var i=n[o];if("__proto__"===i||"constructor"===i)break;i in e||(e[i]={}),e=e[i]}e[n[t]]=r}function w(e){if(!i)return null;var n=document.createElement("a");return n.setAttribute("href",e),n.hostname}function j(e){return(w(e)||"").split(".").slice(-2).join(".")}function O(e){var n=e.split(".");return n.length>1?n.slice(0,-1).join("."):e}s.bind(null,"undefined"),s.bind(null,"boolean"),s.bind(null,"symbol"),p.bind(null,TypeError),p.bind(null,SyntaxError),v(g,"form"),v(g,"button"),v(g,"input"),v(g,"select");var E={trimTld:O,getDomainBase:j,getDomainHost:w},T="google",A="q",D={"daum.net":A,"eniro.se":"search_word","naver.com":"query","yahoo.com":"p","msn.com":A,"aol.com":A,"ask.com":A,"baidu.com":"wd","yandex.com":"text","rambler.ru":"words",google:A,"bing.com":{p:A,n:"live"}};return e.decodeUri=o,e.dotProp=t,e.getBrowserLocale=function(){if(i){var e=navigator,n=e.languages;return e.userLanguage||(n&&n.length?n[0]:e.language)}},e.getTimeZone=function(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){}},e.isExternalReferrer=x,e.isScriptLoaded=function(e){if(!i)return!0;var n=document.getElementsByTagName("script");return!!Object.keys(n).filter(function(r){var t=n[r].src;return d(e)?-1!==t.indexOf(e):e instanceof RegExp&&t.match(e)}).length},e.paramsClean=h,e.paramsGet=function(e,n){return o((RegExp(e+"=(.+?)(&|$)").exec(n)||[,""])[1])},e.paramsParse=y,e.paramsRemove=function(e,n){return i?new Promise(function(r,t){if(window.history&&window.history.replaceState){var o=window.location.href,i=h(o,e);o!==i&&history.replaceState({},"",i)}return n&&n(),r()}):Promise.resolve()},e.parseReferrer=function(e,n){if(!i)return!1;var r={source:"(direct)",medium:"(none)",campaign:"(not set)"};e&&x(e)&&(r.referrer=e);var t=function(e){if(!e||!i)return!1;var n=j(e),r=document.createElement("a");if(r.href=e,r.hostname.indexOf(T)>-1&&(n=T),D[n]){var t=D[n],o=new RegExp(("string"==typeof t?t:t.p)+"=.*?([^&#]*|$)","gi"),u=r.search.match(o);return{source:t.n||O(n),medium:"organic",term:(u?u[0].split("=")[1]:"")||"(not provided)"}}var a=x(e)?"referral":"internal";return{source:r.hostname,medium:a}}(e);t&&Object.keys(t).length&&(r=Object.assign({},r,t));var o=y(n),u=Object.keys(o);if(!u.length)return r;var a=u.reduce(function(e,n){return n.match(/^utm_/)&&(e[""+n.replace(/^utm_/,"")]=o[n]),n.match(/^(d|g)clid/)&&(e.source=T,e.medium=o.gclid?"cpc":"cpm",e[n]=o[n]),e},{});return Object.assign({},r,a)},e.throttle=function(e,n){var r,t,o,i=null,u=0,a=function(){u=new Date,i=null,o=e.apply(r,t)};return function(){var c=new Date;u||(u=c);var l=n-(c-u);return r=this,t=arguments,l<=0?(clearTimeout(i),i=null,u=c,o=e.apply(r,t)):i||(i=setTimeout(a,l)),o}},e.url=E,e.uuid=function(){for(var e="",n=0,r=4294967295*Math.random()|0;n++<36;){var t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx"[n-1],o=15&r;e+="-"==t||"4"==t?t:("x"==t?o:3&o|8).toString(16),r=n%8==0?4294967295*Math.random()|0:r>>4}return e},e}({});
//# sourceMappingURL=analytics-utils.js.map
