'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

function gaNode(pluginConfig) {
  return {
    name: 'google-analytics',
    initialize: function initialize(_ref) {// console.log('has no server implementation yet')

      _ref.config;
    }
  };
}

/* This module will shake out unused code + work in browser and node 🎉 */

var index = gaNode;
/* init for CDN usage. globalName.init() */

var init = gaNode;

exports["default"] = index;
exports.init = init;
