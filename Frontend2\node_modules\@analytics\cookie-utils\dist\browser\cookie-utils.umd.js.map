{"version": 3, "file": "cookie-utils.umd.js", "sources": ["../../../analytics-util-types/dist/analytics-util-types.module.js", "../../../analytics-util-storage-global/dist/analytics-util-global-storage.module.js", "../../src/index.js"], "sourcesContent": ["var n=\"function\",t=\"string\",e=\"undefined\",r=\"boolean\",o=\"object\",u=\"array\",i=\"number\",c=\"symbol\",a=\"null\",f=\"error\",s=\"typeError\",l=\"syntaxError\",d=\"asyncFunction\",p=\"generatorFunction\",y=\"asyncGeneratorFunction\",g=function(){},b=\"any\",m=\"*\",v=\"none\",h=\"hidden\",j=\"__\",O=\"form\",S=\"input\",A=\"button\",E=\"select\",N=\"change\",w=\"submit\",D=/^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/,z=/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z=/^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/,F=\"undefined\"!=typeof process?process:{},P=F.env&&F.env.NODE_ENV||\"\",x=\"production\"===P,C=\"staging\"===P,L=\"development\"===P,$=\"undefined\"!=typeof document,T=$&&\"localhost\"===window.location.hostname,_=null!=F.versions&&null!=F.versions.node,k=\"undefined\"!=typeof Deno&&void 0!==Deno.core,B=\"object\"==typeof self&&self.constructor&&\"DedicatedWorkerGlobalScope\"===self.constructor.name,G=$&&\"nodejs\"===window.name||\"undefined\"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes(\"Node.js\")||navigator.userAgent.includes(\"jsdom\"));function M(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=M.bind(null,\"toUpperCase\"),H=M.bind(null,\"toLowerCase\");function J(n){return Y(n)?U(\"null\"):\"object\"==typeof n?yn(n):Object.prototype.toString.call(n).slice(8,-1)}function R(n,t){void 0===t&&(t=!0);var e=J(n);return t?H(e):e}function V(n,t){return typeof t===n}var W=V.bind(null,\"function\"),q=V.bind(null,\"string\"),I=V.bind(null,\"undefined\");function K(n){return!I(n)}var Q=V.bind(null,\"boolean\"),X=V.bind(null,\"symbol\");function Y(n){return null===n}function nn(n){return\"number\"===R(n)&&!isNaN(n)}function tn(n){return!isNaN(parseFloat(n))}function en(n){return!!W(n)&&/^class /.test(Function.prototype.toString.call(n))}function rn(n){return\"array\"===R(n)}function on(n){if(!un(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function un(n){return n&&(\"object\"==typeof n||null!==n)}function cn(n){if(!q(n)||!Z.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0}function an(n){if(Y(n))return!0;switch(typeof n){case\"string\":case\"number\":case\"symbol\":case\"undefined\":case\"boolean\":return!0;default:return!1}}function fn(n,t){return on(n)&&W(n[t])}function sn(n){return!!n&&!!(!I(Promise)&&n instanceof Promise||n.then&&W(n.then))}function ln(n){return un(n)&&W(n.throw)&&W(n.return)&&W(n.next)}function dn(n){return\"generatorFunction\"===R(n)}function pn(n){return\"asyncFunction\"===R(n)}function yn(n){return W(n.constructor)?n.constructor.name:null}function gn(n){return n instanceof Set}function bn(n){return n instanceof Map}function mn(n){return n instanceof RegExp}function vn(n){return!(!n.constructor||!W(n.constructor.isBuffer))&&n.constructor.isBuffer(n)}function hn(n){return n instanceof Error||q(n.message)&&n.constructor&&nn(n.constructor.stackTraceLimit)}function jn(n){return un(n)&&q(n.message)&&q(n.name)}function On(n,t){if(\"object\"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=R(new n(\"\"));if(hn(t))for(;t;){if(R(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var Sn=On.bind(null,TypeError),An=On.bind(null,SyntaxError);function En(n){if(!W(n))return!1;var t=/{(\\r|\\n|\\s)*}/gm,e=g+\"\";return e===(n.toString().match(t)||[\"\"])[0].replace(t,e)}function Nn(n){try{if(nn(n.length)&&W(n.callee))return!0}catch(n){if(-1!==n.message.indexOf(\"callee\"))return!0}return!1}function wn(n){return!(q(n)&&\"false\"===n.toLowerCase()||!n)}function Dn(n){return!n}function zn(n){return!0===n}function Zn(n){return!1===n}function Fn(n){return!(n.length>320)&&z.test(n)}function Pn(n){return n instanceof Date||W(n.toDateString)&&W(n.getDate)&&W(n.setDate)}function xn(n){return D.test(n)}function Cn(n){return!(!Y(n)&&(rn(n)?n.length:gn(n)||bn(n)?n.size:on(n)&&Object.keys(n).length))}function Ln(n){return NodeList.prototype.isPrototypeOf(n)}function $n(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?Tn(n,t):e}function Tn(n,t){return void 0===t&&(t=\"\"),n&&n.nodeName===t.toUpperCase()}function _n(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var kn=_n($n,\"form\"),Bn=_n($n,\"button\"),Gn=_n($n,\"input\"),Mn=_n($n,\"select\");function Un(n,t){if(!n||\"hidden\"===getComputedStyle(n).visibility)return!0;for(;n;){if(null!=t&&n===t)return!1;if(\"none\"===getComputedStyle(n).display)return!0;n=n.parentElement}return!1}function Hn(n){return n?rn(n)?n:[n]:[]}export{m as ALL,b as ANY,u as ARRAY,d as ASYNC_FUNCTION,y as ASYNC_GENERATOR_FUNCTION,r as BOOLEAN,A as BUTTON,N as CHANGE,P as ENV,f as ERROR,O as FORM,n as FUNCTION,p as GENERATOR_FUNCTION,h as HIDDEN,S as INPUT,v as NONE,a as NULL,i as NUMBER,o as OBJECT,j as PREFIX,z as REGEX_EMAIL,D as REGEX_ISO,Z as REGEX_JSON,E as SELECT,t as STRING,w as SUBMIT,c as SYMBOL,l as SYNTAX_ERROR,s as TYPE_ERROR,e as UNDEFINED,yn as ctorName,Hn as ensureArray,R as getType,J as getTypeName,Nn as isArguments,rn as isArray,pn as isAsyncFunction,Q as isBoolean,$ as isBrowser,vn as isBuffer,Bn as isButton,en as isClass,Pn as isDate,K as isDefined,k as isDeno,L as isDev,$n as isElement,Fn as isEmail,Cn as isEmpty,hn as isError,jn as isErrorLike,Zn as isFalse,Dn as isFalsy,kn as isForm,W as isFunction,ln as isGenerator,dn as isGeneratorFunction,Un as isHidden,Gn as isInput,xn as isIsoDate,G as isJsDom,cn as isJson,T as isLocalHost,bn as isMap,fn as isMethod,En as isNoOp,_ as isNode,Ln as isNodeList,Tn as isNodeType,Y as isNull,nn as isNumber,tn as isNumberLike,on as isObject,un as isObjectLike,an as isPrimitive,x as isProd,sn as isPromise,mn as isRegex,Mn as isSelect,gn as isSet,C as isStaging,q as isString,X as isSymbol,An as isSyntaxError,zn as isTrue,wn as isTruthy,Sn as isTypeError,I as isUndefined,B as isWebWorker,g as noOp};\n//# sourceMappingURL=analytics-util-types.module.js.map\n", "import{PREFIX as t,OBJECT as e,UNDEFINED as r}from\"@analytics/type-utils\";var l=\"global\",o=t+\"global\"+t,n=typeof self===e&&self.self===self&&self||typeof global===e&&global.global===global&&global||void 0;function a(t){return n[o][t]}function f(t,e){return n[o][t]=e}function i(t){delete n[o][t]}function u(t,e,r){var l;try{if(b(t)){var o=window[t];l=o[e].bind(o)}}catch(t){}return l||r}n[o]||(n[o]={});var c={};function b(t){if(typeof c[t]!==r)return c[t];try{var e=window[t];e.setItem(r,r),e.removeItem(r)}catch(e){return c[t]=!1}return c[t]=!0}export{l as GLOBAL,o as KEY,a as get,n as globalContext,b as hasSupport,i as remove,f as set,u as wrap};\n//# sourceMappingURL=analytics-util-global-storage.module.js.map\n", "import { get, set, remove } from '@analytics/global-storage-utils'\n\nexport const COOKIE = 'cookie'\n\nlet isSupported = hasCookies()\n\n/**\n * Get a cookie value\n * @param  {string} name - key of cookie\n * @return {string} value of cookie\n */\nexport const getCookie = cookie\n\n/**\n * Set a cookie value\n * @param {string} name  - key of cookie\n * @param {string} value - value of cookie\n * @param {string} days  - days to keep cookie\n */\nexport const setCookie = cookie\n\n/**\n * Remove a cookie value.\n * @param {string} name  - key of cookie\n */\nexport function removeCookie(name) {\n  return isSupported ? cookie(name, '', -1) : remove(name) \n}\n\n/**\n * Check if browser has cookie support\n * @returns {boolean}\n */\nexport function hasCookies() {\n  if (typeof isSupported !== 'undefined') {\n    return isSupported\n  }\n  const tmp = COOKIE + COOKIE\n  try {\n    // Try to set cookie\n    cookie(tmp, tmp)\n    isSupported = document.cookie.indexOf(tmp) !== -1\n    // Cleanup cookie\n    removeCookie(tmp)\n  } catch (e) {\n    isSupported = false\n  }\n  return isSupported\n}\n\n/** \n  * Cookie setter & getter\n  * @version    1.0.4\n  * @date       2015-03-13\n  * @stability  3 - Stable\n  * <AUTHOR> <<EMAIL>>\n  * @license    MIT License\n  * Modified by David Wells\n  * @param {string} name \n  * @param {*} value \n  * @param {*} ttl - Time to live in seconds\n  * @param {*} path - Cookie domain\n  * @param {*} domain - Cookie domain\n  * @param {Boolean} secure - secure cookie\n  * @returns {*} value\n  * @example\n    cookie('test', 'a') // set\n    cookie('test', 'a', 60*60*24, '/api', '*.example.com', true) // complex set - cookie(name, value, ttl, path, domain, secure)\n    cookie('test') // get\n    cookie('test', '', -1) // destroy\n*/\nfunction cookie(name, value, ttl, path, domain, secure) {\n  if (typeof window === 'undefined') return\n  const isSet = arguments.length > 1\n  /* If cookies not supported fallback to global */\n  if (isSupported === false) (isSet) ? set(name, value) : get(name)\n  /* Set values */\n  if (isSet) {\n    return document.cookie = name + '=' + encodeURIComponent(value) +\n      /* Set TTL set expiration on cookie */\n      ((!ttl) ? '' : '; expires=' + new Date(+new Date() + (ttl * 1000)).toUTCString() +\n      // If path set path\n      ((!path) ? '' : '; path=' + path) +\n      // If domain set domain\n      ((!domain) ? '' : '; domain=' + domain) +\n      // If secure set secure\n      ((!secure) ? '' : '; secure'))\n  }\n  /* Get values */\n  return decodeURIComponent((('; ' + document.cookie).split('; ' + name + '=')[1] || '').split(';')[0])\n}\n"], "names": ["OBJECT", "process", "<PERSON><PERSON><PERSON><PERSON>", "method", "s", "char<PERSON>t", "slice", "<PERSON><PERSON>", "window", "name", "navigator", "userAgent", "includes", "text", "bind", "lower", "val", "toLowerCase", "upper", "x", "constructor", "ctorName", "Object", "prototype", "toString", "call", "getTypeName", "type", "kind", "typeOf", "isString", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNull", "getType", "message", "n", "isNaN", "isNumber", "stackTraceLimit", "isError", "typeName", "getPrototypeOf", "element", "nodeName", "toUpperCase", "isNodeType", "isEl", "fn", "boundArgs", "errorType", "TypeError", "SyntaxError", "bind<PERSON><PERSON><PERSON>", "isElement", "KEY", "PREFIX", "globalContext", "self", "global", "this", "key", "COOKIE", "isSupported", "hasCookies", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "tmp", "document", "indexOf", "e", "ttl", "path", "domain", "secure", "isSet", "arguments", "length", "set", "get", "encodeURIComponent", "Date", "toUTCString", "decodeURIComponent", "split"], "mappings": "yOASaA,EAAS,SAFG,6BAwCsBC,YAelCC,EAvDY,6BA2EzB,WAAcC,EAAQC,GACpB,SAASC,OAAO,GAAGF,KAAYC,EAAEE,MAAM,GA5EhB,0BA+DiCC,KAMlCL,GAA6B,WAAhBM,OAAOC,MArEnB,oCAqEiDC,IAAkCA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,gBAU5MC,EAAKC,KAAK,KAAM,eACxBC,EAAQF,EAAKC,KAAK,KAAM,0BAiBNE,EAAKC,sBAC3B,MAlB4B,SAOFD,GAC1B,SAAWA,GAAaE,EAlFN,QAJE,4BA+VGC,GACvB,SAAkBA,EAAEC,aAAeD,EAAEC,YAAYX,KAAO,KAzQvBY,CAASL,GAAOM,OAAOC,UAAUC,SAASC,KAAKT,GAAKV,MAAM,GAAI,GASlFoB,CAAYV,GAEzB,SAAuBD,EAAMY,GAAQA,aAUvBC,EAAMZ,GACpB,kBAAsBY,QAQEC,EAAOf,KAAK,KAzHd,YAgIXgB,EAAWD,EAAOf,KAAK,KA/Hd,UAMA,WA8JCK,GACrB,cAAOA,aA4QUY,EAASC,GAC1B,GAAqB,oBAAYC,EAAOD,GAAQ,SAEhD,GAAIA,eAA0B,SAC9B,MAAiBE,EAAQ,MAAY,KAErC,YAnCsBf,GACtB,2BAA8BW,EAASX,EAAEgB,UAAYhB,EAAEC,sBAvNhCgB,GACvB,MA1LoB,WA0LbF,EAAQE,KAAkBC,MAAMD,GAsN+BE,CAASnB,EAAEC,YAAYmB,iBAkCzFC,CAAQR,GACV,KAAOA,GAAO,CACZ,GAAIE,EAAQF,KAAWS,EACrB,SAEAT,EAAQV,OAAOoB,eAAeV,uBA8JZW,EAAShB,GACjC,MAAagB,sBAA8BA,0BAC3C,UAAchB,WAUWgB,EAAShB,GAClC,gBADkCA,IAAAA,EAAO,IAClCgB,GAAWA,EAAQC,WAAajB,EAAKkB,cAVrCC,CAAWH,EAAShB,GADAoB,aAcXC,oCAChB,kBACE,sDAAsBC,KAteCpB,EAAOf,KAAK,KArId,aAoJAe,EAAOf,KAAK,KAnJd,WAyJCe,EAAOf,KAAK,KArJd,UA4bKoC,EAAUpC,KAAK,KAAMqC,WAEnBD,EAAUpC,KAAK,KAAMsC,aAiL5BC,EAASC,EA9lBX,QAqmBID,EAASC,EAnmBX,UA0mBCD,EAASC,EA3mBX,SAknBGD,EAASC,EAhnBX,cC5BTC,EAAMC,aAENC,gBAAiCzD,GAAU0D,KAAKA,OAASA,MAAQA,sBAA4B1D,GAAU2D,OAAMA,SAAaA,QAAUA,aAAAA,EAAWC,WAWxIC,GAClB,SAAqBN,GAAKM,cASRA,EAAK7B,GACvB,SAAqBuB,GAAKM,GAAO7B,IAnBhBuB,KACjBE,EAAcF,GAAO,QCRVO,EAAS,SAElBC,EAAcC,IAOLC,EAAYC,EAQZC,EAAYD,WAMTE,EAAa3D,GAC3B,OAAOsD,EAAcG,EAAOzD,EAAM,IAAK,iBDUlB8C,GCV8B9C,YAOrCuD,IACd,QAA2B,IAAhBD,EACT,OAAOA,EAET,IAAMM,EAAMP,EAASA,EACrB,IAEEI,EAAOG,EAAKA,GACZN,GAAgD,IAAlCO,SAASJ,OAAOK,QAAQF,GAEtCD,EAAaC,GACb,MAAOG,GACPT,GAAc,EAEhB,OAAOA,EAwBT,SAASG,EAAOzD,EAAMuB,EAAOyC,EAAKC,EAAMC,EAAQC,GAC9C,GAAsB,oBAAXpE,OAAX,CACA,IAAMqE,EAAQC,UAAUC,OAAS,EAIjC,OAFoB,IAAhBhB,IAAwBc,EAASG,EAAIvE,EAAMuB,GAASiD,EAAIxE,IAExDoE,EACKP,SAASJ,OAASzD,EAAO,IAAMyE,mBAAmBlD,IAEpDyC,EAAY,aAAe,IAAIU,MAAM,IAAIA,KAAgB,IAANV,GAAaW,eAEhEV,EAAa,UAAYA,EAAjB,KAERC,EAAe,YAAcA,EAAnB,KAEVC,EAAe,WAAL,IANH,IASPS,qBAAqB,KAAOf,SAASJ,QAAQoB,MAAM,KAAO7E,EAAO,KAAK,IAAM,IAAI6E,MAAM,KAAK"}