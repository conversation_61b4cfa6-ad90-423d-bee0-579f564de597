# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [1.0.14](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.14) (2024-12-12)


### Bug Fixes

* [#391](https://github.com/DavidWells/analytics/issues/391) param parse ([b693e2b](https://github.com/DavidWells/analytics/commit/b693e2ba07450e84130a752f3e1a5dc7646a4e8a))





## [1.0.13](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.13) (2024-12-11)


### Bug Fixes

* [#453](https://github.com/DavidWells/analytics/issues/453) param parser ([18c3671](https://github.com/DavidWells/analytics/commit/18c36712219957942ec6fdc98718caa6582461c9))





## [1.0.12](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.12) (2023-05-27)

**Note:** Version bump only for package analytics-utils





## [1.0.11](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.11) (2023-05-27)

**Note:** Version bump only for package analytics-utils





## [1.0.10](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.10) (2022-03-18)

**Note:** Version bump only for package analytics-utils





## [1.0.9](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.9) (2022-02-05)

**Note:** Version bump only for package analytics-utils





## [1.0.8](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.8) (2022-01-03)

**Note:** Version bump only for package analytics-utils





## [1.0.7](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.7) (2022-01-02)

**Note:** Version bump only for package analytics-utils





## [1.0.6](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.6) (2021-12-12)

**Note:** Version bump only for package analytics-utils





## [1.0.5](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.5) (2021-10-24)

**Note:** Version bump only for package analytics-utils





## [1.0.4](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.4) (2021-10-17)

**Note:** Version bump only for package analytics-utils





## [1.0.3](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.3) (2021-08-27)


### Bug Fixes

* remove prototype issue ([5acaea4](https://github.com/DavidWells/analytics/commit/5acaea4))





## [1.0.2](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.2) (2021-08-05)

**Note:** Version bump only for package analytics-utils





## [1.0.1](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.1) (2021-08-05)

**Note:** Version bump only for package analytics-utils





# [1.0.0](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@1.0.0) (2021-07-31)


### Code Refactoring

* streamline analytics-utils ([641a917](https://github.com/DavidWells/analytics/commit/641a917))


### BREAKING CHANGES

* removes types, noOp, & storage

Signed-off-by: David Wells <>





## [0.4.8](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.8) (2021-07-28)

**Note:** Version bump only for package analytics-utils





## [0.4.7](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.7) (2021-07-26)

**Note:** Version bump only for package analytics-utils





## [0.4.6](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.6) (2021-07-20)

**Note:** Version bump only for package analytics-utils





## [0.4.5](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.5) (2021-07-20)

**Note:** Version bump only for package analytics-utils





## [0.4.4](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.4) (2021-03-20)

**Note:** Version bump only for package analytics-utils





## [0.4.3](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.3) (2021-03-11)

**Note:** Version bump only for package analytics-utils





## [0.4.2](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.2) (2021-01-04)

**Note:** Version bump only for package analytics-utils





## [0.4.1](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.1) (2020-12-23)

**Note:** Version bump only for package analytics-utils





# [0.4.0](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.4.0) (2020-12-09)


### Features

* add throttle util ([22af474](https://github.com/DavidWells/analytics/commit/22af474))





# [0.3.0](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.3.0) (2020-12-02)


### Features

* add scroll util ([eb6aa1a](https://github.com/DavidWells/analytics/commit/eb6aa1a))





## [0.2.2](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.2.2) (2020-07-14)

**Note:** Version bump only for package analytics-utils





## [0.2.1](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.2.1) (2020-04-21)

**Note:** Version bump only for package analytics-utils





# [0.2.0](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.2.0) (2020-04-16)


### Features

* add dlv for dotpot ([f1a2771](https://github.com/DavidWells/analytics/commit/f1a2771))
* add shared type checking functions ([46729af](https://github.com/DavidWells/analytics/commit/46729af))





## [0.1.2](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.1.2) (2019-10-03)


### Bug Fixes

* storage import ref ([5b44ea6](https://github.com/DavidWells/analytics/commit/5b44ea6))





## [0.1.1](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.1.1) (2019-08-14)

**Note:** Version bump only for package analytics-utils





# [0.1.0](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.1.0) (2019-07-13)


### Features

* add onRouteChange utility ([bf8701b](https://github.com/DavidWells/analytics/commit/bf8701b))





## [0.0.22](https://github.com/DavidWells/analytics/compare/<EMAIL>-utils@0.0.22) (2019-07-13)


### Bug Fixes

* **utils:** fix build for node & testing + add iife build ([20a5021](https://github.com/DavidWells/analytics/commit/20a5021))
* **utils:** remove babel transpiler. [@todo](https://github.com/todo) fix so utils transpiles and is consumable by analytics iife ([d3b8f58](https://github.com/DavidWells/analytics/commit/d3b8f58))


### Features

* **utils:** add adblock detector utility ([2b03223](https://github.com/DavidWells/analytics/commit/2b03223))
* **utils:** add browser locale ([0799127](https://github.com/DavidWells/analytics/commit/0799127))
* **utils:** add browserLocal util ([f884941](https://github.com/DavidWells/analytics/commit/f884941))
* **utils:** add clientside url parsing utils ([174805d](https://github.com/DavidWells/analytics/commit/174805d))
* **utils:** add cookie utils ([0c92eb9](https://github.com/DavidWells/analytics/commit/0c92eb9))
* **utils:** add extendApi method for easier extending of provider plugins ([2fe0851](https://github.com/DavidWells/analytics/commit/2fe0851))
* **utils:** add get timeZone util ([101ac1e](https://github.com/DavidWells/analytics/commit/101ac1e))
* **utils:** add isScriptLoaded util ([6e53aa5](https://github.com/DavidWells/analytics/commit/6e53aa5))
* **utils:** add noOp util ([3283f6f](https://github.com/DavidWells/analytics/commit/3283f6f))
* add onRouteChange utility ([bf8701b](https://github.com/DavidWells/analytics/commit/bf8701b))
* **utils:** add ParseReferrer Util ([75cff90](https://github.com/DavidWells/analytics/commit/75cff90))
* **utils:** add storage util ([ad8f6c6](https://github.com/DavidWells/analytics/commit/ad8f6c6))
* **utils:** update storage and allow for setting/getting values from specific locations ([7b730d4](https://github.com/DavidWells/analytics/commit/7b730d4))
