{"version": 3, "file": "analytics-utils.umd.js", "sources": ["../../src/decodeUri.js", "../../../analytics-util-types/dist/analytics-util-types.module.js", "../../src/isExternalReferrer.js", "../../src/paramsClean.js", "../../src/paramsParse.js", "../../src/url.js", "../../src/parseReferrer.js", "../../src/getBrowserLocale.js", "../../src/getTimeZone.js", "../../src/isScriptLoaded.js", "../../src/paramsGet.js", "../../src/paramsRemove.js", "../../src/throttle.js", "../../src/uuid.js"], "sourcesContent": ["/**\n * Decode URI string\n *\n * @param {String} s string to decode\n * @returns {String} decoded string\n * @example\n * decode(\"Bought%20keyword)\n * => \"Bought keyword\"\n */\nexport function decodeUri(s) {\n  try {\n    return decodeURIComponent(s.replace(/\\+/g, ' '))\n  } catch (e) {\n    return null\n  }\n}\n", "var n=\"function\",t=\"string\",e=\"undefined\",r=\"boolean\",o=\"object\",u=\"array\",i=\"number\",c=\"symbol\",a=\"null\",f=\"error\",s=\"typeError\",l=\"syntaxError\",d=\"asyncFunction\",p=\"generatorFunction\",y=\"asyncGeneratorFunction\",g=function(){},b=\"any\",m=\"*\",v=\"none\",h=\"hidden\",j=\"__\",O=\"form\",S=\"input\",A=\"button\",E=\"select\",N=\"change\",w=\"submit\",D=/^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/,z=/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z=/^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/,F=\"undefined\"!=typeof process?process:{},P=F.env&&F.env.NODE_ENV||\"\",x=\"production\"===P,C=\"staging\"===P,L=\"development\"===P,$=\"undefined\"!=typeof document,T=$&&\"localhost\"===window.location.hostname,_=null!=F.versions&&null!=F.versions.node,k=\"undefined\"!=typeof Deno&&void 0!==Deno.core,B=\"object\"==typeof self&&self.constructor&&\"DedicatedWorkerGlobalScope\"===self.constructor.name,G=$&&\"nodejs\"===window.name||\"undefined\"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes(\"Node.js\")||navigator.userAgent.includes(\"jsdom\"));function M(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=M.bind(null,\"toUpperCase\"),H=M.bind(null,\"toLowerCase\");function J(n){return Y(n)?U(\"null\"):\"object\"==typeof n?yn(n):Object.prototype.toString.call(n).slice(8,-1)}function R(n,t){void 0===t&&(t=!0);var e=J(n);return t?H(e):e}function V(n,t){return typeof t===n}var W=V.bind(null,\"function\"),q=V.bind(null,\"string\"),I=V.bind(null,\"undefined\");function K(n){return!I(n)}var Q=V.bind(null,\"boolean\"),X=V.bind(null,\"symbol\");function Y(n){return null===n}function nn(n){return\"number\"===R(n)&&!isNaN(n)}function tn(n){return!isNaN(parseFloat(n))}function en(n){return!!W(n)&&/^class /.test(Function.prototype.toString.call(n))}function rn(n){return\"array\"===R(n)}function on(n){if(!un(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function un(n){return n&&(\"object\"==typeof n||null!==n)}function cn(n){if(!q(n)||!Z.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0}function an(n){if(Y(n))return!0;switch(typeof n){case\"string\":case\"number\":case\"symbol\":case\"undefined\":case\"boolean\":return!0;default:return!1}}function fn(n,t){return on(n)&&W(n[t])}function sn(n){return!!n&&!!(!I(Promise)&&n instanceof Promise||n.then&&W(n.then))}function ln(n){return un(n)&&W(n.throw)&&W(n.return)&&W(n.next)}function dn(n){return\"generatorFunction\"===R(n)}function pn(n){return\"asyncFunction\"===R(n)}function yn(n){return W(n.constructor)?n.constructor.name:null}function gn(n){return n instanceof Set}function bn(n){return n instanceof Map}function mn(n){return n instanceof RegExp}function vn(n){return!(!n.constructor||!W(n.constructor.isBuffer))&&n.constructor.isBuffer(n)}function hn(n){return n instanceof Error||q(n.message)&&n.constructor&&nn(n.constructor.stackTraceLimit)}function jn(n){return un(n)&&q(n.message)&&q(n.name)}function On(n,t){if(\"object\"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=R(new n(\"\"));if(hn(t))for(;t;){if(R(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var Sn=On.bind(null,TypeError),An=On.bind(null,SyntaxError);function En(n){if(!W(n))return!1;var t=/{(\\r|\\n|\\s)*}/gm,e=g+\"\";return e===(n.toString().match(t)||[\"\"])[0].replace(t,e)}function Nn(n){try{if(nn(n.length)&&W(n.callee))return!0}catch(n){if(-1!==n.message.indexOf(\"callee\"))return!0}return!1}function wn(n){return!(q(n)&&\"false\"===n.toLowerCase()||!n)}function Dn(n){return!n}function zn(n){return!0===n}function Zn(n){return!1===n}function Fn(n){return!(n.length>320)&&z.test(n)}function Pn(n){return n instanceof Date||W(n.toDateString)&&W(n.getDate)&&W(n.setDate)}function xn(n){return D.test(n)}function Cn(n){return!(!Y(n)&&(rn(n)?n.length:gn(n)||bn(n)?n.size:on(n)&&Object.keys(n).length))}function Ln(n){return NodeList.prototype.isPrototypeOf(n)}function $n(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?Tn(n,t):e}function Tn(n,t){return void 0===t&&(t=\"\"),n&&n.nodeName===t.toUpperCase()}function _n(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var kn=_n($n,\"form\"),Bn=_n($n,\"button\"),Gn=_n($n,\"input\"),Mn=_n($n,\"select\");function Un(n,t){if(!n||\"hidden\"===getComputedStyle(n).visibility)return!0;for(;n;){if(null!=t&&n===t)return!1;if(\"none\"===getComputedStyle(n).display)return!0;n=n.parentElement}return!1}function Hn(n){return n?rn(n)?n:[n]:[]}export{m as ALL,b as ANY,u as ARRAY,d as ASYNC_FUNCTION,y as ASYNC_GENERATOR_FUNCTION,r as BOOLEAN,A as BUTTON,N as CHANGE,P as ENV,f as ERROR,O as FORM,n as FUNCTION,p as GENERATOR_FUNCTION,h as HIDDEN,S as INPUT,v as NONE,a as NULL,i as NUMBER,o as OBJECT,j as PREFIX,z as REGEX_EMAIL,D as REGEX_ISO,Z as REGEX_JSON,E as SELECT,t as STRING,w as SUBMIT,c as SYMBOL,l as SYNTAX_ERROR,s as TYPE_ERROR,e as UNDEFINED,yn as ctorName,Hn as ensureArray,R as getType,J as getTypeName,Nn as isArguments,rn as isArray,pn as isAsyncFunction,Q as isBoolean,$ as isBrowser,vn as isBuffer,Bn as isButton,en as isClass,Pn as isDate,K as isDefined,k as isDeno,L as isDev,$n as isElement,Fn as isEmail,Cn as isEmpty,hn as isError,jn as isErrorLike,Zn as isFalse,Dn as isFalsy,kn as isForm,W as isFunction,ln as isGenerator,dn as isGeneratorFunction,Un as isHidden,Gn as isInput,xn as isIsoDate,G as isJsDom,cn as isJson,T as isLocalHost,bn as isMap,fn as isMethod,En as isNoOp,_ as isNode,Ln as isNodeList,Tn as isNodeType,Y as isNull,nn as isNumber,tn as isNumberLike,on as isObject,un as isObjectLike,an as isPrimitive,x as isProd,sn as isPromise,mn as isRegex,Mn as isSelect,gn as isSet,C as isStaging,q as isString,X as isSymbol,An as isSyntaxError,zn as isTrue,wn as isTruthy,Sn as isTypeError,I as isUndefined,B as isWebWorker,g as noOp};\n//# sourceMappingURL=analytics-util-types.module.js.map\n", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * @param {string | null | undefined} ref\n * @returns {boolean | undefined}\n */\nexport function isExternalReferrer(ref) {\n  if (!isBrowser) return false\n  const referrer = ref || document.referrer\n  if (referrer) {\n    const port = window.document.location.port\n    let ref = referrer.split('/')[2]\n    if (port) {\n      ref = ref.replace(`:${port}`, '')\n    }\n    return ref !== window.location.hostname\n  }\n  return false\n}\n", "\n/**\n * Strip a query parameter from a url string\n * @param  {string} url   - url with query parameters\n * @param  {string} param - parameter to strip\n * @return {string} cleaned url\n */\nexport function paramsClean(url, param) {\n  const search = (url.split('?') || [ , ])[1] // eslint-disable-line\n  if (!search || search.indexOf(param) === -1) {\n    return url\n  }\n  // remove all utm params from URL search\n  const regex = new RegExp(`(\\\\&|\\\\?)${param}([_A-Za-z0-9\"+=.\\\\/\\\\-@%]+)`, 'g')\n  const cleanSearch = `?${search}`.replace(regex, '').replace(/^&/, '?')\n  // replace search params with clean params\n  const cleanURL = url.replace(`?${search}`, cleanSearch)\n  // use browser history API to clean the params\n  return cleanURL\n}\n", "import { isBrowser } from '@analytics/type-utils'\nimport { decodeUri } from './decodeUri'\n\n/**\n * Get search string from given url\n * @param  {string} [url] - optional url string. If no url, window.location.search will be used\n * @return {string} url search string\n */\nfunction getSearchString(url) {\n  if (url) {\n    const p = url.match(/\\?(.*)/)\n    return (p && p[1]) ? p[1].split('#')[0] : ''\n  }\n  return isBrowser && window.location.search.substring(1)\n}\n\n/**\n * Parse url parameters into javascript object\n * @param  {string} [url] - URI to parse. If no url supplied window.location will be used\n * @return {object} parsed url parameters\n */\nexport function paramsParse(url) {\n  return getParamsAsObject(getSearchString(url))\n}\n\n/*\n?first=abc&a[]=123&a[]=false&b[]=str&c[]=3.5&a[]=last\nhttps://random.url.com?Target=Report&Method=getStats&fields%5B%5D=Offer.name&fields%5B%5D=Advertiser.company&fields%5B%5D=Stat.clicks&fields%5B%5D=Stat.conversions&fields%5B%5D=Stat.cpa&fields%5B%5D=Stat.payout&fields%5B%5D=Stat.date&fields%5B%5D=Stat.offer_id&fields%5B%5D=Affiliate.company&groups%5B%5D=Stat.offer_id&groups%5B%5D=Stat.date&filters%5BStat.affiliate_id%5D%5Bconditional%5D=EQUAL_TO&filters%5BStat.affiliate_id%5D%5Bvalues%5D=1831&limit=9999\nhttps://random.url.com?Target=Offer&Method=findAll&filters%5Bhas_goals_enabled%5D%5BTRUE%5D=1&filters%5Bstatus%5D=active&fields%5B%5D=id&fields%5B%5D=name&fields%5B%5D=default_goal_name\nhttp://localhost:3000/?Target=Offer&Method=findAll&filters[has_goals_enabled][TRUE]=1&filters[status]=active&filters[wow]arr[]=yaz&filters[wow]arr[]=naz&fields[]=id&fields[]=name&fields[]=default_goal_name */\n\n\nfunction getParamsAsObject(query) {\n  let params = Object.create(null)\n  let temp\n  const re = /([^&=]+)=?([^&]*)/g\n\n  while (temp = re.exec(query)) {\n    var k = decodeUri(temp[1])\n    var v = decodeUri(temp[2])\n    if (!k) continue\n    if (k.substring(k.length - 2) === '[]') {\n      k = k.substring(0, k.length - 2);\n      var arrVal = params[k] || (params[k] = [])\n      params[k] = Array.isArray(arrVal) ? arrVal : []\n      params[k].push(v)\n    } else {\n      params[k] = (v === '') ? true : v\n    }\n  }\n\n  for (var prop in params) {\n    var arr = prop.split('[')\n    if (arr.length > 1) {\n      assign(params, arr.map((x) => x.replace(/[?[\\]\\\\ ]/g, '')), params[prop])\n      delete params[prop]\n    }\n  }\n  return params\n}\n\nfunction assign(obj, keyPath, value) {\n  var lastKeyIndex = keyPath.length - 1\n  for (var i = 0; i < lastKeyIndex; ++i) {\n    var key = keyPath[i]\n    if (key === '__proto__' || key === 'constructor') {\n      break;\n    }\n    if (!(key in obj)) { \n      obj[key] = {} \n    }\n    obj = obj[key]\n  }\n  obj[keyPath[lastKeyIndex]] = value\n}\n\n\n/*\nhttps://github.com/choojs/nanoquery/blob/791cbdfe49cc380f0b2f93477572128946171b46/browser.js\nvar reg = /([^?=&]+)(=([^&]*))?/g\n\nfunction qs (url) {\n  var obj = {}\n  url.replace(/^.*\\?/, '').replace(reg, function (a0, a1, a2, a3) {\n    var value = decodeURIComponent(a3)\n    var key = decodeURIComponent(a1)\n    if (obj.hasOwnProperty(key)) {\n      if (Array.isArray(obj[key])) obj[key].push(value)\n      else obj[key] = [obj[key], value]\n    } else {\n      obj[key] = value\n    }\n  })\n  return obj\n}\n*/", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * Get host domain of url\n * @param  {String} url - href of page\n * @return {String} hostname of page\n *\n * @example\n *  getDomainHost('https://subdomain.my-site.com/')\n *  > subdomain.my-site.com\n */\nexport function getDomainHost(url) {\n  if (!isBrowser) return null\n  const a = document.createElement('a')\n  a.setAttribute('href', url)\n  return a.hostname\n}\n\n/**\n * Get host domain of url\n * @param  {String} url - href of page\n * @return {String} base hostname of page\n *\n * @example\n *  getDomainBase('https://subdomain.my-site.com/')\n *  > my-site.com\n */\nexport function getDomainBase(url) {\n  const host = getDomainHost(url) || ''\n  return host.split('.').slice(-2).join('.')\n}\n\n/**\n * Remove TLD from domain string\n * @param  {String} baseDomain - host name of site\n * @return {String}\n * @example\n *  trimTld('google.com')\n *  > google\n */\nexport function trimTld(baseDomain) {\n  const arr = baseDomain.split('.')\n  return (arr.length > 1) ? arr.slice(0, -1).join('.') : baseDomain\n}\n\nexport default {\n  trimTld,\n  getDomainBase,\n  getDomainHost\n}\n", "import { isBrowser } from '@analytics/type-utils'\nimport { paramsParse } from './paramsParse'\nimport { isExternalReferrer } from './isExternalReferrer'\nimport { trimTld, getDomainBase } from './url'\n\nconst googleKey = 'google'\n\n/**\n * @typedef {{\n *  campaign: string,\n *  referrer?: string,\n * } & DomainObject & Object.<string, any>} ReferrerObject\n */\n\n/**\n * Checks a given url and parses referrer data\n * @param  {String} [referrer] - (optional) referring URL\n * @param  {String} [currentUrl] - (optional) the current url\n * @return {ReferrerObject}     [description]\n */\nexport function parseReferrer(referrer, currentUrl) {\n  if (!isBrowser) return false\n  // default referral data\n  let refData = {\n    'source': '(direct)',\n    'medium': '(none)',\n    'campaign': '(not set)'\n  }\n  // Add raw ref url if external\n  if (referrer && isExternalReferrer(referrer)) {\n    refData.referrer = referrer\n  }\n\n  const domainInfo = parseDomain(referrer)\n  // Read referrer URI and infer source\n  if (domainInfo && Object.keys(domainInfo).length) {\n    refData = Object.assign({}, refData, domainInfo)\n  }\n\n  // Read URI params and use set utm params\n  const params = paramsParse(currentUrl)\n  const paramKeys = Object.keys(params)\n  if (!paramKeys.length) {\n    return refData\n  }\n\n  // set campaign params off GA matches\n  const gaParams = paramKeys.reduce((acc, key) => {\n    // match utm params & dclid (display) & gclid (cpc)\n    if (key.match(/^utm_/)) {\n      acc[`${key.replace(/^utm_/, '')}`] = params[key]\n    }\n    // https://developers.google.com/analytics/devguides/collection/protocol/v1/parameters\n    // dclid - cpc Cost-Per-Thousand Impressions\n    // gclid - cpc Cost per Click\n    if (key.match(/^(d|g)clid/)) {\n      acc['source'] = googleKey\n      acc['medium'] = (params.gclid) ? 'cpc' : 'cpm'\n      acc[key] = params[key]\n    }\n    return acc\n  }, {})\n\n  return Object.assign({}, refData, gaParams)\n}\n\n/**\n * @typedef {{\n *  source: string,\n *  medium: string,\n *  term?: string\n * }} DomainObject\n */\n\n/**\n * Client side domain parser for determining marketing data.\n * @param  {String} referrer - ref url\n * @return {DomainObject | boolean}\n */\nfunction parseDomain(referrer) {\n  if (!referrer || !isBrowser) return false\n\n  let referringDomain = getDomainBase(referrer)\n  const a = document.createElement('a')\n  a.href = referrer\n\n  // Shim for the billion google search engines\n  if (a.hostname.indexOf(googleKey) > -1) {\n    referringDomain = googleKey\n  }\n\n  // If is search engine\n  if (searchEngines[referringDomain]) {\n    const searchEngine = searchEngines[referringDomain]\n    const queryParam = (typeof searchEngine === 'string') ? searchEngine : searchEngine.p\n    const termRegex = new RegExp(queryParam + '=.*?([^&#]*|$)', 'gi')\n    const term = a.search.match(termRegex)\n\n    return {\n      source: searchEngine.n || trimTld(referringDomain),\n      medium: 'organic',\n      term: (term ? term[0].split('=')[1] : '') || '(not provided)'\n    }\n  }\n\n  // Default\n  const medium = (!isExternalReferrer(referrer)) ? 'internal' : 'referral'\n  return {\n    source: a.hostname,\n    medium: medium\n  }\n}\n\n/**\n * Search engine query string data\n * @type {Object}\n */\nconst Q = 'q'\nconst QUERY = 'query'\nconst searchEngines = {\n  'daum.net': Q,\n  'eniro.se': 'search_word',\n  'naver.com': QUERY,\n  'yahoo.com': 'p',\n  'msn.com': Q,\n  'aol.com': Q,\n  'ask.com': Q,\n  'baidu.com': 'wd',\n  'yandex.com': 'text',\n  'rambler.ru': 'words',\n  'google': Q,\n  'bing.com': {\n    'p': Q,\n    'n': 'live'\n  },\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * @returns {string | undefined}\n */\nexport function getBrowserLocale() {\n  if (!isBrowser) return\n  const { language, languages, userLanguage } = navigator\n  if (userLanguage) return userLanguage // IE only\n  return (languages && languages.length) ? languages[0] : language\n}\n", "\n/**\n * @returns {string | undefined}\n */\nexport function getTimeZone() {\n  try {\n    return Intl.DateTimeFormat().resolvedOptions().timeZone\n  } catch (error) {}\n}\n", "import { isBrowser, isString, isRegex } from '@analytics/type-utils'\n\n/**\n * Check if a script is loaded\n * @param  {String|RegExp} script - Script src as string or regex\n * @return {Boolean} is script loaded\n */\nexport function isScriptLoaded(script) {\n  if (!isBrowser) return true\n  const scripts = document.getElementsByTagName('script')\n  return !!Object.keys(scripts).filter((key) => {\n    const { src } = scripts[key]\n    if (isString(script)) {\n      return src.indexOf(script) !== -1\n    } else if (isRegex(script)) {\n      return src.match(script)\n    }\n    return false\n  }).length\n}\n", "import {decodeUri} from './decodeUri'\n\n/**\n * Get a given query parameter value\n * @param  {string} param - Key of parameter to find\n * @param  {string} url - url to search\n * @return {string} match\n */\nexport function paramsGet(param, url) {\n  return decodeUri((RegExp(`${param}=(.+?)(&|$)`).exec(url) || [, ''])[1])\n}\n", "import { isBrowser } from '@analytics/type-utils'\nimport { paramsClean } from './paramsClean'\n\n/**\n * Removes params from url in browser\n * @param  {string}   param       - param key to remove from current URL\n * @param  {() => void} [callback]  - callback function to run. Only runs in browser\n * @return {PromiseLike<void>}\n */\nexport function paramsRemove(param, callback) {\n  if (!isBrowser) return Promise.resolve()\n\n  return new Promise((resolve, reject) => {\n    if (window.history && window.history.replaceState) {\n      const url = window.location.href\n      const cleanUrl = paramsClean(url, param)\n      if (url !== cleanUrl) {\n        /* replace URL with history API */\n        // eslint-disable-next-line no-restricted-globals\n        history.replaceState({}, '', cleanUrl)\n      }\n    }\n\n    if (callback) callback()\n\n    return resolve()\n  })\n}\n", "/**\n * @template {Function} F;\n * @param {F} func;\n * @param {number} wait;\n * @return {F};\n */\nexport function throttle(func, wait) {\n  var context, args, result\n  var timeout = null\n  var previous = 0\n  var later = function () {\n    previous = new Date()\n    timeout = null\n    result = func.apply(context, args)\n  };\n  return function () {\n    var now = new Date()\n    if (!previous) {\n      previous = now\n    }\n    var remaining = wait - (now - previous)\n    context = this\n    args = arguments\n    if (remaining <= 0) {\n      clearTimeout(timeout)\n      timeout = null\n      previous = now\n      result = func.apply(context, args)\n    } else if (!timeout) {\n      timeout = setTimeout(later, remaining)\n    }\n    return result\n  }\n}\n", "/* ref: http://bit.ly/2daP79j */\n/**\n * @return {string}\n */\nexport function uuid() {\n  var u = '',\n  m = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx',\n  i = 0,\n  rb = Math.random() * 0xffffffff|0;\n\n  while (i++<36) {\n    var c = m [i-1],\n    r = rb&0xf,\n    v = c=='x' ? r : (r&0x3|0x8);\n\n    u += (c=='-' || c=='4') ? c : v.toString(16);\n    rb = i%8==0 ? Math.random() * 0xffffffff|0 : rb>>4\n  }\n  return u\n}\n"], "names": ["decodeUri", "s", "decodeURIComponent", "replace", "e", "process", "<PERSON><PERSON><PERSON><PERSON>", "method", "char<PERSON>t", "slice", "<PERSON><PERSON>", "window", "name", "navigator", "userAgent", "includes", "text", "bind", "lower", "val", "toLowerCase", "upper", "x", "constructor", "ctorName", "Object", "prototype", "toString", "call", "getTypeName", "type", "kind", "typeOf", "isString", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNull", "getType", "message", "n", "isNaN", "isNumber", "stackTraceLimit", "isError", "typeName", "getPrototypeOf", "element", "nodeName", "toUpperCase", "isNodeType", "isEl", "fn", "boundArgs", "isExternalReferrer", "ref", "referrer", "document", "port", "location", "split", "hostname", "paramsClean", "url", "param", "search", "indexOf", "regex", "RegExp", "cleanSearch", "paramsParse", "query", "temp", "params", "create", "re", "exec", "k", "v", "substring", "length", "arrVal", "Array", "isArray", "push", "prop", "arr", "assign", "map", "getParamsAsObject", "p", "match", "getSearchString", "obj", "keyP<PERSON>", "lastKeyIndex", "i", "key", "getDomainHost", "a", "createElement", "setAttribute", "getDomainBase", "join", "trimTld", "baseDomain", "errorType", "TypeError", "SyntaxError", "bind<PERSON><PERSON><PERSON>", "isElement", "googleKey", "Q", "searchEngines", "google", "languages", "userLanguage", "language", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "error", "script", "scripts", "getElementsByTagName", "keys", "filter", "src", "callback", "Promise", "resolve", "reject", "history", "replaceState", "href", "cleanUrl", "currentUrl", "refData", "source", "medium", "campaign", "domainInfo", "referringDomain", "searchEngine", "termRegex", "term", "parseDomain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaParams", "reduce", "acc", "gclid", "func", "wait", "context", "args", "result", "timeout", "previous", "later", "Date", "apply", "now", "remaining", "this", "arguments", "clearTimeout", "setTimeout", "u", "rb", "Math", "random", "c", "r"], "mappings": "waASgBA,EAAUC,GACxB,IACE,OAAOC,mBAAmBD,EAAEE,QAAQ,MAAO,MAC3C,MAAOC,GACP,aCNqB,6BAwCsBC,YAelCC,EAvDY,6BA2EzB,WAAcC,EAAQN,GACpB,SAASO,OAAO,GAAGD,KAAYN,EAAEQ,MAAM,GA5EhB,0BA+DiCC,KAMlCJ,GAA6B,WAAhBK,OAAOC,MArEnB,oCAqEiDC,IAAkCA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,gBAU5MC,EAAKC,KAAK,KAAM,eACxBC,EAAQF,EAAKC,KAAK,KAAM,0BAiBNE,EAAKC,sBAC3B,MAlB4B,SAOFD,GAC1B,SAAWA,GAAaE,EAlFN,QAJE,4BA+VGC,GACvB,SAAkBA,EAAEC,aAAeD,EAAEC,YAAYX,KAAO,KAzQvBY,CAASL,GAAOM,OAAOC,UAAUC,SAASC,KAAKT,GAAKV,MAAM,GAAI,GASlFoB,CAAYV,GAEzB,SAAuBD,EAAMY,GAAQA,aAUvBC,EAAMZ,GACpB,kBAAsBY,QAQEC,EAAOf,KAAK,KAzHd,YAgIXgB,EAAWD,EAAOf,KAAK,KA/Hd,UAMA,WA8JCK,GACrB,cAAOA,aA4QUY,EAASC,GAC1B,GAAqB,oBAAYC,EAAOD,GAAQ,SAEhD,GAAIA,eAA0B,SAC9B,MAAiBE,EAAQ,MAAY,KAErC,YAnCsBf,GACtB,2BAA8BW,EAASX,EAAEgB,UAAYhB,EAAEC,sBAvNhCgB,GACvB,MA1LoB,WA0LbF,EAAQE,KAAkBC,MAAMD,GAsN+BE,CAASnB,EAAEC,YAAYmB,iBAkCzFC,CAAQR,GACV,KAAOA,GAAO,CACZ,GAAIE,EAAQF,KAAWS,EACrB,SAEAT,EAAQV,OAAOoB,eAAeV,uBA8JZW,EAAShB,GACjC,MAAagB,sBAA8BA,0BAC3C,UAAchB,WAUWgB,EAAShB,GAClC,gBADkCA,IAAAA,EAAO,IAClCgB,GAAWA,EAAQC,WAAajB,EAAKkB,cAVrCC,CAAWH,EAAShB,GADAoB,aAcXC,oCAChB,kBACE,sDAAsBC,cC5mBVC,EAAmBC,GACjC,IAAKhD,EAAW,SAChB,IAAMiD,EAAWD,GAAOE,SAASD,SACjC,GAAIA,EAAU,CACZ,IAAME,EAAO9C,OAAO6C,SAASE,SAASD,KAClCH,EAAMC,EAASI,MAAM,KAAK,GAI9B,OAHIF,IACFH,EAAMA,EAAInD,YAAYsD,EAAQ,KAEzBH,IAAQ3C,OAAO+C,SAASE,SAEjC,kBCVcC,EAAYC,EAAKC,GAC/B,IAAMC,GAAUF,EAAIH,MAAM,MAAQ,KAAO,GACzC,IAAKK,IAAqC,IAA3BA,EAAOC,QAAQF,GAC5B,OAAOD,EAGT,IAAMI,EAAQ,IAAIC,mBAAmBJ,gCAAoC,KACnEK,OAAkBJ,GAAS7D,QAAQ+D,EAAO,IAAI/D,QAAQ,KAAM,KAIlE,OAFiB2D,EAAI3D,YAAY6D,EAAUI,YCK7BC,EAAYP,GAC1B,OAUF,SAA2BQ,GAKzB,IAJA,IACIC,EADAC,EAAS/C,OAAOgD,OAAO,MAErBC,EAAK,qBAEJH,EAAOG,EAAGC,KAAKL,IAAQ,CAC5B,IAAIM,EAAI5E,EAAUuE,EAAK,IACnBM,EAAI7E,EAAUuE,EAAK,IACvB,GAAKK,EACL,GAAkC,OAA9BA,EAAEE,UAAUF,EAAEG,OAAS,GAAa,CAEtC,IAAIC,EAASR,EADbI,EAAIA,EAAEE,UAAU,EAAGF,EAAEG,OAAS,MACHP,EAAOI,GAAK,IACvCJ,EAAOI,GAAKK,MAAMC,QAAQF,GAAUA,EAAS,GAC7CR,EAAOI,GAAGO,KAAKN,QAEfL,EAAOI,GAAY,KAANC,GAAmBA,EAIpC,IAAK,IAAIO,KAAQZ,EAAQ,CACvB,IAAIa,EAAMD,EAAKzB,MAAM,KACjB0B,EAAIN,OAAS,IACfO,EAAOd,EAAQa,EAAIE,IAAI,SAACjE,UAAMA,EAAEnB,QAAQ,aAAc,MAAMqE,EAAOY,WAC5DZ,EAAOY,IAGlB,OAAOZ,EApCAgB,CAdT,SAAyB1B,GACvB,GAAIA,EAAK,CACP,IAAM2B,EAAI3B,EAAI4B,MAAM,UACpB,OAAQD,GAAKA,EAAE,GAAMA,EAAE,GAAG9B,MAAM,KAAK,GAAK,GAE5C,OAAOrD,GAAaK,OAAO+C,SAASM,OAAOc,UAAU,GAS5Ba,CAAgB7B,IAuC3C,SAASwB,EAAOM,EAAKC,EAAS1D,GAE5B,IADA,IAAI2D,EAAeD,EAAQd,OAAS,EAC3BgB,EAAI,EAAGA,EAAID,IAAgBC,EAAG,CACrC,IAAIC,EAAMH,EAAQE,GAClB,GAAY,cAARC,GAA+B,gBAARA,EACzB,MAEIA,KAAOJ,IACXA,EAAII,GAAO,IAEbJ,EAAMA,EAAII,GAEZJ,EAAIC,EAAQC,IAAiB3D,WC9Df8D,EAAcnC,GAC5B,IAAKxD,EAAW,YAChB,IAAM4F,EAAI1C,SAAS2C,cAAc,KAEjC,OADAD,EAAEE,aAAa,OAAQtC,GAChBoC,EAAEtC,kBAYKyC,EAAcvC,GAE5B,OADamC,EAAcnC,IAAQ,IACvBH,MAAM,KAAKlD,OAAO,GAAG6F,KAAK,cAWxBC,EAAQC,GACtB,IAAMnB,EAAMmB,EAAW7C,MAAM,KAC7B,OAAQ0B,EAAIN,OAAS,EAAKM,EAAI5E,MAAM,GAAI,GAAG6F,KAAK,KAAOE,EJkG9BxE,EAAOf,KAAK,KArId,aAoJAe,EAAOf,KAAK,KAnJd,WAyJCe,EAAOf,KAAK,KArJd,UA4bKwF,EAAUxF,KAAK,KAAMyF,WAEnBD,EAAUxF,KAAK,KAAM0F,aAiL5BC,EAASC,EA9lBX,QAqmBID,EAASC,EAnmBX,UA0mBCD,EAASC,EA3mBX,SAknBGD,EAASC,EAhnBX,UIatB,MAAe,CACbN,QAAAA,EACAF,cAAAA,EACAJ,cAAAA,GC3CIa,EAAY,SAgHZC,EAAI,IAEJC,EAAgB,CACpB,WAAYD,EACZ,WAAY,cACZ,YAJY,QAKZ,YAAa,IACb,UAAWA,EACX,UAAWA,EACX,UAAWA,EACX,YAAa,KACb,aAAc,OACd,aAAc,QACdE,OAAUF,EACV,WAAY,CACVtB,EAAKsB,EACLxE,EAAK,iEC/HP,GAAKjC,EAAL,CACA,MAA8CO,UAA5BqG,IAAAA,UAClB,SAD6BC,eAErBD,GAAaA,EAAUnC,OAAUmC,EAAU,KAF3CE,qCCFR,IACE,OAAOC,KAAKC,iBAAiBC,kBAAkBC,SAC/C,MAAOC,uDCAoBC,GAC7B,IAAKpH,EAAW,SAChB,IAAMqH,EAAUnE,SAASoE,qBAAqB,UAC9C,QAASnG,OAAOoG,KAAKF,GAASG,OAAO,SAAC9B,GACpC,IAAQ+B,EAAQJ,EAAQ3B,GAAhB+B,IACR,OAAI9F,EAASyF,IACqB,IAAzBK,EAAI9D,QAAQyD,GACFA,qBACVK,EAAIrC,MAAMgC,KAGlB3C,6CCVqBhB,EAAOD,GAC/B,OAAO9D,GAAWmE,OAAUJ,iBAAoBY,KAAKb,IAAQ,EAAG,KAAK,6CCA1CC,EAAOiE,GAClC,OAAK1H,MAEM2H,QAAQ,SAACC,EAASC,GAC3B,GAAIxH,OAAOyH,SAAWzH,OAAOyH,QAAQC,aAAc,CACjD,IAAMvE,EAAMnD,OAAO+C,SAAS4E,KACtBC,EAAW1E,EAAYC,EAAKC,GAC9BD,IAAQyE,GAGVH,QAAQC,aAAa,GAAI,GAAIE,GAMjC,OAFIP,GAAUA,IAEPE,MAfcD,QAAQC,oCLUH3E,EAAUiF,GACtC,IAAKlI,EAAW,SAEhB,IAAImI,EAAU,CACZC,OAAU,WACVC,OAAU,SACVC,SAAY,aAGVrF,GAAYF,EAAmBE,KACjCkF,EAAQlF,SAAWA,GAGrB,IAAMsF,EA8CR,SAAqBtF,GACnB,IAAKA,IAAajD,EAAW,SAE7B,IAAIwI,EAAkBzC,EAAc9C,GAC9B2C,EAAI1C,SAAS2C,cAAc,KASjC,GARAD,EAAEoC,KAAO/E,EAGL2C,EAAEtC,SAASK,QAAQ6C,IAAc,IACnCgC,EAAkBhC,GAIhBE,EAAc8B,GAAkB,CAClC,IAAMC,EAAe/B,EAAc8B,GAE7BE,EAAY,IAAI7E,QADsB,iBAAjB4E,EAA6BA,EAAeA,EAAatD,GAC1C,iBAAkB,MACtDwD,EAAO/C,EAAElC,OAAO0B,MAAMsD,GAE5B,MAAO,CACLN,OAAQK,EAAaxG,GAAKgE,EAAQuC,GAClCH,OAAQ,UACRM,MAAOA,EAAOA,EAAK,GAAGtF,MAAM,KAAK,GAAK,KAAO,kBAKjD,IAAMgF,EAAWtF,EAAmBE,GAA0B,WAAb,WACjD,MAAO,CACLmF,OAAQxC,EAAEtC,SACV+E,OAAQA,GA5ESO,CAAY3F,GAE3BsF,GAAcpH,OAAOoG,KAAKgB,GAAY9D,SACxC0D,EAAUhH,OAAO6D,OAAO,GAAImD,EAASI,IAIvC,IAAMrE,EAASH,EAAYmE,GACrBW,EAAY1H,OAAOoG,KAAKrD,GAC9B,IAAK2E,EAAUpE,OACb,OAAO0D,EAIT,IAAMW,EAAWD,EAAUE,OAAO,SAACC,EAAKtD,GAatC,OAXIA,EAAIN,MAAM,WACZ4D,KAAOtD,EAAI7F,QAAQ,QAAS,KAASqE,EAAOwB,IAK1CA,EAAIN,MAAM,gBACZ4D,EAAG,OAAaxC,EAChBwC,EAAG,OAAc9E,EAAO+E,MAAS,MAAQ,MACzCD,EAAItD,GAAOxB,EAAOwB,IAEbsD,GACN,IAEH,OAAO7H,OAAO6D,OAAO,GAAImD,EAASW,wBMzDXI,EAAMC,GAC7B,IAAIC,EAASC,EAAMC,EACfC,EAAU,KACVC,EAAW,EACXC,EAAQ,WACVD,EAAW,IAAIE,KACfH,EAAU,KACVD,EAASJ,EAAKS,MAAMP,EAASC,IAE/B,kBACE,IAAIO,EAAM,IAAIF,KACTF,IACHA,EAAWI,GAEb,IAAIC,EAAYV,GAAQS,EAAMJ,GAW9B,OAVAJ,EAAUU,KACVT,EAAOU,UACHF,GAAa,GACfG,aAAaT,GACbA,EAAU,KACVC,EAAWI,EACXN,EAASJ,EAAKS,MAAMP,EAASC,IACnBE,IACVA,EAAUU,WAAWR,EAAOI,IAEvBP,8BCrBT,IALA,IAAIY,EAAI,GAERzE,EAAI,EACJ0E,EAAqB,WAAhBC,KAAKC,SAAsB,EAEzB5E,IAAI,IAAI,CACb,IAAI6E,EALF,uCAKS7E,EAAE,GACb8E,EAAO,GAAHJ,EAGJD,GAAS,KAAHI,GAAa,KAAHA,EAAUA,GAFnB,KAAHA,EAASC,EAAO,EAAFA,EAAM,GAEQlJ,SAAS,IACzC8I,EAAK1E,EAAE,GAAG,EAAoB,WAAhB2E,KAAKC,SAAsB,EAAIF,GAAI,EAEnD,OAAOD"}