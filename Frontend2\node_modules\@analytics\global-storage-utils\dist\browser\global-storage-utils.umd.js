!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n||self).utilGlobalStorage={})}(this,function(n){var e="undefined",t="object";"undefined"==typeof process||process;var o="undefined"!=typeof document;function r(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,o&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var i=r.bind(null,"toUpperCase"),u=r.bind(null,"toLowerCase");function l(n,e){void 0===e&&(e=!0);var t=function(n){return s(n)?i("null"):"object"==typeof n?function(n){return c(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?u(t):t}function f(n,e){return typeof e===n}var c=f.bind(null,"function"),a=f.bind(null,"string");function s(n){return null===n}function d(n,e){if("object"!=typeof e||s(e))return!1;if(e instanceof n)return!0;var t=l(new n(""));if(function(n){return n instanceof Error||a(n.message)&&n.constructor&&function(n){return"number"===l(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(l(e)===t)return!0;e=Object.getPrototypeOf(e)}return!1}function p(n,e){var t=n instanceof Element||n instanceof HTMLDocument;return t&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):t}function b(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}f.bind(null,"undefined"),f.bind(null,"boolean"),f.bind(null,"symbol"),d.bind(null,TypeError),d.bind(null,SyntaxError),b(p,"form"),b(p,"button"),b(p,"input"),b(p,"select");var v="__global__",g=typeof self===t&&self.self===self&&self||typeof global===t&&global.global===global&&global||void 0;g[v]||(g[v]={});var y={};function m(n){if(typeof y[n]!==e)return y[n];try{var t=window[n];t.setItem(e,e),t.removeItem(e)}catch(e){return y[n]=!1}return y[n]=!0}n.GLOBAL="global",n.KEY=v,n.get=function(n){return g[v][n]},n.globalContext=g,n.hasSupport=m,n.remove=function(n){delete g[v][n]},n.set=function(n,e){return g[v][n]=e},n.wrap=function(n,e,t){var o;try{if(m(n)){var r=window[n];o=r[e].bind(r)}}catch(n){}return o||t}});
//# sourceMappingURL=global-storage-utils.umd.js.map
