var utilCookies=function(e,o){var t="cookie",n=u(),i=a,r=a;function c(e){return n?a(e,"",-1):o.remove(e)}function u(){if(void 0!==n)return n;var e=t+t;try{a(e,e),n=-1!==document.cookie.indexOf(e),c(e)}catch(e){n=!1}return n}function a(e,t,i,r,c,u){if("undefined"!=typeof window){var a=arguments.length>1;return!1===n&&(a?o.set(e,t):o.get(e)),a?document.cookie=e+"="+encodeURIComponent(t)+(i?"; expires="+new Date(+new Date+1e3*i).toUTCString()+(r?"; path="+r:"")+(c?"; domain="+c:"")+(u?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+e+"=")[1]||"").split(";")[0])}}return e.COOKIE=t,e.getCookie=i,e.hasCookies=u,e.removeCookie=c,e.setCookie=r,e}({},globalStorageUtils);
//# sourceMappingURL=cookie-utils.js.map
