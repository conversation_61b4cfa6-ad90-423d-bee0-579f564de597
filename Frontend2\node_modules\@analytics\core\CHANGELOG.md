# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.12.17](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.16...@analytics/core@0.12.17) (2024-12-12)

**Note:** Version bump only for package @analytics/core





## [0.12.16](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.15...@analytics/core@0.12.16) (2024-12-11)

**Note:** Version bump only for package @analytics/core





## [0.12.15](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.14...@analytics/core@0.12.15) (2024-07-29)


### Bug Fixes

* fix typedef ([d58f70d](https://github.com/DavidWells/analytics/commit/d58f70d60422b4c4e862dd732f3df50d8e802e25))





## [0.12.14](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.10...@analytics/core@0.12.14) (2024-05-30)

**Note:** Version bump only for package @analytics/core





## [0.12.10](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.9...@analytics/core@0.12.10) (2024-05-30)

**Note:** Version bump only for package @analytics/core





## [0.12.9](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.8...@analytics/core@0.12.9) (2024-02-12)

**Note:** Version bump only for package @analytics/core





## [0.12.8](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.7...@analytics/core@0.12.8) (2024-02-12)


### Bug Fixes

* add abort to queue drain and make queue methods abortable. Fixes [#418](https://github.com/DavidWells/analytics/issues/418) ([58b1849](https://github.com/DavidWells/analytics/commit/58b184993106ae2234a2508193cdb7ef0e8c0893))





## [0.12.7](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.6...@analytics/core@0.12.7) (2023-06-16)

**Note:** Version bump only for package @analytics/core





## [0.12.6](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.5...@analytics/core@0.12.6) (2023-06-16)

**Note:** Version bump only for package @analytics/core





## [0.12.5](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.4...@analytics/core@0.12.5) (2023-05-27)

**Note:** Version bump only for package @analytics/core





## [0.12.4](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.3...@analytics/core@0.12.4) (2023-05-27)

**Note:** Version bump only for package @analytics/core





## [0.12.3](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.2...@analytics/core@0.12.3) (2023-05-27)

**Note:** Version bump only for package @analytics/core





## [0.12.2](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.1...@analytics/core@0.12.2) (2022-07-21)

**Note:** Version bump only for package @analytics/core





## [0.12.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.12.0...@analytics/core@0.12.1) (2022-07-21)

**Note:** Version bump only for package @analytics/core





# [0.12.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.11.1...@analytics/core@0.12.0) (2022-07-21)


### Features

* automatically call ready callback if instance ready ([3f14922](https://github.com/DavidWells/analytics/commit/3f1492263d5ec6f4c861b0ae5d4fa53d82d4b032))





## [0.11.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.11.0...@analytics/core@0.11.1) (2022-03-18)

**Note:** Version bump only for package @analytics/core





# [0.11.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.23...@analytics/core@0.11.0) (2022-02-07)


### Features

* shrink bundle ([61bb45f](https://github.com/DavidWells/analytics/commit/61bb45fa336e044c8a1d40b8f9fe69d5d865de7e))





## [0.10.23](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.22...@analytics/core@0.10.23) (2022-02-06)

**Note:** Version bump only for package @analytics/core





## [0.10.22](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.21...@analytics/core@0.10.22) (2022-02-05)

**Note:** Version bump only for package @analytics/core





## [0.10.21](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.20...@analytics/core@0.10.21) (2022-01-03)

**Note:** Version bump only for package @analytics/core





## [0.10.20](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.19...@analytics/core@0.10.20) (2022-01-02)

**Note:** Version bump only for package @analytics/core





## [0.10.19](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.18...@analytics/core@0.10.19) (2021-12-12)

**Note:** Version bump only for package @analytics/core





## [0.10.18](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.17...@analytics/core@0.10.18) (2021-10-29)

**Note:** Version bump only for package @analytics/core





## [0.10.17](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.16...@analytics/core@0.10.17) (2021-10-24)

**Note:** Version bump only for package @analytics/core





## [0.10.16](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.15...@analytics/core@0.10.16) (2021-10-17)

**Note:** Version bump only for package @analytics/core





## [0.10.15](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.14...@analytics/core@0.10.15) (2021-09-29)

**Note:** Version bump only for package @analytics/core





## [0.10.14](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.13...@analytics/core@0.10.14) (2021-08-27)

**Note:** Version bump only for package @analytics/core





## [0.10.13](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.12...@analytics/core@0.10.13) (2021-08-05)

**Note:** Version bump only for package @analytics/core





## [0.10.12](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.11...@analytics/core@0.10.12) (2021-08-05)

**Note:** Version bump only for package @analytics/core





## [0.10.11](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.10...@analytics/core@0.10.11) (2021-07-31)

**Note:** Version bump only for package @analytics/core





## [0.10.10](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.9...@analytics/core@0.10.10) (2021-07-28)

**Note:** Version bump only for package @analytics/core





## [0.10.9](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.8...@analytics/core@0.10.9) (2021-07-27)

**Note:** Version bump only for package @analytics/core





## [0.10.8](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.7...@analytics/core@0.10.8) (2021-07-26)

**Note:** Version bump only for package @analytics/core





## [0.10.7](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.6...@analytics/core@0.10.7) (2021-07-20)

**Note:** Version bump only for package @analytics/core





## [0.10.6](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.5...@analytics/core@0.10.6) (2021-07-20)

**Note:** Version bump only for package @analytics/core





## [0.10.5](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.4...@analytics/core@0.10.5) (2021-03-20)

**Note:** Version bump only for package @analytics/core





## [0.10.4](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.3...@analytics/core@0.10.4) (2021-03-11)

**Note:** Version bump only for package @analytics/core





## [0.10.3](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.2...@analytics/core@0.10.3) (2021-02-15)

**Note:** Version bump only for package @analytics/core





## [0.10.2](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.1...@analytics/core@0.10.2) (2021-02-01)

**Note:** Version bump only for package @analytics/core





## [0.10.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.10.0...@analytics/core@0.10.1) (2021-02-01)

**Note:** Version bump only for package @analytics/core





# [0.10.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.9.3...@analytics/core@0.10.0) (2021-01-26)


### Features

* add AWS pinpoint ([909c950](https://github.com/DavidWells/analytics/commit/909c950))





## [0.9.3](https://github.com/DavidWells/analytics/compare/@analytics/core@0.9.2...@analytics/core@0.9.3) (2021-01-20)

**Note:** Version bump only for package @analytics/core





## [0.9.2](https://github.com/DavidWells/analytics/compare/@analytics/core@0.9.1...@analytics/core@0.9.2) (2021-01-14)

**Note:** Version bump only for package @analytics/core





## [0.9.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.9.0...@analytics/core@0.9.1) (2021-01-04)

**Note:** Version bump only for package @analytics/core





# [0.9.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.8.0...@analytics/core@0.9.0) (2020-12-23)


### Features

* allow all plugins to have ‘enable’ option ([9d03087](https://github.com/DavidWells/analytics/commit/9d03087))





# [0.8.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.7.2...@analytics/core@0.8.0) (2020-12-23)


### Features

* Add enable feature to initial plugins loading ([a72276c](https://github.com/DavidWells/analytics/commit/a72276c))





## [0.7.2](https://github.com/DavidWells/analytics/compare/@analytics/core@0.7.1...@analytics/core@0.7.2) (2020-12-09)

**Note:** Version bump only for package @analytics/core





## [0.7.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.7.0...@analytics/core@0.7.1) (2020-12-02)

**Note:** Version bump only for package @analytics/core





# [0.7.0](https://github.com/DavidWells/analytics/compare/@analytics/core@0.6.2...@analytics/core@0.7.0) (2020-11-20)


### Features

* **plugins:** add mixpanel ([d084055](https://github.com/DavidWells/analytics/commit/d084055))





## [0.6.2](https://github.com/DavidWells/analytics/compare/@analytics/core@0.6.1...@analytics/core@0.6.2) (2020-07-17)

**Note:** Version bump only for package @analytics/core





## [0.6.1](https://github.com/DavidWells/analytics/compare/@analytics/core@0.6.0...@analytics/core@0.6.1) (2020-07-16)

**Note:** Version bump only for package @analytics/core





# 0.6.0 (2020-07-14)


### Bug Fixes

* **core:** disable ‘os’ for node until build fixed ([4610cd0](https://github.com/DavidWells/analytics/commit/4610cd0))
* **core:** expore devtools when debug set to true ([8dd38b7](https://github.com/DavidWells/analytics/commit/8dd38b7))
* **core:** fix campaign event ([8df0eaa](https://github.com/DavidWells/analytics/commit/8df0eaa))
* **core:** fix payload for .once listeners ([4cf07b7](https://github.com/DavidWells/analytics/commit/4cf07b7))
* **core:** remove async keyword ([6446e72](https://github.com/DavidWells/analytics/commit/6446e72))
* **core:** save last event & history ([275e89a](https://github.com/DavidWells/analytics/commit/275e89a))
* **core:** set anonId when setItemEnd occurs for ANON_ID ([5401cda](https://github.com/DavidWells/analytics/commit/5401cda))
* **core plugins:** run .page .track .identify callbacks when no plugins found ([09dfd78](https://github.com/DavidWells/analytics/commit/09dfd78))
*  namespaced plugin config values should reflect current plugin ([a3cc443](https://github.com/DavidWells/analytics/commit/a3cc443)), closes [#25](https://github.com/DavidWells/analytics/issues/25)
* **debug:** support debug enhancer when no dev tools found ([c2a93c7](https://github.com/DavidWells/analytics/commit/c2a93c7))
* **utils:** fix build for node & testing + add iife build ([20a5021](https://github.com/DavidWells/analytics/commit/20a5021))


### Features

* **cli:** add analytics CLI for automate plugin docs & more to come ([297476c](https://github.com/DavidWells/analytics/commit/297476c))
* **core:** add analytics.reset for clean slate ([f30b83d](https://github.com/DavidWells/analytics/commit/f30b83d))
* **core:** add browser tab visible and window mouseout events ([ae65b37](https://github.com/DavidWells/analytics/commit/ae65b37))
* **core:** add debug to plugin action mods ([3c47088](https://github.com/DavidWells/analytics/commit/3c47088))
* **core:** add debug utilities ([daaef87](https://github.com/DavidWells/analytics/commit/daaef87))
* **core:** add deeper event log support for debugging ([5a8e810](https://github.com/DavidWells/analytics/commit/5a8e810))
* **core:** add enable / disable integration ([acc5390](https://github.com/DavidWells/analytics/commit/acc5390))
* add @analytics/core ([6b00148](https://github.com/DavidWells/analytics/commit/6b00148))
* add conditional enable/disable on track/page/identify ([d33587b](https://github.com/DavidWells/analytics/commit/d33587b))
* **core:** Expose setItem, removeItem, EVENTS, & CONSTANTS in api ([808b9ae](https://github.com/DavidWells/analytics/commit/808b9ae))
* Add methods key for plugins to provide custom methods ([9e0debe](https://github.com/DavidWells/analytics/commit/9e0debe))
* **core:** add enable/disable integration by array of providers ([ddf596f](https://github.com/DavidWells/analytics/commit/ddf596f))
* **core:** add getPersistedUserData for initial state ([6620881](https://github.com/DavidWells/analytics/commit/6620881))
* **core:** add network events ([b2e06b6](https://github.com/DavidWells/analytics/commit/b2e06b6))
* **core:** add offline handler ([c4e2b11](https://github.com/DavidWells/analytics/commit/c4e2b11))
* **core:** add querystring API for triggering events + identify calls via utm params ([44d05d5](https://github.com/DavidWells/analytics/commit/44d05d5))
* **core:** add queuing & heartbeat mechanism for handling load loading analytic scripts ([7058b39](https://github.com/DavidWells/analytics/commit/7058b39))
* **core:** add storage constants ([04036a1](https://github.com/DavidWells/analytics/commit/04036a1))
* **core:** add storage middleware to allow for third party plugins to intercept / audit persistance data ([96fb50f](https://github.com/DavidWells/analytics/commit/96fb50f))
* **core:** add timestamp for track, identify, & page calls ([33d8338](https://github.com/DavidWells/analytics/commit/33d8338))
* **core:** add timeZone & locale to context ([d627a52](https://github.com/DavidWells/analytics/commit/d627a52))
* **core:** listen to window events for future session feature ([4acebc5](https://github.com/DavidWells/analytics/commit/4acebc5))
* **core:** pass state getter to integrations as last arg ([29566d1](https://github.com/DavidWells/analytics/commit/29566d1))
* **core:** queue track, page, & identify calls if browser offline ([41f7f78](https://github.com/DavidWells/analytics/commit/41f7f78))
* **fullstory:** add fullstory plugin ([255ce77](https://github.com/DavidWells/analytics/commit/255ce77))
* **plugin-system:** add new core plugin engine ([e122572](https://github.com/DavidWells/analytics/commit/e122572))
* **plugins:** refactor plugins to allow for hooking into plugin specific events/methods ([c1c5379](https://github.com/DavidWells/analytics/commit/c1c5379))
* add page.last & track.last to state ([c8ce5cc](https://github.com/DavidWells/analytics/commit/c8ce5cc))
* add perfume.js performance metrics plugin ([2a4b3f2](https://github.com/DavidWells/analytics/commit/2a4b3f2))
* make .track, .page, .identify, .reset return promise for async usage ([4069c03](https://github.com/DavidWells/analytics/commit/4069c03))





## [0.5.2](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.5.2) (2020-07-05)

**Note:** Version bump only for package analytics





# [0.5.0](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.5.0) (2020-06-03)


### Features

* add page.last & track.last to state ([c8ce5cc](https://github.com/DavidWells/analytics/commit/c8ce5cc))
* add perfume.js performance metrics plugin ([2a4b3f2](https://github.com/DavidWells/analytics/commit/2a4b3f2))





# [0.4.0](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.4.0) (2020-05-18)


### Features

* add conditional enable/disable on track/page/identify ([d33587b](https://github.com/DavidWells/analytics/commit/d33587b))





## [0.3.5](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.5) (2020-04-21)

**Note:** Version bump only for package analytics





## [0.3.4](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.4) (2020-04-16)

**Note:** Version bump only for package analytics





## [0.3.3](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.3) (2020-04-16)

**Note:** Version bump only for package analytics





## [0.3.2](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.2) (2020-04-10)

**Note:** Version bump only for package analytics





## [0.3.1](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.1) (2020-03-02)


### Bug Fixes

*  namespaced plugin config values should reflect current plugin ([a3cc443](https://github.com/DavidWells/analytics/commit/a3cc443)), closes [#25](https://github.com/DavidWells/analytics/issues/25)





# [0.3.0](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.3.0) (2019-10-31)


### Features

* make .track, .page, .identify, .reset return promise for async usage ([4069c03](https://github.com/DavidWells/analytics/commit/4069c03))





## [0.2.6](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.6) (2019-10-21)

**Note:** Version bump only for package analytics





## [0.2.5](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.5) (2019-10-14)

**Note:** Version bump only for package analytics





## [0.2.4](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.4) (2019-10-07)

**Note:** Version bump only for package analytics





## [0.2.3](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.3) (2019-10-03)

**Note:** Version bump only for package analytics





## [0.2.2](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.2) (2019-09-30)

**Note:** Version bump only for package analytics





## [0.2.1](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.1) (2019-09-23)

**Note:** Version bump only for package analytics





# [0.2.0](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.2.0) (2019-09-10)


### Features

* **fullstory:** add fullstory plugin ([255ce77](https://github.com/DavidWells/analytics/commit/255ce77))





## [0.1.20](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.20) (2019-08-26)

**Note:** Version bump only for package analytics





## [0.1.19](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.19) (2019-08-14)

**Note:** Version bump only for package analytics





## [0.1.18](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.18) (2019-08-14)

**Note:** Version bump only for package analytics





## [0.1.17](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.17) (2019-08-14)

**Note:** Version bump only for package analytics





## [0.1.16](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.16) (2019-07-22)

**Note:** Version bump only for package analytics





## [0.1.15](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.15) (2019-07-19)

**Note:** Version bump only for package analytics





## [0.1.14](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.14) (2019-07-13)

**Note:** Version bump only for package analytics





## [0.1.13](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.13) (2019-07-13)

**Note:** Version bump only for package analytics





## [0.1.12](https://github.com/DavidWells/analytics/compare/<EMAIL>@0.1.12) (2019-07-13)


### Bug Fixes

* **core:** disable ‘os’ for node until build fixed ([4610cd0](https://github.com/DavidWells/analytics/commit/4610cd0))
* **core:** expore devtools when debug set to true ([8dd38b7](https://github.com/DavidWells/analytics/commit/8dd38b7))
* **core:** fix campaign event ([8df0eaa](https://github.com/DavidWells/analytics/commit/8df0eaa))
* **core:** fix payload for .once listeners ([4cf07b7](https://github.com/DavidWells/analytics/commit/4cf07b7))
* **core:** remove async keyword ([6446e72](https://github.com/DavidWells/analytics/commit/6446e72))
* **core:** save last event & history ([275e89a](https://github.com/DavidWells/analytics/commit/275e89a))
* **core:** set anonId when setItemEnd occurs for ANON_ID ([5401cda](https://github.com/DavidWells/analytics/commit/5401cda))
* **core plugins:** run .page .track .identify callbacks when no plugins found ([09dfd78](https://github.com/DavidWells/analytics/commit/09dfd78))
* **debug:** support debug enhancer when no dev tools found ([c2a93c7](https://github.com/DavidWells/analytics/commit/c2a93c7))
* **utils:** fix build for node & testing + add iife build ([20a5021](https://github.com/DavidWells/analytics/commit/20a5021))


### Features

* **cli:** add analytics CLI for automate plugin docs & more to come ([297476c](https://github.com/DavidWells/analytics/commit/297476c))
* **core:** add analytics.reset for clean slate ([f30b83d](https://github.com/DavidWells/analytics/commit/f30b83d))
* **core:** add browser tab visible and window mouseout events ([ae65b37](https://github.com/DavidWells/analytics/commit/ae65b37))
* **core:** add debug to plugin action mods ([3c47088](https://github.com/DavidWells/analytics/commit/3c47088))
* **core:** add debug utilities ([daaef87](https://github.com/DavidWells/analytics/commit/daaef87))
* **core:** add deeper event log support for debugging ([5a8e810](https://github.com/DavidWells/analytics/commit/5a8e810))
* **core:** add enable / disable integration ([acc5390](https://github.com/DavidWells/analytics/commit/acc5390))
* **core:** add enable/disable integration by array of providers ([ddf596f](https://github.com/DavidWells/analytics/commit/ddf596f))
* **core:** add getPersistedUserData for initial state ([6620881](https://github.com/DavidWells/analytics/commit/6620881))
* **core:** add network events ([b2e06b6](https://github.com/DavidWells/analytics/commit/b2e06b6))
* **core:** add offline handler ([c4e2b11](https://github.com/DavidWells/analytics/commit/c4e2b11))
* **core:** add querystring API for triggering events + identify calls via utm params ([44d05d5](https://github.com/DavidWells/analytics/commit/44d05d5))
* **core:** add queuing & heartbeat mechanism for handling load loading analytic scripts ([7058b39](https://github.com/DavidWells/analytics/commit/7058b39))
* **core:** add storage constants ([04036a1](https://github.com/DavidWells/analytics/commit/04036a1))
* **core:** add storage middleware to allow for third party plugins to intercept / audit persistance data ([96fb50f](https://github.com/DavidWells/analytics/commit/96fb50f))
* **core:** add timestamp for track, identify, & page calls ([33d8338](https://github.com/DavidWells/analytics/commit/33d8338))
* **core:** add timeZone & locale to context ([d627a52](https://github.com/DavidWells/analytics/commit/d627a52))
* **core:** Expose setItem, removeItem, EVENTS, & CONSTANTS in api ([808b9ae](https://github.com/DavidWells/analytics/commit/808b9ae))
* **core:** listen to window events for future session feature ([4acebc5](https://github.com/DavidWells/analytics/commit/4acebc5))
* **core:** pass state getter to integrations as last arg ([29566d1](https://github.com/DavidWells/analytics/commit/29566d1))
* **core:** queue track, page, & identify calls if browser offline ([41f7f78](https://github.com/DavidWells/analytics/commit/41f7f78))
* **plugin-system:** add new core plugin engine ([e122572](https://github.com/DavidWells/analytics/commit/e122572))
* **plugins:** refactor plugins to allow for hooking into plugin specific events/methods ([c1c5379](https://github.com/DavidWells/analytics/commit/c1c5379))
