# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.6.2](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.6.1...@analytics/type-utils@0.6.2) (2023-05-27)

**Note:** Version bump only for package @analytics/type-utils





## [0.6.1](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.6.0...@analytics/type-utils@0.6.1) (2023-05-27)

**Note:** Version bump only for package @analytics/type-utils





# [0.6.0](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.5.4...@analytics/type-utils@0.6.0) (2022-03-18)


### Features

* add type checks ([f51819d](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/f51819d69d0187159911a3b626fcf0c9d118580f))





## [0.5.4](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.5.3...@analytics/type-utils@0.5.4) (2022-02-05)

**Note:** Version bump only for package @analytics/type-utils





## [0.5.3](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.5.2...@analytics/type-utils@0.5.3) (2022-01-03)

**Note:** Version bump only for package @analytics/type-utils





## [0.5.2](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.5.1...@analytics/type-utils@0.5.2) (2022-01-02)

**Note:** Version bump only for package @analytics/type-utils





## [0.5.1](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.5.0...@analytics/type-utils@0.5.1) (2021-12-12)

**Note:** Version bump only for package @analytics/type-utils





# [0.5.0](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.4.0...@analytics/type-utils@0.5.0) (2021-10-24)


### Features

* add isEmail to types ([0ced368](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/0ced368))





# [0.4.0](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.3.1...@analytics/type-utils@0.4.0) (2021-10-17)


### Features

* add truthy check ([5d47089](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/5d47089))





## [0.3.1](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.3.0...@analytics/type-utils@0.3.1) (2021-08-05)

**Note:** Version bump only for package @analytics/type-utils





# [0.3.0](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.2.0...@analytics/type-utils@0.3.0) (2021-08-05)


### Features

* add isNumber, isElement + isHidden checker ([220fbf8](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/220fbf8))





# [0.2.0](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/compare/@analytics/type-utils@0.1.0...@analytics/type-utils@0.2.0) (2021-07-31)


### Features

* add isRegex util ([ce931b5](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/ce931b5))





# 0.1.0 (2021-07-26)


### Features

* add @analytics/types-utils ([07401b2](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/07401b2))
* add isBrowser ([c83c376](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-types/commit/c83c376))
