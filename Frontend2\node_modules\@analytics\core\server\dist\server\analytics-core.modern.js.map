{"version": 3, "file": "analytics-core.modern.js", "sources": ["../../../src/vendor/redux/utils/defs.js", "../../../src/vendor/redux/createStore.js", "../../../src/vendor/redux/combineReducers.js", "../../../src/vendor/redux/compose.js", "../../../src/constants.js", "../../../src/utils/internalConstants.js", "../../../src/events.js", "../../../src/middleware/initialize.js", "../../../src/modules/user.js", "../../../src/middleware/identify.js", "../../../src/utils/callback-stack.js", "../../../src/utils/waitForReady.js", "../../../src/utils/heartbeat.js", "../../../src/middleware/plugins/engine.js", "../../../src/middleware/plugins/index.js", "../../../src/utils/filterDisabled.js", "../../../src/middleware/storage.js", "../../../src/middleware/dynamic.js", "../../../src/modules/plugins.js", "../../../src/utils/serialize.js", "../../../src/modules/track.js", "../../../src/modules/queue.js", "../../../src/modules/page.js", "../../../src/modules/context.js", "../../../src/utils/getOSName/node.js", "../../../src/utils/debug.js", "../../../src/utils/ensureArray.js", "../../../src/utils/enrichMeta.js", "../../../src/utils/getCallback.js", "../../../src/utils/timestamp.js", "../../../src/index.js", "../../../src/vendor/redux/applyMiddleware.js"], "sourcesContent": ["export const FUNC = 'function'\nexport const UNDEF = 'undefined'\nexport const REDUCER = 'reducer'\n\nconst base = '@@redux/'\nexport const ACTION_INIT = base + 'INIT'\nexport const ACTION_TEST = base + Math.random().toString(36)\n", "import { isObject } from '@analytics/type-utils'\nimport { FUNC, UNDEF, ACTION_INIT, REDUCER } from './utils/defs'\n\n// eslint-disable-next-line\nconst $$observable = /* #__PURE__ */ (() => (typeof Symbol === FUNC && Symbol.observable) || '@@observable')();\n\n/*\n * Creates a Redux store that holds the state tree.\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\nconst msg = ' != ' + FUNC\nexport default function createStore(reducer, preloadedState, enhancer) {\n  if (typeof preloadedState === FUNC && typeof enhancer === UNDEF) {\n    enhancer = preloadedState\n    preloadedState = undefined\n  }\n\n  if (typeof enhancer !== UNDEF) {\n    if (typeof enhancer !== FUNC) {\n      throw new Error('enhancer' + msg)\n    }\n\n    return enhancer(createStore)(reducer, preloadedState)\n  }\n\n  if (typeof reducer !== FUNC) {\n    throw new Error(REDUCER + msg)\n  }\n\n  let currentReducer = reducer\n  let currentState = preloadedState\n  let currentListeners = []\n  let nextListeners = currentListeners\n  let isDispatching = false\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice()\n    }\n  }\n\n  /*\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n  function getState() {\n    return currentState\n  }\n\n  /*\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n  function subscribe(listener) {\n    if (typeof listener !== FUNC) {\n      throw new Error('Listener' + msg)\n    }\n\n    let isSubscribed = true\n\n    ensureCanMutateNextListeners()\n    nextListeners.push(listener)\n\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return\n      }\n\n      isSubscribed = false\n\n      ensureCanMutateNextListeners()\n      const index = nextListeners.indexOf(listener)\n      nextListeners.splice(index, 1)\n    }\n  }\n\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n  function dispatch(action) {\n    /* // add default info to actions... \n    console.log('dispatch before', _action)\n    const action = {\n      ..._action,\n      ...{\n        action: {\n          ..._action.action,\n          ...{ customInfo: 'yoooo'} \n        }\n      }\n    }\n    console.log('dispatch after', action)\n    /** */\n\n    if (!isObject(action)) {\n      throw new Error('Act != obj')\n    }\n\n    if (typeof action.type === UNDEF) {\n      throw new Error('ActType ' + UNDEF)\n    }\n\n    if (isDispatching) {\n      throw new Error('Dispatch in ' + REDUCER)\n    }\n\n    try {\n      isDispatching = true\n      currentState = currentReducer(currentState, action)\n    } finally {\n      isDispatching = false\n    }\n\n    const listeners = currentListeners = nextListeners\n    for (let i = 0; i < listeners.length; i++) {\n      const listener = listeners[i]\n      listener()\n    }\n\n    return action\n  }\n\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== FUNC) {\n      throw new Error('next ' + REDUCER + msg)\n    }\n\n    currentReducer = nextReducer\n    dispatch({ type: ACTION_INIT })\n  }\n\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n  function observable() {\n    const outerSubscribe = subscribe\n    return {\n      /*\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe(observer) {\n        if (typeof observer !== 'object') {\n          throw new TypeError('Observer != obj')\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState())\n          }\n        }\n\n        observeState()\n        const unsubscribe = outerSubscribe(observeState)\n        return { unsubscribe }\n      },\n\n      [$$observable]() {\n        return this\n      }\n    }\n  }\n\n  // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n  dispatch({ type: ACTION_INIT })\n\n  return {\n    dispatch,\n    subscribe,\n    getState,\n    replaceReducer,\n    [$$observable]: observable\n  }\n}\n", "import { isObject } from '@analytics/type-utils'\nimport warning from './utils/warning'\nimport { FUNC, UNDEF, REDUCER, ACTION_INIT, ACTION_TEST } from './utils/defs'\n\nfunction getUndefinedStateErrorMessage(key, action) {\n  const actionType = action && action.type\n  const actionName = (actionType && actionType.toString()) || '?'\n\n  return ('action ' + actionName + REDUCER + ' ' + key + ' returns ' + UNDEF)\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  const reducerKeys = Object.keys(reducers)\n  const argumentName = action && action.type === ACTION_INIT ? 'preloadedState arg passed to createStore' : 'previous state received by ' + REDUCER\n\n  if (reducerKeys.length === 0) {\n    return ('Store has no valid reducers')\n  }\n\n  if (!isObject(inputState)) {\n    return (\n      `The ${argumentName} has unexpected type of \"` +\n      ({}).toString.call(inputState).match(/\\s([a-z|A-Z]+)/)[1] +\n      `\". Expected argument to be an object with the following ` +\n      `keys: \"${reducerKeys.join('\", \"')}\"`\n    )\n  }\n\n  const unexpectedKeys = Object.keys(inputState).filter(key =>\n    !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key]\n  )\n\n  unexpectedKeys.forEach(key => {\n    unexpectedKeyCache[key] = true\n  })\n\n  if (unexpectedKeys.length > 0) {\n    return (\n      `Unexpected keys ${unexpectedKeys.join('\", \"')} in ${argumentName}. ` +\n      `Expected to find 1 of the known reducer keys instead: ${reducerKeys.join('\", \"')}`\n    )\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(key => {\n    const reducer = reducers[key]\n    const initialState = reducer(undefined, { type: ACTION_INIT })\n    if (\n      typeof initialState === UNDEF ||\n      typeof reducer(undefined, { type: ACTION_TEST }) === UNDEF\n    ) {\n      throw new Error(REDUCER + ' ' + key + ' ' + UNDEF)\n    }\n  })\n}\n\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\nexport default function combineReducers(reducers) {\n  const reducerKeys = Object.keys(reducers)\n  const finalReducers = {}\n  for (let i = 0; i < reducerKeys.length; i++) {\n    const key = reducerKeys[i]\n\n    if (NODE_ENV !== 'production') {\n      if (typeof reducers[key] === UNDEF) {\n        warning(`No reducer > ${key}`)\n      }\n    }\n\n    if (typeof reducers[key] === FUNC) {\n      finalReducers[key] = reducers[key]\n    }\n  }\n  const finalReducerKeys = Object.keys(finalReducers)\n\n  let unexpectedKeyCache\n  if (NODE_ENV !== 'production') {\n    unexpectedKeyCache = {}\n  }\n\n  let shapeAssertionError\n  try {\n    assertReducerShape(finalReducers)\n  } catch (e) {\n    shapeAssertionError = e\n  }\n\n  return function combination(state = {}, action) {\n    if (shapeAssertionError) {\n      throw shapeAssertionError\n    }\n\n    if (NODE_ENV !== 'production') {\n      const warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache)\n      if (warningMessage) {\n        warning(warningMessage)\n      }\n    }\n\n    let hasChanged = false\n    const nextState = {}\n    for (let i = 0; i < finalReducerKeys.length; i++) {\n      const key = finalReducerKeys[i]\n      const reducer = finalReducers[key]\n      const previousStateForKey = state[key]\n      const nextStateForKey = reducer(previousStateForKey, action)\n      if (typeof nextStateForKey === UNDEF) {\n        const errorMessage = getUndefinedStateErrorMessage(key, action)\n        throw new Error(errorMessage)\n      }\n      nextState[key] = nextStateForKey\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey\n    }\n    return hasChanged ? nextState : state\n  }\n}\n", "/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\n\nexport default function compose(...funcs) {\n  if (funcs.length === 0) {\n    return arg => arg\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0]\n  }\n\n  return funcs.reduce((a, b) => (...args) => a(b(...args)))\n}\n", "/**\n * Core Analytic constants. These are exposed for third party plugins & listeners\n * @typedef {Object} constants\n * @property {ANON_ID} ANON_ID - Anonymous visitor Id localstorage key\n * @property {USER_ID} USER_ID - Visitor Id localstorage key\n * @property {USER_TRAITS} USER_TRAITS - Visitor traits localstorage key\n */\nimport { PREFIX } from '@analytics/type-utils'\n\n\n/**\n * Anonymous visitor Id localstorage key\n * @typedef {String} ANON_ID\n */\nexport const ANON_ID = PREFIX + 'anon_id' // __anon_id\n/**\n * Visitor Id localstorage key\n * @typedef {String} USER_ID\n */\nexport const USER_ID = PREFIX + 'user_id' // __user_id\n/**\n * Visitor traits localstorage key\n * @typedef {String} USER_TRAITS\n */\nexport const USER_TRAITS = PREFIX + 'user_traits' // __user_traits\n", "\nexport const LIB_NAME = 'analytics'\n\nexport const ID = 'userId'\n\nexport const ANONID = 'anonymousId'\n\nexport const ERROR_URL = 'https://lytics.dev/errors/'", "/* Core Analytic Events */\n\nexport const coreEvents = [\n  /**\n   * `bootstrap` - Fires when analytics library starts up.\n   * This is the first event fired. '.on/once' listeners are not allowed on bootstrap\n   * Plugins can attach logic to this event\n   */\n  'bootstrap',\n  /**\n   * `params` - Fires when analytics parses URL parameters\n   */\n  'params',\n  /**\n   * `campaign` - Fires if params contain \"utm\" parameters\n   */\n  'campaign',\n  /**\n   * `initializeStart` - Fires before 'initialize', allows for plugins to cancel loading of other plugins\n   */\n  'initializeStart',\n  /**\n   * `initialize` - Fires when analytics loads plugins\n   */\n  'initialize',\n  /**\n   * `initializeEnd` - Fires after initialize, allows for plugins to run logic after initialization methods run\n   */\n  'initializeEnd',\n  /**\n   * `ready` - Fires when all analytic providers are fully loaded. This waits for 'initialize' and 'loaded' to return true\n   */\n  'ready',\n  /**\n   * `resetStart` - Fires if analytic.reset() is called.\n   * Use this event to cancel reset based on a specific condition\n   */\n  'resetStart',\n  /**\n   * `reset` - Fires if analytic.reset() is called.\n   * Use this event to run custom cleanup logic (if needed)\n   */\n  'reset',\n  /**\n   * `resetEnd` - Fires after analytic.reset() is called.\n   * Use this event to run a callback after user data is reset\n   */\n  'resetEnd',\n  /******************\n   * Page Events\n   ******************/\n  /**\n   * `pageStart` - Fires before 'page' events fire.\n   *  This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'pageStart',\n  /**\n   * `page` - Core analytics hook for page views.\n   *  If your plugin or integration tracks page views, this is the event to fire on.\n   */\n  'page',\n  /**\n   * `pageEnd` - Fires after all registered 'page' methods fire.\n   */\n  'pageEnd',\n  /**\n   * `pageAborted` - Fires if 'page' call is cancelled by a plugin\n   */\n  'pageAborted',\n  /****************\n   * Track Events\n   ***************/\n  /**\n   * `trackStart` - Called before the 'track' events fires.\n   *  This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'trackStart',\n  /**\n   * `track` - Core analytics hook for event tracking.\n   *  If your plugin or integration tracks custom events, this is the event to fire on.\n   */\n  'track',\n  /**\n   * `trackEnd` - Fires after all registered 'track' events fire from plugins.\n   */\n  'trackEnd',\n  /**\n   * `trackAborted` - Fires if 'track' call is cancelled by a plugin\n   */\n  'trackAborted',\n  /******************\n   * Identify Events\n   ******************/\n  /**\n   * `identifyStart` - Called before the 'identify' events fires.\n   * This allows for dynamic page view cancellation based on current state of user or options passed in.\n   */\n  'identifyStart',\n  /**\n   * `identify` - Core analytics hook for user identification.\n   *  If your plugin or integration identifies users or user traits, this is the event to fire on.\n   */\n  'identify',\n  /**\n   * `identifyEnd` - Fires after all registered 'identify' events fire from plugins.\n   */\n  'identifyEnd',\n  /**\n   * `identifyAborted` - Fires if 'track' call is cancelled by a plugin\n   */\n  'identifyAborted',\n  /**\n   * `userIdChanged` - Fires when a user id is updated\n   */\n  'userIdChanged',\n  /******************\n   * Plugin Events\n   ******************/\n  /**\n   * `registerPlugins` - Fires when analytics is registering plugins\n   */\n  'registerPlugins',\n  /**\n   * `enablePlugin` - Fires when 'analytics.plugins.enable()' is called\n   */\n  'enablePlugin',\n  /**\n   * `disablePlugin` - Fires when 'analytics.plugins.disable()' is called\n   */\n  'disablePlugin',\n  /*\n   * `loadPlugin` - Fires when 'analytics.loadPlugin()' is called\n   */\n  // 'loadPlugin',\n  /******************\n   * Browser activity events\n   ******************/\n  /**\n   * `online` - Fires when browser network goes online.\n   * This fires only when coming back online from an offline state.\n   */\n  'online',\n  /**\n   * `offline` - Fires when browser network goes offline.\n   */\n  'offline',\n  /******************\n   * Storage events\n   ******************/\n  /**\n   * `setItemStart` - Fires when analytics.storage.setItem is initialized.\n   * This event gives plugins the ability to intercept keys & values and alter them before they are persisted.\n   */\n  'setItemStart',\n  /**\n   * `setItem` - Fires when analytics.storage.setItem is called.\n   * This event gives plugins the ability to intercept keys & values and alter them before they are persisted.\n   */\n  'setItem',\n  /**\n   * `setItemEnd` - Fires when setItem storage is complete.\n   */\n  'setItemEnd',\n  /**\n   * `setItemAborted` - Fires when setItem storage is cancelled by a plugin.\n   */\n  'setItemAborted',\n  /**\n   * `removeItemStart` - Fires when analytics.storage.removeItem is initialized.\n   * This event gives plugins the ability to intercept removeItem calls and abort / alter them.\n   */\n  'removeItemStart',\n  /**\n   * `removeItem` - Fires when analytics.storage.removeItem is called.\n   * This event gives plugins the ability to intercept removeItem calls and abort / alter them.\n   */\n  'removeItem',\n  /**\n   * `removeItemEnd` - Fires when removeItem storage is complete.\n   */\n  'removeItemEnd',\n  /**\n   * `removeItemAborted` - Fires when removeItem storage is cancelled by a plugin.\n   */\n  'removeItemAborted',\n]\n\n/* Keys on a plugin that are not considered events */\nexport const nonEvents = ['name', 'EVENTS', 'config', 'loaded']\n\nconst pluginEvents = {\n  registerPluginType: (name) => `registerPlugin:${name}`,\n  pluginReadyType: (name) => `ready:${name}`,\n}\n\nconst EVENTS = coreEvents.reduce((acc, curr) => {\n  acc[curr] = curr\n  return acc\n}, pluginEvents)\n\nexport default EVENTS\n\nexport function isReservedAction(type) {\n  return coreEvents.includes(type)\n}\n", "/* eslint-disable camelcase */\nimport EVENTS from '../events'\nimport { ANON_ID, USER_ID, USER_TRAITS } from '../constants'\n\nconst utmRegex = /^utm_/\nconst propRegex = /^an_prop_/\nconst traitRegex = /^an_trait_/\n\n// Middleware runs during EVENTS.initialize\nexport default function initializeMiddleware(instance) {\n  const { setItem } = instance.storage\n  return store => next => action => {\n    /* Handle bootstrap event */\n    if (action.type === EVENTS.bootstrap) {\n      const { params, user, persistedUser, initialUser } = action\n      const isKnownId = persistedUser.userId === user.userId\n      /* 1. Set anonymous ID */\n      if (persistedUser.anonymousId !== user.anonymousId) {\n        setItem(ANON_ID, user.anonymousId)\n      }\n      /* 2. Set userId */\n      if (!isKnownId) {\n        setItem(USER_ID, user.userId)\n      }\n      /* 3. Set traits if they are different */\n      if (initialUser.traits) {\n         setItem(USER_TRAITS, {\n          ...(isKnownId && persistedUser.traits) ? persistedUser.traits : {},\n          ...initialUser.traits\n        })\n        /* TODO multi user setup\n        setItem(`${USER_TRAITS}.${user.userId}`, {\n          ...(isKnownId) ? existingTraits : {},\n          ...initialUser.traits\n        })\n        */\n      }\n      /* 4. Parse url params */\n      const paramsArray = Object.keys(action.params)\n      if (paramsArray.length) {\n        const { an_uid, an_event } = params\n        const groupedParams = paramsArray.reduce((acc, key) => {\n          // match utm params & dclid (display) & gclid (cpc)\n          if (key.match(utmRegex) || key.match(/^(d|g)clid/)) {\n            const cleanName = key.replace(utmRegex, '')\n            const keyName = (cleanName === 'campaign') ? 'name' : cleanName\n            acc.campaign[keyName] = params[key]\n          }\n          if (key.match(propRegex)) {\n            acc.props[key.replace(propRegex, '')] = params[key]\n          }\n          if (key.match(traitRegex)) {\n            acc.traits[key.replace(traitRegex, '')] = params[key]\n          }\n          return acc\n        }, {\n          campaign: {},\n          props: {},\n          traits: {}\n        })\n\n        store.dispatch({\n          type: EVENTS.params,\n          raw: params,\n          ...groupedParams,\n          ...(an_uid ? { userId: an_uid } : {}),\n        })\n\n        /* If userId set, call identify */\n        if (an_uid) {\n          // timeout to debounce and make sure integration is registered. Todo refactor\n          setTimeout(() => instance.identify(an_uid, groupedParams.traits), 0)\n        }\n\n        /* If tracking event set, call track */\n        if (an_event) {\n          // timeout to debounce and make sure integration is registered. Todo refactor\n          setTimeout(() => instance.track(an_event, groupedParams.props), 0)\n        }\n\n        // if url has utm params\n        if (Object.keys(groupedParams.campaign).length) {\n          store.dispatch({\n            type: EVENTS.campaign,\n            campaign: groupedParams.campaign\n          })\n        }\n      }\n    }\n    return next(action)\n  }\n}\n", "import { get } from '@analytics/global-storage-utils'\nimport { isObject, PREFIX } from '@analytics/type-utils'\nimport { ANON_ID, USER_ID, USER_TRAITS } from '../constants'\nimport EVENTS from '../events'\n\n/* user reducer */\nexport default function userReducer(storage) {\n  return function user(state = {}, action = {}) {\n\n    if (action.type === EVENTS.setItemEnd) {\n      // Set anonymousId if changed by storage.setItem\n      if (action.key === ANON_ID) {\n        return { ...state, ...{ anonymousId: action.value }}\n      }\n      // Set userId if changed by storage.setItem\n      if (action.key === USER_ID) {\n        return { ...state, ...{ userId: action.value }}\n      }\n    }\n\n    switch (action.type) {\n      case EVENTS.identify:\n        return Object.assign({}, state, {\n          userId: action.userId,\n          traits: {\n            ...state.traits,\n            ...action.traits\n          }\n        })\n      case EVENTS.reset:\n        // Side effect to fix race condition in Node. TODO refactor\n        // This is from default storage.removeItem: (key) => globalContext[key] = undefined\n        [ USER_ID, ANON_ID, USER_TRAITS ].forEach((key) => {\n          // sync storage, not instance.storage\n          storage.removeItem(key)\n        })\n        return Object.assign({}, state, {\n          userId: null,\n          // TODO reset anon id automatically?\n          anonymousId: null,\n          traits: {},\n        })\n      default:\n        return state\n    }\n  }\n}\n\nexport function getPersistedUserData(storage) {\n  return {\n    userId: storage.getItem(USER_ID),\n    anonymousId: storage.getItem(ANON_ID),\n    traits: storage.getItem(USER_TRAITS)\n  }\n}\n\nexport const tempKey = (key) => PREFIX + 'TEMP' + PREFIX + key\n\nexport function getUserPropFunc(storage) {\n  return function getUserProp(key, instance, payload) {\n    /* 1. Try current state */\n    const currentId = instance.getState('user')[key]\n    if (currentId) {\n      /*\n      console.log(`from state ${key}`, currentId)\n      /** */\n      return currentId\n    }\n\n    /* 2. Try event payload */\n    if (payload && isObject(payload) && payload[key]) {\n      /*\n      console.log(`from payload ${key}`, payload[key])\n      /** */\n      return payload[key]\n    }\n\n    /* 3. Try persisted data */\n    const persistedInfo = getPersistedUserData(storage)[key]\n    if (persistedInfo) {\n      /*\n      console.log(`from persistedInfo ${key}`, persistedInfo)\n      /** */\n      return persistedInfo\n    }\n\n    /* 4. Else, try to get in memory placeholder. TODO watch this for future issues */\n    return get(tempKey(key)) || null\n  }\n}\n", "import { uuid } from 'analytics-utils'\nimport { remove } from '@analytics/global-storage-utils'\nimport { tempKey } from '../modules/user'\nimport { USER_ID, USER_TRAITS, ANON_ID } from '../constants'\nimport { ID, ANONID } from '../utils/internalConstants'\nimport EVENTS from '../events'\n\nexport default function identifyMiddleware(instance) {\n  const { setItem, removeItem, getItem } = instance.storage\n  return store => next => action => {\n    const { userId, traits, options } = action\n    /* Reset user id and traits */\n    if (action.type === EVENTS.reset) {\n      // Remove stored data\n      [ USER_ID, USER_TRAITS, ANON_ID ].forEach((key) => {\n        // Fires async removeItem dispatch\n        removeItem(key)\n      });\n      [ ID, ANONID, 'traits' ].forEach((key) => {\n        // Remove from global context\n        remove(tempKey(key))\n      })\n    }\n\n    if (action.type === EVENTS.identify) {\n      /* If no anon id. Set it! */\n      if (!getItem(ANON_ID)) {\n        setItem(ANON_ID, uuid())\n      }\n\n      const currentId = getItem(USER_ID)\n      const currentTraits = getItem(USER_TRAITS) || {}\n\n      if (currentId && (currentId !== userId)) {\n        store.dispatch({\n          type: EVENTS.userIdChanged,\n          old: {\n            userId: currentId,\n            traits: currentTraits,\n          },\n          new: {\n            userId,\n            traits\n          },\n          options: options,\n        })\n      }\n\n      /* Save user id */\n      if (userId) {\n        setItem(USER_ID, userId)\n      }\n\n      /* Save user traits */\n      if (traits) {\n        setItem(USER_TRAITS, {\n          ...currentTraits,\n          ...traits\n        })\n      }\n    }\n    return next(action)\n  }\n}\n", "import { isFunction } from '@analytics/type-utils'\n\n// Stack to temporarily hold deferred promises/callbacks\nconst stack = {}\n\nfunction runCallback(id, payload) {\n  if (stack[id] && isFunction(stack[id])) {\n    // console.log(`run ${id}`)\n    stack[id](payload)\n    delete stack[id]\n  }\n}\n\nexport { stack, runCallback }", "/**\n * Wait until a given analytics provider is ready.\n * @param  {Object} data - passthrough resolve data\n * @param  {Function} predicate - function that resolves true\n * @param  {Number} timeout - max wait time\n * @return {Promise}\n */\nexport default function waitForReady(data, predicate, timeout) {\n  return new Promise((resolve, reject) => {\n    if (predicate()) {\n      return resolve(data)\n    }\n    // Timeout. Add to queue\n    if (timeout < 1) {\n      return reject({ ...data, queue: true }) // eslint-disable-line\n    }\n    // Else recursive retry\n    return pause(10).then(_ => {\n      return waitForReady(data, predicate, timeout - 10).then(resolve, reject)\n    })\n  })\n}\n\nfunction pause(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms))\n}\n", "import { isFunction, isObject } from '@analytics/type-utils'\nimport { ID, ANONID } from './internalConstants'\n\nfunction abort(reason) {\n  return { abort: reason }\n}\n\nexport function processQueue(store, getPlugins, instance) {\n  const abortedCalls = {}\n  const pluginMethods = getPlugins()\n  const { plugins, context, queue, user } = store.getState()\n  const isOnline = !context.offline\n  /* If network connection found and there is items in queue, process them all */\n  if (isOnline && queue && queue.actions && queue.actions.length) {\n    const pipeline = queue.actions.reduce((acc, item, index) => {\n      const isLoaded = plugins[item.plugin].loaded\n      if (isLoaded) {\n        acc.process.push(item)\n        acc.processIndex.push(index)\n      } else {\n        acc.requeue.push(item)\n        acc.requeueIndex.push(index)\n      }\n      return acc\n    }, {\n      processIndex: [],\n      process: [],\n      requeue: [],\n      requeueIndex: []\n    })\n\n    if (pipeline.processIndex && pipeline.processIndex.length) {\n      pipeline.processIndex.forEach((i) => {\n        const processAction = queue.actions[i]\n        // console.log('RePROCESS THIS>', processAction)\n        // Call methods directly right now\n        const currentPlugin = processAction.plugin\n        const currentMethod = processAction.payload.type\n        const method = pluginMethods[currentPlugin][currentMethod]\n        if (method && isFunction(method)) {\n          /* enrich queued payload with userId / anon id if missing */\n          /* TODO hoist enrich into where action queued? */\n          // console.log('before', processAction.payload)\n          const enrichedPayload = enrich(processAction.payload, user)\n          // console.log('user.userId', user.userId)\n          // console.log('user.anonymousId', user.anonymousId)\n          // console.log('after enrich', enrichedPayload)\n          let retVal\n          const isAborted = abortedCalls[enrichedPayload.meta.rid]\n          /* if not aborted call method */\n          if (!isAborted) {\n            // TODO make async\n            retVal = method({\n              payload: enrichedPayload,\n              config: plugins[currentPlugin].config,\n              instance,\n              abort\n            })\n            // If aborted, cancel the downstream calls\n            if (retVal && isObject(retVal) && retVal.abort) {\n              abortedCalls[enrichedPayload.meta.rid] = true\n              return\n            }\n          }\n\n          /* Then redispatch for .on listeners / other middleware */\n          if (!isAborted) {\n            const pluginEvent = `${currentMethod}:${currentPlugin}`\n            store.dispatch({\n              ...enrichedPayload,\n              type: pluginEvent,\n              /* Internal data for analytics engine */\n              _: {\n                called: pluginEvent,\n                from: 'queueDrain'\n              }\n            })\n          }\n        }\n      })\n\n      /* Removed processed actions */\n      const reQueueActions = queue.actions.filter((value, index) => {\n        // note !~ === return pipeline.processIndex.indexOf(index) === -1\n        return !~pipeline.processIndex.indexOf(index)\n      })\n\n      /* Set queue actions. TODO refactor to non mutatable or move out of redux */\n      queue.actions = reQueueActions\n\n      /*\n      if (!reQueueActions.length) {\n        console.log('Queue clears')\n        console.log('abortedCalls', abortedCalls)\n      }\n      /** */\n    }\n  }\n}\n\n/* Heartbeat retries queued events */\nexport default function heartBeat(store, getPlugins, instance) {\n  // 3e3 === 3000 ms\n  return setInterval(() => processQueue(store, getPlugins, instance), 3e3)\n}\n\n// Assign userId && anonymousId values if present in payload but null\nfunction enrich(payload = {}, user = {}) {\n  return [ ID, ANONID ].reduce((acc, key) => {\n    if (payload.hasOwnProperty(key) && user[key] && (user[key] !== payload[key])) {\n      // console.log(`${key} stale update with ${user[key]}`)\n      acc[key] = user[key]\n    }\n    return acc\n  }, payload)\n}\n", "import EVENTS from '../../events'\nimport fitlerDisabledPlugins from '../../utils/filterDisabled'\nimport { isFunction, isObject, isString } from '@analytics/type-utils'\nimport { runCallback } from '../../utils/callback-stack'\n\nconst endsWithStartRegex = /Start$/\nconst bootstrapRegex = /^bootstrap/\nconst readyRegex = /^ready/\n\nexport default async function (action, getPlugins, instance, store, eventsInfo) {\n  const pluginObject = isFunction(getPlugins) ? getPlugins() : getPlugins\n  const originalType = action.type\n  const updatedType = originalType.replace(endsWithStartRegex, '')\n\n  /* If action already dispatched exit early. This makes it so plugin methods are not fired twice. */\n  if (action._ && action._.called) {\n    // console.log('Already called', action._.called)\n    return action\n  }\n\n  const state = instance.getState()\n  /* Remove plugins that are disabled by options or by settings */\n  let activePlugins = fitlerDisabledPlugins(pluginObject, state.plugins, action.options)\n\n  /* If analytics.plugin.enable calls do special behavior */\n  if (originalType === EVENTS.initializeStart && action.fromEnable) {\n    // Return list of all enabled plugins that have NOT been initialized yet\n    activePlugins = Object.keys(state.plugins).filter((name) => {\n      const info = state.plugins[name]\n      return action.plugins.includes(name) && !info.initialized\n    }).map((name) => pluginObject[name])\n  }\n  // console.log(`engine activePlugins ${action.type}`, activePlugins)\n\n  const allActivePluginKeys = activePlugins.map((p) => p.name)\n  // console.log('allActivePluginKeys', allActivePluginKeys)\n  const allMatches = getAllMatchingCalls(originalType, activePlugins, pluginObject)\n  // console.log('allMatches', allMatches)\n\n  /* @TODO cache matches and purge on enable/disable/add plugin */\n\n  /**\n   * Process all 'actionBefore' hooks\n   * Example:\n   *  This is processes 'pageStart' methods from plugins and update the event to send through the chain\n   */\n  const actionBefore = await processEvent({\n    action: action,\n    data: {\n      exact: allMatches.before,\n      namespaced: allMatches.beforeNS\n    },\n    state: state,\n    allPlugins: pluginObject,\n    allMatches,\n    instance,\n    store,\n    EVENTS: eventsInfo\n  })\n  // console.log('____ actionBefore out', actionBefore)\n\n  /* Abort if ‘eventBefore’ returns abort data */\n  if (shouldAbortAll(actionBefore, allActivePluginKeys.length)) {\n    return actionBefore\n  }\n\n  /* Filter over the plugin method calls and remove aborted plugin by name */\n  // const activeAndNonAbortedCalls = activePlugins.filter((plugin) => {\n  //   if (shouldAbort(actionBefore, plugin.name)) return false\n  //   return true\n  // })\n  // console.log(`activeAndNonAbortedCalls ${action.type}`, activeAndNonAbortedCalls)\n\n  let actionDuring\n  if (originalType === updatedType) {\n    /* If type the same don't double process */\n    actionDuring = actionBefore\n  } else {\n    /**\n     * Process all 'action' hooks\n     * Example: This is process 'page' methods from plugins and update the event to send through\n     */\n    actionDuring = await processEvent({\n      action: {\n        ...actionBefore,\n        type: updatedType\n      },\n      data: {\n        exact: allMatches.during,\n        namespaced: allMatches.duringNS\n      },\n      state: state,\n      allPlugins: pluginObject,\n      allMatches,\n      instance,\n      store,\n      EVENTS: eventsInfo\n    })\n  }\n  // console.log('____ actionDuring', actionDuring)\n\n  /**\n   * Process all 'actionEnd' hooks\n   * Example:\n   *  This is process 'pageEnd' methods from plugins and update the event to send through\n   */\n  // Only trigger `eventTypeEnd` if originalAction has Start ending.\n  if (originalType.match(endsWithStartRegex)) {\n    const afterName = `${updatedType}End`\n    const actionAfter = await processEvent({\n      action: {\n        ...actionDuring,\n        type: afterName\n      },\n      data: {\n        exact: allMatches.after,\n        namespaced: allMatches.afterNS\n      },\n      state: state,\n      allPlugins: pluginObject,\n      allMatches,\n      instance,\n      store,\n      EVENTS: eventsInfo\n    })\n    // console.log('____ actionAfter', actionAfter)\n\n    /* Fire callback if supplied */\n    if (actionAfter.meta && actionAfter.meta.hasCallback) {\n      /*\n      console.log('End of engine action has callback')\n      console.log(actionAfter.meta)\n      console.log('stack', stack)\n      /** */\n\n      // @TODO figure out exact args calls and .on will get\n      runCallback(actionAfter.meta.rid, { payload: actionAfter })\n    }\n  }\n\n  return actionBefore\n}\n\n/**\n * Async reduce over matched plugin methods\n * Fires plugin functions\n */\nasync function processEvent({\n  data,\n  action,\n  instance,\n  state,\n  allPlugins,\n  allMatches,\n  store,\n  EVENTS\n}) {\n  const { plugins, context } = state\n  const method = action.type\n  const isStartEvent = method.match(endsWithStartRegex)\n  // console.log(`data ${method}`, data)\n  // console.log(`data allMatches ${method}`, allMatches)\n  let abortable = data.exact.map((x) => {\n    return x.pluginName\n  })\n\n  /* If abort is called from xyzStart */\n  if (isStartEvent) {\n    abortable = allMatches.during.map((x) => {\n      return x.pluginName\n    })\n  }\n\n  /* make args for functions to concume */\n  const makeArgs = argumentFactory(instance, abortable)\n  // console.log('makeArgs', makeArgs)\n\n  /* Check if plugin loaded, if not mark action for queue */\n  const queueData = data.exact.reduce((acc, thing) => {\n    const { pluginName, methodName } = thing\n    let addToQueue = false\n    // Queue actions if plugin not loaded except for initialize and reset\n    if (!methodName.match(/^initialize/) && !methodName.match(/^reset/)) {\n      addToQueue = !plugins[pluginName].loaded\n    }\n    /* If offline and its a core method. Add to queue */\n    if (context.offline && (methodName.match(/^(page|track|identify)/))) {\n      addToQueue = true\n    }\n    acc[`${pluginName}`] = addToQueue\n    return acc\n  }, {})\n\n  /* generate plugin specific payloads */\n  const payloads = await data.exact.reduce(async (scoped, curr, i) => {\n    const { pluginName } = curr\n    const curScope = await scoped\n    if (data.namespaced && data.namespaced[pluginName]) {\n      const scopedPayload = await data.namespaced[pluginName].reduce(async (acc, p, count) => {\n        // await value\n        const curScopeData = await acc\n        if (!p.method || !isFunction(p.method)) {\n          return curScopeData\n        }\n\n        /* Make sure plugins don’t call themselves */\n        validateMethod(p.methodName, p.pluginName)\n\n        function genAbort(currentAct, pname, otherPlug) {\n          return function (reason, plugins) {\n            const callsite = otherPlug || pname\n            // console.log(`__abort msg: ${reason}`)\n            // console.log(`__abort ${pname}`)\n            // console.log(`__abort xxx: ${plugins}`)\n            // console.log(`__abort otherPlug`, otherPlug)\n            return {\n              ...currentAct,\n              abort: {\n                reason: reason,\n                plugins: plugins || [pname],\n                caller: method,\n                from: callsite\n              }\n            }\n          }\n        }\n\n        const val = await p.method({\n          payload: curScopeData,\n          instance,\n          abort: genAbort(curScopeData, pluginName, p.pluginName),\n          config: getConfig(p.pluginName, plugins, allPlugins),\n          plugins: plugins\n        })\n        const returnValue = isObject(val) ? val : {}\n        return Promise.resolve({\n          ...curScopeData,\n          ...returnValue\n        })\n      }, Promise.resolve(action))\n\n      /* Set scoped payload */\n      curScope[pluginName] = scopedPayload\n    } else {\n      /* Set payload as default action */\n      curScope[pluginName] = action\n    }\n    return Promise.resolve(curScope)\n  }, Promise.resolve({}))\n  // console.log(`aaa scoped payloads ${action.type}`, payloads)\n\n  // Then call the normal methods with scoped payload\n  const resolvedAction = await data.exact.reduce(async (promise, curr, i) => {\n    const lastLoop = data.exact.length === (i + 1)\n    const { pluginName } = curr\n    const currentPlugin = allPlugins[pluginName]\n    const currentActionValue = await promise\n\n    let payloadValue = (payloads[pluginName]) ? payloads[pluginName] : {}\n    /* If eventStart, allow for value merging */\n    if (isStartEvent) {\n      payloadValue = currentActionValue\n    }\n\n    if (shouldAbort(payloadValue, pluginName)) {\n      // console.log(`> Abort from payload specific \"${pluginName}\" abort value`, payloadValue)\n      abortDispatch({\n        data: payloadValue,\n        method,\n        instance,\n        pluginName,\n        store\n      })\n      return Promise.resolve(currentActionValue)\n    }\n    if (shouldAbort(currentActionValue, pluginName)) {\n      // console.log(`> Abort from ${method} abort value`, currentActionValue)\n      if (lastLoop) {\n        abortDispatch({\n          data: currentActionValue,\n          method,\n          instance,\n          // pluginName,\n          store\n        })\n      }\n      return Promise.resolve(currentActionValue)\n    }\n\n    if (queueData.hasOwnProperty(pluginName) && queueData[pluginName] === true) {\n      // console.log('Queue this instead', pluginName)\n      store.dispatch({\n        type: `queue`,\n        plugin: pluginName,\n        payload: payloadValue,\n        /* Internal data for analytics engine */\n        _: {\n          called: `queue`,\n          from: 'queueMechanism' // for debugging\n        }\n      })\n      return Promise.resolve(currentActionValue)\n    }\n    /*\n    const checkForLoaded = () => {\n      const p = instance.getState('plugins')\n      return p[currentPlugin.name].loaded\n    }\n    // const p = instance.getState('plugins')\n    console.log(`loaded \"${currentPlugin.name}\" > ${method}:`, p[currentPlugin.name].loaded)\n    // await waitForReady(currentPlugin, checkForLoaded, 10000).then((d) => {\n    //   console.log(`Loaded ${method}`, currentPlugin.name)\n    // }).catch((e) => {\n    //   console.log(`Error ${method} ${currentPlugin.name}`, e)\n    //   // TODO dispatch failure\n    // })\n    */\n\n    // @TODO figure out if we want queuing semantics\n\n    const funcArgs = makeArgs(payloads[pluginName], allPlugins[pluginName])\n\n    // console.log(`funcArgs ${method} ${pluginName}`, funcArgs)\n\n    /* Run the plugin function */\n    const val = await currentPlugin[method]({\n      // currentPlugin: pluginName,\n      abort: funcArgs.abort,\n      // Send in original action value or scope payload\n      payload: payloadValue,\n      instance,\n      config: getConfig(pluginName, plugins, allPlugins),\n      plugins: plugins\n    })\n\n    const returnValue = isObject(val) ? val : {}\n    const merged = {\n      ...currentActionValue,\n      ...returnValue\n    }\n\n    const scopedPayload = payloads[pluginName] // || currentActionValue\n    if (shouldAbort(scopedPayload, pluginName)) {\n      // console.log(`>> HANDLE abort ${method} ${pluginName}`)\n      abortDispatch({\n        data: scopedPayload,\n        method,\n        instance,\n        pluginName,\n        store\n      })\n    } else {\n      const nameSpaceEvent = `${method}:${pluginName}`\n      const actionDepth = (nameSpaceEvent.match(/:/g) || []).length\n      if (actionDepth < 2 && !method.match(bootstrapRegex) && !method.match(readyRegex)) {\n        const updatedPayload = (isStartEvent) ? merged : payloadValue\n        // Dispatched for `.on('xyz') listeners.\n        instance.dispatch({\n          ...updatedPayload,\n          type: nameSpaceEvent,\n          _: {\n            called: nameSpaceEvent,\n            from: 'submethod'\n          }\n        })\n      }\n    }\n    // console.log('merged', merged)\n    return Promise.resolve(merged)\n  }, Promise.resolve(action))\n\n  // Dispatch End. Make sure actions don't get double dispatched. EG userIdChanged\n  if (!method.match(endsWithStartRegex) &&\n      !method.match(/^registerPlugin/) &&\n      // !method.match(/^disablePlugin/) &&\n      // !method.match(/^enablePlugin/) &&\n      !method.match(readyRegex) &&\n      !method.match(bootstrapRegex) &&\n      !method.match(/^params/) &&\n      !method.match(/^userIdChanged/)\n  ) {\n    if (EVENTS.plugins.includes(method)) {\n      // console.log(`Dont dispatch for ${method}`, resolvedAction)\n      // return resolvedAction\n    }\n    /*\n      Verify this original action setup.\n      It's intended to keep actions from double dispatching themselves\n    */\n    if (resolvedAction._ && resolvedAction._.originalAction === method) {\n      // console.log(`Dont dispatch for ${method}`, resolvedAction)\n      return resolvedAction\n    }\n\n    let endAction = {\n      ...resolvedAction,\n      ...{\n        _: {\n          originalAction: resolvedAction.type,\n          called: resolvedAction.type,\n          from: 'engineEnd'\n        }\n      }\n    }\n\n    /* If all plugins are aborted, dispatch xAborted */\n    if (shouldAbortAll(resolvedAction, data.exact.length) && !method.match(/End$/)) {\n      endAction = {\n        ...endAction,\n        ...{\n          type: resolvedAction.type + 'Aborted',\n        }\n      }\n    }\n\n    store.dispatch(endAction)\n  }\n\n  return resolvedAction\n}\n\nfunction abortDispatch({ data, method, instance, pluginName, store }) {\n  const postFix = (pluginName) ? ':' + pluginName : ''\n  const abortEvent = method + 'Aborted' + postFix\n  store.dispatch({\n    ...data,\n    type: abortEvent,\n    _: {\n      called: abortEvent,\n      from: 'abort'\n    }\n  })\n}\n\nfunction getConfig(name, pluginState, allPlugins) {\n  const pluginData = pluginState[name] || allPlugins[name]\n  if (pluginData && pluginData.config) {\n    return pluginData.config\n  }\n  return {}\n}\n\nfunction getPluginFunctions(methodName, plugins) {\n  return plugins.reduce((arr, plugin) => {\n    return (!plugin[methodName]) ? arr : arr.concat({\n      methodName: methodName,\n      pluginName: plugin.name,\n      method: plugin[methodName],\n    })\n  }, [])\n}\n\nfunction formatMethod(type) {\n  return type.replace(endsWithStartRegex, '')\n}\n\n/**\n * Return array of event names\n * @param  {String} eventType - original event type\n * @param  {String} namespace - optional namespace postfix\n * @return {array} - type, method, end\n */\nfunction getEventNames(eventType, namespace) {\n  const method = formatMethod(eventType)\n  const postFix = (namespace) ? `:${namespace}` : ''\n  // `typeStart:pluginName`\n  const type = `${eventType}${postFix}`\n  // `type:pluginName`\n  const methodName = `${method}${postFix}`\n  // `typeEnd:pluginName`\n  const end = `${method}End${postFix}`\n  return [ type, methodName, end ]\n}\n\n/* Collect all calls for a given event in the system */\nfunction getAllMatchingCalls(eventType, activePlugins, allPlugins) {\n  const eventNames = getEventNames(eventType)\n  // console.log('eventNames', eventNames)\n  // 'eventStart', 'event', & `eventEnd`\n  const core = eventNames.map((word) => {\n    return getPluginFunctions(word, activePlugins)\n  })\n  // Gather nameSpaced Events\n  return activePlugins.reduce((acc, plugin) => {\n    const { name } = plugin\n    const nameSpacedEvents = getEventNames(eventType, name)\n    // console.log('eventNames namespaced', nameSpacedEvents)\n    const [ beforeFuncs, duringFuncs, afterFuncs ] = nameSpacedEvents.map((word) => {\n      return getPluginFunctions(word, activePlugins)\n    })\n\n    if (beforeFuncs.length) {\n      acc.beforeNS[name] = beforeFuncs\n    }\n    if (duringFuncs.length) {\n      acc.duringNS[name] = duringFuncs\n    }\n    if (afterFuncs.length) {\n      acc.afterNS[name] = afterFuncs\n    }\n    return acc\n  }, {\n    before: core[0],\n    beforeNS: {},\n    during: core[1],\n    duringNS: {},\n    after: core[2],\n    afterNS: {}\n  })\n}\n\nfunction shouldAbort({ abort }, pluginName) {\n  if (!abort) return false\n  if (abort === true) return true\n  return includes(abort, pluginName) || (abort && includes(abort.plugins, pluginName))\n}\n\nfunction shouldAbortAll({ abort }, pluginsCount) {\n  if (!abort) return false\n  if (abort === true || isString(abort)) return true\n  const { plugins } = abort\n  return (isArray(abort) && (abort.length === pluginsCount)) || (isArray(plugins) && (plugins.length === pluginsCount))\n}\n\nfunction isArray(arr) {\n  return Array.isArray(arr)\n}\n\nfunction includes(arr, name) {\n  if (!arr || !isArray(arr)) return false\n  return arr.includes(name)\n}\n\n/**\n * Generate arguments to pass to plugin methods\n * @param  {Object} instance - analytics instance\n * @param  {array} abortablePlugins - plugins that can be cancelled by caller\n * @return {*} function to inject plugin params\n */\nfunction argumentFactory(instance, abortablePlugins) {\n  // console.log('abortablePlugins', abortablePlugins)\n  return function (action, plugin, otherPlugin) {\n    const { config, name } = plugin\n    let method = `${name}.${action.type}`\n    if (otherPlugin) {\n      method = otherPlugin.event\n    }\n\n    const abortF = (action.type.match(endsWithStartRegex))\n      ? abortFunction(name, method, abortablePlugins, otherPlugin, action)\n      : notAbortableError(action, method)\n\n    return {\n      /* self: plugin, for future maybe */\n      // clone objects to avoid reassign\n      payload: formatPayload(action),\n      instance: instance,\n      config: config || {},\n      abort: abortF\n    }\n  }\n}\n\nfunction abortFunction(pluginName, method, abortablePlugins, otherPlugin, action) {\n  return function (reason, plugins) {\n    const caller = (otherPlugin) ? otherPlugin.name : pluginName\n    let pluginsToAbort = (plugins && isArray(plugins)) ? plugins : abortablePlugins\n    if (otherPlugin) {\n      pluginsToAbort = (plugins && isArray(plugins)) ? plugins : [pluginName]\n      if (!pluginsToAbort.includes(pluginName) || pluginsToAbort.length !== 1) {\n        throw new Error(`Method ${method} can only abort ${pluginName} plugin. ${JSON.stringify(pluginsToAbort)} input valid`)\n      }\n    }\n    return {\n      ...action, // 🔥 todo verify this merge is ok\n      abort: {\n        reason: reason,\n        plugins: pluginsToAbort,\n        caller: method,\n        _: caller\n      }\n    }\n  }\n}\n\nfunction notAbortableError(action, method) {\n  return () => {\n    throw new Error(action.type + ' action not cancellable. Remove abort in ' + method)\n  }\n}\n\n/**\n * Verify plugin is not calling itself with whatever:myPluginName self refs\n */\nfunction validateMethod(actionName, pluginName) {\n  const text = getNameSpacedAction(actionName)\n  const methodCallMatchesPluginNamespace = text && (text.name === pluginName)\n  if (methodCallMatchesPluginNamespace) {\n    const sub = getNameSpacedAction(text.method)\n    const subText = (sub) ? 'or ' + sub.method : ''\n    throw new Error([ pluginName + ' plugin is calling method ' + actionName,\n      'Plugins cant call self',\n      `Use ${text.method} ${subText} in ${pluginName} plugin insteadof ${actionName}`]\n      .join('\\n')\n    )\n  }\n}\n\nfunction getNameSpacedAction(event) {\n  const split = event.match(/(.*):(.*)/)\n  if (!split) {\n    return false\n  }\n  return {\n    method: split[1],\n    name: split[2],\n  }\n}\n\nfunction formatPayload(action) {\n  return Object.keys(action).reduce((acc, key) => {\n    // redact type from { payload }\n    if (key === 'type') {\n      return acc\n    }\n    if (isObject(action[key])) {\n      acc[key] = Object.assign({}, action[key])\n    } else {\n      acc[key] = action[key]\n    }\n    return acc\n  }, {})\n}\n\n/*\nfunction getMatchingMethods(eventType, activePlugins) {\n  const exact = getPluginFunctions(eventType, activePlugins)\n  // console.log('exact', exact)\n  // Gather nameSpaced Events\n  return activePlugins.reduce((acc, plugin) => {\n    const { name } = plugin\n    const clean = getPluginFunctions(`${eventType}:${name}`, activePlugins)\n    if (clean.length) {\n      acc.namespaced[name] = clean\n    }\n    return acc\n  }, {\n    exact: exact,\n    namespaced: {}\n  })\n}\n*/\n", "import EVENTS, { nonEvents } from '../../events'\nimport { runCallback, stack } from '../../utils/callback-stack'\nimport waitForReady from '../../utils/waitForReady'\nimport { processQueue } from '../../utils/heartbeat'\nimport runPlugins from './engine'\n\nexport default function pluginMiddleware(instance, getPlugins, systemEvents) {\n  const isReady = {}\n  return store => next => async action => {\n    const { type, abort, plugins } = action\n    let updatedAction = action\n\n    if (abort) {\n      return next(action)\n    }\n\n    /* Analytics.plugins.enable called, we need to init the plugins */\n    if (type === EVENTS.enablePlugin) {\n      store.dispatch({\n        type: EVENTS.initializeStart,\n        plugins: plugins,\n        disabled: [],\n        fromEnable: true,\n        meta: action.meta\n      })\n    }\n    \n    if (type === EVENTS.disablePlugin) {\n      // If cached callback, resolve promise/run callback. debounced to fix race condition\n      setTimeout(() => runCallback(action.meta.rid, { payload: action }), 0)\n    }\n\n    /* @TODO implement\n    if (type === EVENTS.loadPlugin) {\n      // Rerun initialize calls in plugins\n      const allPlugins = getPlugins()\n      const pluginsToLoad = Object.keys(allPlugins).filter((name) => {\n        return plugins.includes(name)\n      }).reduce((acc, curr) => {\n        acc[curr] = allPlugins[curr]\n        return acc\n      }, {})\n      const initializeAction = {\n        type: EVENTS.initializeStart,\n        plugins: plugins\n      }\n      const updated = await runPlugins(initializeAction, pluginsToLoad, instance, store, systemEvents)\n      return next(updated)\n    }\n    */\n\n    //  || type.match(/^initializeAbort:/)\n    if (type === EVENTS.initializeEnd) {\n      const allPlugins = getPlugins()\n      const pluginsArray = Object.keys(allPlugins)\n      const allRegisteredPlugins = pluginsArray.filter((name) => {\n        return plugins.includes(name)\n      }).map((name) => {\n        return allPlugins[name]\n      })\n      let completed = []\n      let failed = []\n      let disabled = action.disabled\n      // console.log('allRegisteredPlugins', allRegisteredPlugins)\n      const waitForPluginsToLoad = allRegisteredPlugins.map((plugin) => {\n        const { loaded, name, config } = plugin\n        const loadedFn = () => loaded({ config }) // @TODO add in more to api to match other funcs?\n        /* Plugins will abort trying to load after 10 seconds. 1e4 === 10000 MS */\n        return waitForReady(plugin, loadedFn, 1e4).then((d) => {\n          if (!isReady[name]) {\n            // only dispatch namespaced rdy once\n            store.dispatch({\n              type: EVENTS.pluginReadyType(name), // `ready:${name}`\n              name: name,\n              events: Object.keys(plugin).filter((name) => {\n                return !nonEvents.includes(name)\n              })\n            })\n            isReady[name] = true\n          }\n          completed = completed.concat(name)\n\n          return plugin\n          // It's loaded! run the command\n        }).catch((e) => {\n          // Timeout Add to queue\n          // console.log('Error generic waitForReady. Push this to queue', e)\n          if (e instanceof Error) {\n            throw new Error(e)\n          }\n          failed = failed.concat(e.name)\n          // Failed to fire, add to queue\n          return e\n        })\n      })\n\n      Promise.all(waitForPluginsToLoad).then((calls) => {\n        // setTimeout to ensure runs after 'page'\n        const payload = {\n          plugins: completed,\n          failed: failed,\n          disabled: disabled\n        }\n        setTimeout(() => {\n          if (pluginsArray.length === (waitForPluginsToLoad.length + disabled.length)) {\n            store.dispatch({\n              ...{ type: EVENTS.ready },\n              ...payload,\n              \n            })\n          }\n        }, 0)\n      })\n    }\n\n    /* New plugin system */\n    if (type !== EVENTS.bootstrap) {\n      if (/^ready:([^:]*)$/.test(type)) {\n        // Immediately flush queue\n        setTimeout(() => processQueue(store, getPlugins, instance), 0)\n      }\n      const updated = await runPlugins(action, getPlugins, instance, store, systemEvents)\n      return next(updated)\n    }\n\n    return next(updatedAction)\n  }\n}\n", "import { isBoolean } from '@analytics/type-utils'\n\nexport default function fitlerDisabledPlugins(allPlugins, settings = {}, options = {}) {\n  return Object.keys(allPlugins).filter((name) => {\n    const fromCallOptions = options.plugins || {}\n    // If enabled/disabled by options. Override settings\n    if (isBoolean(fromCallOptions[name])) {\n      return fromCallOptions[name]\n    }\n    // If all: false disable everything unless true explicitly set\n    if (fromCallOptions.all === false) {\n      return false\n    }\n    // else use state.plugin settings\n    if (settings[name] && settings[name].enabled === false) {\n      return false\n    }\n    return true\n  }).map((name) => allPlugins[name])\n}\n", "import EVENTS from '../events'\n\nexport default function storageMiddleware(storage) {\n  return store => next => action => {\n    const { type, key, value, options } = action\n    if (type === EVENTS.setItem || type === EVENTS.removeItem) {\n      if (action.abort) {\n        return next(action)\n      }\n      // Run storage set or remove\n      if (type === EVENTS.setItem) {\n        storage.setItem(key, value, options)\n      } else {\n        storage.removeItem(key, options)\n      }\n    }\n    return next(action)\n  }\n}\n\n/*\n  Todo: emit events for keys we care about\n  window.addEventListener('storage', (event) => console.log(event));\n*/\n", "import { compose } from '../vendor/redux'\n\n/* Class to fix dynamic middlewares from conflicting with each other\nif more than one analytic instance is active */\nexport default class DynamicMiddleware {\n  before = []\n  after = []\n  addMiddleware = (middlewares, position) => {\n    this[position] = this[position].concat(middlewares)\n  }\n  removeMiddleware = (middleware, position) => {\n    const index = this[position].findIndex(d => d === middleware)\n    if (index === -1) return\n\n    this[position] = [\n      ...this[position].slice(0, index),\n      ...this[position].slice(index + 1),\n    ]\n  }\n  /* Not currently in use\n  resetMiddlewares = (position) => {\n    if (!position) {\n      this.before = []\n      this.after = []\n    } else {\n      this[position] = []\n    }\n  }\n  */\n  dynamicMiddlewares = (position) => {\n    return store => next => action => {\n      const middlewareAPI = {\n        getState: store.getState,\n        dispatch: (act) => store.dispatch(act),\n      }\n      const chain = this[position].map(middleware => middleware(middlewareAPI))\n      return compose(...chain)(next)(action)\n    }\n  }\n}\n", "// Integrations Reducer. Follows ducks pattern http://bit.ly/2DnERMc\nimport EVENTS from '../events'\n\nexport default function createReducer(getPlugins) {\n  return function plugins(state = {}, action) {\n    let newState = {}\n    if (action.type === 'initialize:aborted') {\n      return state\n    }\n    if (/^registerPlugin:([^:]*)$/.test(action.type)) {\n      const name = getNameFromEventType(action.type, 'registerPlugin')\n      const plugin = getPlugins()[name]\n      if (!plugin || !name) {\n        return state\n      }\n      const isEnabled = action.enabled\n      const config = plugin.config\n      newState[name] = {\n        enabled: isEnabled,\n        /* if no initialization method. Set initialized true */\n        initialized: (isEnabled) ? Boolean(!plugin.initialize) : false,\n        /* If plugin enabled === false, set loaded to false, else check plugin.loaded function */\n        loaded: (isEnabled) ? Boolean(plugin.loaded({ config })) : false,\n        config\n      }\n      return { ...state, ...newState }\n    }\n    if (/^initialize:([^:]*)$/.test(action.type)) {\n      const name = getNameFromEventType(action.type, EVENTS.initialize)\n      const plugin = getPlugins()[name]\n      if (!plugin || !name) {\n        return state\n      }\n      const config = plugin.config\n      newState[name] = {\n        ...state[name],\n        ...{\n          initialized: true,\n          /* check plugin.loaded function */\n          loaded: Boolean(plugin.loaded({ config }))\n        }\n      }\n      return { ...state, ...newState }\n    }\n    if (/^ready:([^:]*)$/.test(action.type)) {\n      // const name = getNameFromEventType(action.type, 'ready')\n      newState[action.name] = {\n        ...state[action.name],\n        ...{ loaded: true }\n      }\n      return { ...state, ...newState }\n    }\n    switch (action.type) {\n      /* case EVENTS.pluginFailed:\n        // console.log('PLUGIN_FAILED', action.name)\n        newState[action.name] = {\n          ...state[action.name],\n          ...{ loaded: false }\n        }\n        return { ...state, ...newState }\n      */\n      /* When analytics.plugins.disable called */\n      case EVENTS.disablePlugin:\n        return { \n          ...state,\n          ...togglePluginStatus(action.plugins, false, state)\n        }\n      /* When analytics.plugins.enable called */\n      case EVENTS.enablePlugin:\n        return {\n          ...state, \n          ...togglePluginStatus(action.plugins, true, state)\n        }\n      default:\n        return state\n    }\n  }\n}\n\nfunction getNameFromEventType(type, baseName) {\n  return type.substring(baseName.length + 1, type.length)\n}\n\nfunction togglePluginStatus(plugins, status, currentState) {\n  return plugins.reduce((acc, pluginKey) => {\n    acc[pluginKey] = {\n      ...currentState[pluginKey],\n      ...{\n        enabled: status \n      }\n    }\n    return acc\n  }, currentState)\n}\n", "export default function serialize(obj) {\n  try {\n   return JSON.parse(JSON.stringify(obj))\n  } catch (err) {}\n  return obj\n}", "// Track Module. Follows ducks pattern http://bit.ly/2DnERMc\nimport EVENTS from '../events'\nimport serialize from '../utils/serialize'\n\n// Track State\nconst initialState = {\n  last: {},\n  history: [],\n}\n\n// track reducer\nexport default function trackReducer(state = initialState, action) {\n  const { type, event, properties, options, meta } = action\n\n  switch (type) {\n    case EVENTS.track:\n      const trackEvent = serialize({\n        event,\n        properties,\n        ...(Object.keys(options).length) && { options: options },\n        meta\n      })\n      return {\n        ...state,\n        ...{\n          last: trackEvent,\n          // Todo prevent LARGE arrays https://bit.ly/2MnBwPT\n          history: state.history.concat(trackEvent)\n        }\n      }\n    default:\n      return state\n  }\n}\n", "import EVENTS from '../events'\n/*\nTODO figure out if this should live in state...\nQueue could be in mermory as well.\nBut also needs to be persisted to storage\n*/\n\nconst initialState = {\n  actions: [],\n}\n\nexport default function queueReducer(state = initialState, action) {\n  const { type, payload } = action\n\n  switch (type) {\n    case 'queue':\n      let actionChain\n      /* prioritize identify in event queue */\n      if (payload && payload.type && payload.type === EVENTS.identify) {\n        actionChain = [action].concat(state.actions)\n      } else {\n        actionChain = state.actions.concat(action)\n      }\n      return {\n        ...state,\n        actions: actionChain\n      }\n    case 'dequeue':\n      return []\n    // todo push events to history\n    default:\n      return state\n  }\n}\n\nexport const queueAction = (data, timestamp) => {\n  return {\n    type: 'queue',\n    timestamp: timestamp,\n    data: data\n  }\n}\n", "// Page View Reducer. Follows ducks pattern http://bit.ly/2DnERMc\nimport { isBrowser } from '@analytics/type-utils'\nimport serialize from '../utils/serialize'\n\nimport EVENTS from '../events'\n\nconst hashRegex = /#.*$/\n\nfunction canonicalUrl() {\n  if (!isBrowser) return\n  const tags = document.getElementsByTagName('link')\n  for (var i = 0, tag; tag = tags[i]; i++) {\n    if (tag.getAttribute('rel') === 'canonical') {\n      return tag.getAttribute('href')\n    }\n  }\n}\n\nfunction urlPath(url) {\n  const regex = /(http[s]?:\\/\\/)?([^\\/\\s]+\\/)(.*)/g\n  const matches = regex.exec(url)\n  const pathMatch = (matches && matches[3]) ? matches[3].split('?')[0].replace(hashRegex, '') : ''\n  return '/' + pathMatch\n}\n\n/**\n * Return the canonical URL and rmove the hash.\n * @param  {string} search - search param\n * @return {string} return current canonical URL\n */\nfunction currentUrl(search) {\n  const canonical = canonicalUrl()\n  if (!canonical) return window.location.href.replace(hashRegex, '')\n  return canonical.match(/\\?/) ? canonical : canonical + search\n}\n\n/**\n * Page data for overides\n * @typedef {object} PageData\n * @property {string} [title] - Page title\n * @property {string} [url] - Page url\n * @property {string} [path] - Page path\n * @property {string} [search] - Page search\n * @property {string} [width] - Page width\n * @property {string} [height] - Page height\n*/\n\n/**\n * Get information about current page\n * @typedef {Function} getPageData\n * @param  {PageData} [pageData = {}] - Page data overides\n * @return {PageData} resolved page data\n */\nexport const getPageData = (pageData = {}) => {\n  if (!isBrowser) return pageData\n  const { title, referrer } = document\n  const { location, innerWidth, innerHeight } = window\n  const { hash, search } = location\n  const url = currentUrl(search)\n  const page = {\n    title: title,\n    url: url,\n    path: urlPath(url),\n    hash: hash,\n    search: search,\n    width: innerWidth,\n    height: innerHeight,\n  }\n  if (referrer && referrer !== '') {\n    page.referrer = referrer\n  }\n\n  return {\n    ...page,\n    /* .page() user overrrides */\n    ...pageData\n  }\n}\n\nconst initialState = {\n  last: {},\n  history: [],\n}\n\n// page reducer\nexport default function page(state = initialState, action) {\n  const { properties, options, meta } = action\n  switch (action.type) {\n    case EVENTS.page:\n      const viewData = serialize({\n        properties,\n        meta,\n        ...(Object.keys(options).length) && { options: options },\n      })\n      return {\n        ...state,\n        ...{\n          last: viewData,\n          // Todo prevent LARGE arrays https://bit.ly/2MnBwPT\n          history: state.history.concat(viewData)\n        }\n      }\n    default:\n      return state\n  }\n}\n", "// Context Reducer.  Follows ducks pattern http://bit.ly/2DnERMc\nimport { getBrowserLocale, getTimeZone, uuid } from 'analytics-utils'\nimport { isBrowser } from '@analytics/type-utils'\nimport EVENTS from '../events'\nimport { LIB_NAME } from '../utils/internalConstants'\nimport getOSNameNode from '../utils/getOSName/node'\nimport getOSNameBrowser from '../utils/getOSName/browser'\n\nlet osName\nlet referrer\nlet locale\nlet timeZone\nif (BROWSER) {\n  osName = getOSNameBrowser()\n  referrer = (isBrowser) ? document.referrer : null\n  locale = getBrowserLocale()\n  timeZone = getTimeZone()\n} else {\n  osName = getOSNameNode()\n  referrer = {}\n}\n\nconst initialState = {\n  initialized: false,\n  sessionId: uuid(),\n  app: null,\n  version: null,\n  debug: false,\n  offline: (isBrowser) ? !navigator.onLine : false, // use node network is-online\n  os: {\n    name: osName,\n  },\n  userAgent: (isBrowser) ? navigator.userAgent : 'node', // https://github.com/bestiejs/platform.js\n  library: {\n    name: LIB_NAME,\n    // TODO fix version number. npm run publish:patch has wrong version\n    version: VERSION\n  },\n  timezone: timeZone,\n  locale: locale,\n  campaign: {},\n  referrer: referrer,\n}\n\n// context reducer\nexport default function context(state = initialState, action) {\n  const { initialized } = state\n  const { type, campaign } = action\n  switch (type) {\n    case EVENTS.campaign:\n      return {\n        ...state,\n        ...{ campaign: campaign }\n      }\n    case EVENTS.offline:\n      return {\n        ...state,\n        ...{ offline: true }\n      }\n    case EVENTS.online:\n      return {\n        ...state,\n        ...{ offline: false }\n      }\n    default:\n      if (!initialized) {\n        return {\n          ...initialState,\n          ...state,\n          ...{ initialized: true }\n        }\n      }\n      return state\n  }\n}\n\nconst excludeItems = ['plugins', 'reducers', 'storage']\n// Pull plugins and reducers off intital config\nexport function makeContext(config) {\n  return Object.keys(config).reduce((acc, current) => {\n    if (excludeItems.includes(current)) {\n      return acc\n    }\n    acc[current] = config[current]\n    return acc\n  }, {})\n}\n", "// import os from 'os'\n\n// TODO fix os. os getting stripped out for node build\nexport default function getNodeOS() {\n  return 'na' // os.platform()\n}\n", "import { set, globalContext, KEY } from '@analytics/global-storage-utils'\nimport { compose } from '../vendor/redux'\nimport { LIB_NAME } from './internalConstants'\n\nexport function Debug() {\n  // Global key is window.__global__.analytics\n  set(LIB_NAME, [])\n  // Return debugger\n  return (createStore) => {\n    return (reducer, preloadedState, enhancer) => {\n      const store = createStore(reducer, preloadedState, enhancer)\n      const origDispatch = store.dispatch\n      const dispatch = (action) => {\n        const a = action.action || action\n        globalContext[KEY][LIB_NAME].push(a)\n        return origDispatch(action)\n      }\n      return Object.assign(store, { dispatch: dispatch })\n    }\n  }\n}\n\nexport function composeWithDebug(config) {\n  return function () {\n    return compose(compose.apply(null, arguments), Debug(config))\n  }\n}\n", "import { isArray } from '@analytics/type-utils'\n\nexport default function ensureArray(singleOrArray) {\n  if (!singleOrArray) return []\n  if (isArray(singleOrArray)) return singleOrArray\n  return [singleOrArray] \n}", "import getCallback from './getCallback'\nimport { stack } from './callback-stack'\nimport timestamp from './timestamp'\nimport { uuid } from 'analytics-utils'\n\n// Async promise resolver\nfunction deferredPromiseResolver(resolver, callback) {\n  return (data) => {\n    if (callback) callback(data)\n    resolver(data)\n  }\n}\n\nexport default function generateMeta(meta = {}, resolve, possibleCallbacks) {\n    const rid = uuid()\n    if (resolve) {\n      // stack[`${rid}-info`] = meta\n      stack[rid] = deferredPromiseResolver(resolve, getCallback(possibleCallbacks))\n    }\n    return {\n      ...meta,\n      rid: rid,\n      ts: timestamp(),\n      ...(!resolve) ? {} : { hasCallback: true },\n    }\n  }", "import { isFunction } from '@analytics/type-utils'\n\n/**\n * Grab first function found from arguments\n * @param {array} [argArray] - arguments array to find first function\n * @returns {Function|undefined}\n */\nexport default function getCallbackFromArgs(argArray) {\n  const args = argArray || Array.prototype.slice.call(arguments)\n  let cb\n  for (let i = 0; i < args.length; i++) {\n    if (isFunction(args[i])) {\n      cb = args[i]; break;\n    }\n  }\n  return cb\n}", "\nexport default function timeStamp() {\n  return new Date().getTime()\n}\n", "import { uuid, paramsParse, dotProp } from 'analytics-utils'\nimport { get, set, remove } from '@analytics/global-storage-utils'\nimport { isBrowser, isFunction, isObject, isString } from '@analytics/type-utils'\nimport { createStore, combineReducers, applyMiddleware, compose } from './vendor/redux'\nimport * as CONSTANTS from './constants'\nimport { ID, ANONID, ERROR_URL } from './utils/internalConstants'\nimport EVENTS, { coreEvents, nonEvents, isReservedAction } from './events'\n// Middleware\nimport * as middleware from './middleware'\nimport DynamicMiddleware from './middleware/dynamic'\n// Modules\nimport pluginsMiddleware from './modules/plugins'\nimport track from './modules/track'\nimport queue from './modules/queue'\nimport page, { getPageData } from './modules/page'\nimport context, { makeContext } from './modules/context'\nimport user, { getUserPropFunc, tempKey, getPersistedUserData } from './modules/user'\n/* Utils */\nimport { watch } from './utils/handleNetworkEvents'\nimport { Debug, composeWithDebug } from './utils/debug'\nimport heartBeat from './utils/heartbeat'\nimport ensureArray from './utils/ensureArray'\nimport enrichMeta from './utils/enrichMeta'\nimport './pluginTypeDef'\n\n\n/**\n * Analytics library configuration\n *\n * After the library is initialized with config, the core API is exposed & ready for use in the application.\n *\n * @param {object} config - analytics core config\n * @param {string} [config.app] - Name of site / app\n * @param {string|number} [config.version] - Version of your app\n * @param {boolean} [config.debug] - Should analytics run in debug mode\n * @param {Array.<AnalyticsPlugin>}  [config.plugins] - Array of analytics plugins\n * @return {AnalyticsInstance} Analytics Instance\n * @example\n *\n * import Analytics from 'analytics'\n * import pluginABC from 'analytics-plugin-abc'\n * import pluginXYZ from 'analytics-plugin-xyz'\n *\n * // initialize analytics\n * const analytics = Analytics({\n *   app: 'my-awesome-app',\n *   plugins: [\n *     pluginABC,\n *     pluginXYZ\n *   ]\n * })\n *\n */\nfunction analytics(config = {}) {\n  const customReducers = config.reducers || {}\n  const initialUser = config.initialUser || {}\n  // @TODO add custom value reolvers for userId and anonId\n  // const resolvers = config.resolvers || {}\n  // if (BROWSER) {\n  //   console.log('INIT browser')\n  // }\n  // if (SERVER) {\n  //   console.log('INIT SERVER')\n  // }\n  /* Parse plugins array */\n  const parsedOptions = (config.plugins || []).reduce((acc, plugin) => {\n    if (isFunction(plugin)) {\n      /* Custom redux middleware */\n      acc.middlewares = acc.middlewares.concat(plugin)\n      return acc\n    }\n    // Legacy plugin with name\n    if (plugin.NAMESPACE) plugin.name = plugin.NAMESPACE\n    if (!plugin.name) {\n      /* Plugins must supply a \"name\" property. See error url for more details */\n      throw new Error(ERROR_URL + '1')\n    }\n    // Set config if empty\n    if (!plugin.config) plugin.config = {}\n    // if plugin exposes EVENTS capture available events\n    const definedEvents = (plugin.EVENTS) ? Object.keys(plugin.EVENTS).map((k) => {\n      return plugin.EVENTS[k]\n    }) : []\n\n    const enabledFromMerge = !(plugin.enabled === false)\n    const enabledFromPluginConfig = !(plugin.config.enabled === false)\n    // top level { enabled: false } takes presidence over { config: enabled: false }\n    acc.pluginEnabled[plugin.name] = enabledFromMerge && enabledFromPluginConfig\n    delete plugin.enabled\n\n    if (plugin.methods) {\n      acc.methods[plugin.name] = Object.keys(plugin.methods).reduce((a, c) => {\n        // enrich methods with analytics instance\n        a[c] = appendArguments(plugin.methods[c])\n        return a\n      }, {})\n      // Remove additional methods from plugins\n      delete plugin.methods\n    }\n    // Convert available methods into events\n    const methodsToEvents = Object.keys(plugin)\n    // Combine events\n    const allEvents = methodsToEvents.concat(definedEvents)\n    // Dedupe events list\n    const allEventsUnique = new Set(acc.events.concat(allEvents))\n    acc.events = Array.from(allEventsUnique)\n\n    acc.pluginsArray = acc.pluginsArray.concat(plugin)\n\n    if (acc.plugins[plugin.name]) {\n      throw new Error(plugin.name + 'AlreadyLoaded')\n    }\n    acc.plugins[plugin.name] = plugin\n    if (!acc.plugins[plugin.name].loaded) {\n      // set default loaded func\n      acc.plugins[plugin.name].loaded = () => true\n    }\n    return acc\n  }, {\n    plugins: {},\n    pluginEnabled: {},\n    methods: {},\n    pluginsArray: [],\n    middlewares: [],\n    events: []\n  })\n  \n  /* Storage by default is set to global & is not persisted */\n  const storage = (config.storage) ? config.storage : {\n    getItem: get,\n    setItem: set,\n    removeItem: remove\n  }\n\n  const getUserProp = getUserPropFunc(storage)\n\n  // mutable intregrations object for dynamic loading\n  let customPlugins = parsedOptions.plugins\n\n  /* Grab all registered events from plugins loaded */\n  const allPluginEvents = parsedOptions.events.filter((name) => {\n    return !nonEvents.includes(name)\n  }).sort()\n  const uniqueEvents = new Set(allPluginEvents.concat(coreEvents).filter((name) => {\n    return !nonEvents.includes(name)\n  }))\n  const allSystemEvents = Array.from(uniqueEvents).sort()\n\n  /* plugin methods(functions) must be kept out of state. thus they live here */\n  const getPlugins = () => customPlugins\n\n  const {\n    addMiddleware,\n    removeMiddleware,\n    dynamicMiddlewares\n  } = new DynamicMiddleware()\n\n  const nonAbortable = () => {\n    // throw new Error(`${ERROR_URL}3`)\n    throw new Error('Abort disabled inListener')\n  }\n  \n  // Parse URL parameters\n  const params = paramsParse()\n  // Initialize visitor information\n  const persistedUser = getPersistedUserData(storage)\n  const visitorInfo = {\n    ...persistedUser,\n    ...initialUser,\n    ...(!params.an_uid) ? {} : { userId: params.an_uid },\n    ...(!params.an_aid) ? {} : { anonymousId: params.an_aid },\n  }\n  // If no anon id set, create one\n  if (!visitorInfo.anonymousId) {\n    visitorInfo.anonymousId = uuid()\n  }\n\n  /**\n   * Async Management methods for plugins. \n   * \n   * This is also where [custom methods](https://bit.ly/329vFXy) are loaded into the instance.\n   * @typedef {Object} Plugins\n   * @property {EnablePlugin} enable - Set storage value\n   * @property {DisablePlugin} disable - Remove storage value\n   * @example\n   *\n   * // Enable a plugin by namespace\n   * analytics.plugins.enable('keenio')\n   *\n   * // Disable a plugin by namespace\n   * analytics.plugins.disable('google-analytics')\n   */\n  const plugins = {\n    /**\n     * Enable analytics plugin\n     * @typedef {Function} EnablePlugin\n     * @param  {string|string[]} plugins - name of plugins(s) to disable\n     * @param  {Function} [callback] - callback after enable runs\n     * @returns {Promise}\n     * @example\n     *\n     * analytics.plugins.enable('google-analytics').then(() => {\n     *   console.log('do stuff')\n     * })\n     *\n     * // Enable multiple plugins at once\n     * analytics.plugins.enable(['google-analytics', 'segment']).then(() => {\n     *   console.log('do stuff')\n     * })\n     */\n    enable: (plugins, callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.enablePlugin,\n          plugins: ensureArray(plugins),\n          _: { originalAction: EVENTS.enablePlugin },\n        }, resolve, [ callback ])\n      })\n    },\n    /**\n     * Disable analytics plugin\n     * @typedef {Function} DisablePlugin\n     * @param  {string|string[]} plugins - name of integration(s) to disable\n     * @param  {Function} [callback] - callback after disable runs\n     * @returns {Promise}\n     * @example\n     *\n     * analytics.plugins.disable('google').then(() => {\n     *   console.log('do stuff')\n     * })\n     *\n     * analytics.plugins.disable(['google', 'segment']).then(() => {\n     *   console.log('do stuff')\n     * })\n     */\n    disable: (plugins, callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.disablePlugin,\n          plugins: ensureArray(plugins),\n          _: { originalAction: EVENTS.disablePlugin },\n        }, resolve, [callback])\n      })\n    },\n    /*\n     * Load registered analytic providers.\n     * @param  {String} plugins - integration namespace\n     *\n     * @example\n     * analytics.plugins.load('segment')\n     @TODO implement\n    load: (plugins) => {\n      store.dispatch({\n        type: EVENTS.loadPlugin,\n        // Todo handle multiple plugins via array\n        plugins: (plugins) ? [plugins] : Object.keys(getPlugins()),\n      })\n    },\n    */\n    /* @TODO if it stays, state loaded needs to be set. Re PLUGIN_INIT above\n    add: (newPlugin) => {\n      if (typeof newPlugin !== 'object') return false\n      // Set on global integration object\n      customPlugins = Object.assign({}, customPlugins, {\n        [`${newPlugin.name}`]: newPlugin\n      })\n      // then add it, and init state key\n      store.dispatch({\n        type: EVENTS.pluginRegister,\n        name: newPlugin.name,\n        plugin: newPlugin\n      })\n    }, */\n    // Merge in custom plugin methods\n    ...parsedOptions.methods\n  }\n  \n  let readyCalled = false\n  /**\n   * Analytic instance returned from initialization\n   * @typedef {Object} AnalyticsInstance\n   * @property {Identify} identify - Identify a user\n   * @property {Track} track - Track an analytics event\n   * @property {Page} page - Trigger page view\n   * @property {User} user - Get user data\n   * @property {Reset} reset - Clear information about user & reset analytics\n   * @property {Ready} ready - Fire callback on analytics ready event\n   * @property {On} on - Fire callback on analytics lifecycle events.\n   * @property {Once} once - Fire callback on analytics lifecycle events once.\n   * @property {GetState} getState - Get data about user, activity, or context.\n   * @property {Storage} storage - storage methods\n   * @property {Plugins} plugins - plugin methods\n   */\n  const instance = {\n    /**\n    * Identify a user. This will trigger `identify` calls in any installed plugins and will set user data in localStorage\n    * @typedef {Function} Identify\n    * @param  {String}   userId  - Unique ID of user\n    * @param  {Object}   [traits]  - Object of user traits\n    * @param  {Object}   [options] - Options to pass to identify call\n    * @param  {Function} [callback] - Callback function after identify completes\n    * @returns {Promise}\n    * @api public\n    *\n    * @example\n    *\n    * // Basic user id identify\n    * analytics.identify('xyz-123')\n    *\n    * // Identify with additional traits\n    * analytics.identify('xyz-123', {\n    *   name: 'steve',\n    *   company: 'hello-clicky'\n    * })\n    *\n    * // Fire callback with 2nd or 3rd argument\n    * analytics.identify('xyz-123', () => {\n    *   console.log('do this after identify')\n    * })\n    *\n    * // Disable sending user data to specific analytic tools\n    * analytics.identify('xyz-123', {}, {\n    *   plugins: {\n    *     // disable sending this identify call to segment\n    *     segment: false\n    *   }\n    * })\n    *\n    * // Send user data to only to specific analytic tools\n    * analytics.identify('xyz-123', {}, {\n    *   plugins: {\n    *     // disable this specific identify in all plugins except customerio\n    *     all: false,\n    *     customerio: true\n    *   }\n    * })\n    */\n    identify: async (userId, traits, options, callback) => {\n      const id = isString(userId) ? userId : null\n      const data = isObject(userId) ? userId : traits\n      const opts = options || {}\n      const user = instance.user()\n\n      /* sets temporary in memory id. Not to be relied on */\n      set(tempKey(ID), id)\n\n      const resolvedId = id || data.userId || getUserProp(ID, instance, data)\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.identifyStart,\n          userId: resolvedId,\n          traits: data || {},\n          options: opts,\n          anonymousId: user.anonymousId,\n          // Add previousId if exists\n          ...(user.id && (user.id !== id) && { previousId: user.id }),\n        }, resolve, [traits, options, callback])\n      })\n    },\n    /**\n     * Track an analytics event. This will trigger `track` calls in any installed plugins\n     * @typedef {Function} Track\n     * @param  {String}   eventName - Event name\n     * @param  {Object}   [payload]   - Event payload\n     * @param  {Object}   [options]   - Event options\n     * @param  {Function} [callback]  - Callback to fire after tracking completes\n     * @returns {Promise}\n     * @api public\n     *\n     * @example\n     *\n     * // Basic event tracking\n     * analytics.track('buttonClicked')\n     *\n     * // Event tracking with payload\n     * analytics.track('itemPurchased', {\n     *   price: 11,\n     *   sku: '1234'\n     * })\n     *\n     * // Fire callback with 2nd or 3rd argument\n     * analytics.track('newsletterSubscribed', () => {\n     *   console.log('do this after track')\n     * })\n     *\n     * // Disable sending this event to specific analytic tools\n     * analytics.track('cartAbandoned', {\n     *   items: ['xyz', 'abc']\n     * }, {\n     *   plugins: {\n     *     // disable track event for segment\n     *     segment: false\n     *   }\n     * })\n     *\n     * // Send event to only to specific analytic tools\n     * analytics.track('customerIoOnlyEventExample', {\n     *   price: 11,\n     *   sku: '1234'\n     * }, {\n     *   plugins: {\n     *     // disable this specific track call all plugins except customerio\n     *     all: false,\n     *     customerio: true\n     *   }\n     * })\n     */\n    track: async (eventName, payload, options, callback) => {\n      const name = isObject(eventName) ? eventName.event : eventName\n      if (!name || !isString(name)) {\n        throw new Error('EventMissing')\n      }\n      const data = isObject(eventName) ? eventName : (payload || {})\n      const opts = isObject(options) ? options : {}\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.trackStart,\n          event: name,\n          properties: data,\n          options: opts,\n          userId: getUserProp(ID, instance, payload),\n          anonymousId: getUserProp(ANONID, instance, payload),\n        }, resolve, [payload, options, callback])\n      })\n    },\n    /**\n     * Trigger page view. This will trigger `page` calls in any installed plugins\n     * @typedef {Function} Page\n     * @param  {PageData} [data] - Page data overrides.\n     * @param  {Object}   [options] - Page tracking options\n     * @param  {Function} [callback] - Callback to fire after page view call completes\n     * @returns {Promise}\n     * @api public\n     *\n     * @example\n     *\n     * // Basic page tracking\n     * analytics.page()\n     *\n     * // Page tracking with page data overrides\n     * analytics.page({\n     *   url: 'https://google.com'\n     * })\n     *\n     * // Fire callback with 1st, 2nd or 3rd argument\n     * analytics.page(() => {\n     *   console.log('do this after page')\n     * })\n     *\n     * // Disable sending this pageview to specific analytic tools\n     * analytics.page({}, {\n     *   plugins: {\n     *     // disable page tracking event for segment\n     *     segment: false\n     *   }\n     * })\n     *\n     * // Send pageview to only to specific analytic tools\n     * analytics.page({}, {\n     *   plugins: {\n     *     // disable this specific page in all plugins except customerio\n     *     all: false,\n     *     customerio: true\n     *   }\n     * })\n     */\n    page: async (data, options, callback) => {\n      const d = isObject(data) ? data : {}\n      const opts = isObject(options) ? options : {}\n\n      /*\n      // @TODO add custom value reolvers for userId and anonId\n      if (resolvers.getUserId) {\n        const asyncUserId = await resolvers.getUserId()\n        console.log('x', x)\n      }\n      */\n\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.pageStart,\n          properties: getPageData(d),\n          options: opts,\n          userId: getUserProp(ID, instance, d),\n          anonymousId: getUserProp(ANONID, instance, d),\n        }, resolve, [data, options, callback])\n      })\n    },\n    /**\n     * Get user data\n     * @typedef {Function} User\n     * @param {string} [key] - dot.prop.path of user data. Example: 'traits.company.name'\n     * @returns {string|object} value of user data or null\n     *\n     * @example\n     *\n     * // Get all user data\n     * const userData = analytics.user()\n     *\n     * // Get user id\n     * const userId = analytics.user('userId')\n     *\n     * // Get user company name\n     * const companyName = analytics.user('traits.company.name')\n     */\n    user: (key) => {\n      if (key === ID || key === 'id') {\n        return getUserProp(ID, instance)\n      }\n      if (key === ANONID || key === 'anonId') {\n        return getUserProp(ANONID, instance)\n      }\n      const user = instance.getState('user')\n      if (!key) return user\n      return dotProp(user, key)\n    },\n    /**\n     * Clear all information about the visitor & reset analytic state.\n     * @typedef {Function} Reset\n     * @param {Function} [callback] - Handler to run after reset\n     * @returns {Promise}\n     * @example\n     *\n     * // Reset current visitor\n     * analytics.reset()\n     */\n    reset: (callback) => {\n      return new Promise((resolve) => {\n        store.dispatch({\n          type: EVENTS.resetStart\n        }, resolve, callback)\n      })\n    },\n    /**\n     * Fire callback on analytics ready event\n     * @typedef {Function} Ready\n     * @param  {Function} callback - function to trigger when all providers have loaded\n     * @returns {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * analytics.ready((payload) => {\n     *   console.log('all plugins have loaded or were skipped', payload);\n     * })\n     */\n    ready: (callback) => {\n      // If ready already fired. Call callback immediately\n      if (readyCalled) callback({ plugins, instance })\n      return instance.on(EVENTS.ready, (x) => {\n        callback(x)\n        readyCalled = true\n      })\n    },\n    /**\n     * Attach an event handler function for analytics lifecycle events.\n     * @typedef {Function} On\n     * @param  {String}   name - Name of event to listen to\n     * @param  {Function} callback - function to fire on event\n     * @return {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * // Fire function when 'track' calls happen\n     * analytics.on('track', ({ payload }) => {\n     *   console.log('track call just happened. Do stuff')\n     * })\n     *\n     * // Remove listener before it is called\n     * const removeListener = analytics.on('track', ({ payload }) => {\n     *   console.log('This will never get called')\n     * })\n     *\n     * // cleanup .on listener\n     * removeListener()\n     */\n    on: (name, callback) => {\n      if (!name || !isFunction(callback)) {\n        return false\n      }\n      if (name === EVENTS.bootstrap) {\n        throw new Error('.on disabled for ' + name)\n      }\n      const startRegex = /Start$|Start:/\n      if (name === '*') {\n        const beforeHandler = store => next => action => {\n          if (action.type.match(startRegex)) {\n            callback({ // eslint-disable-line\n              payload: action,\n              instance,\n              plugins: customPlugins\n            })\n          }\n          return next(action)\n        }\n        const afterHandler = store => next => action => {\n          if (!action.type.match(startRegex)) {\n            callback({ // eslint-disable-line\n              payload: action,\n              instance,\n              plugins: customPlugins\n            })\n          }\n          return next(action)\n        }\n        addMiddleware(beforeHandler, before)\n        addMiddleware(afterHandler, after)\n        /**\n         * Detach listeners\n         * @typedef {Function} DetachListeners\n         */\n        return () => {\n          removeMiddleware(beforeHandler, before)\n          removeMiddleware(afterHandler, after)\n        }\n      }\n\n      const position = (name.match(startRegex)) ? before : after // eslint-disable-line\n      const handler = store => next => action => {\n        // Subscribe to EVERYTHING\n        if (action.type === name) {\n          callback({ // eslint-disable-line\n            payload: action,\n            instance: instance,\n            plugins: customPlugins,\n            abort: nonAbortable\n          })\n        }\n        /* For future matching of event subpaths `track:*` etc\n        } else if (name.match(/\\*$/)) {\n          const match = (name === '*') ? '.' : name\n          const regex = new RegExp(`${match}`, 'g')\n        } */\n        return next(action)\n      }\n      addMiddleware(handler, position)\n      return () => removeMiddleware(handler, position)\n    },\n    /**\n     * Attach a handler function to an event and only trigger it once.\n     * @typedef {Function} Once\n     * @param  {String} name - Name of event to listen to\n     * @param  {Function} callback - function to fire on event\n     * @return {DetachListeners} - Function to detach listener\n     *\n     * @example\n     *\n     * // Fire function only once per 'track'\n     * analytics.once('track', ({ payload }) => {\n     *   console.log('This is only triggered once when analytics.track() fires')\n     * })\n     *\n     * // Remove listener before it is called\n     * const listener = analytics.once('track', ({ payload }) => {\n     *   console.log('This will never get called b/c listener() is called')\n     * })\n     *\n     * // cleanup .once listener before it fires\n     * listener()\n     */\n    once: (name, callback) => {\n      if (!name || !isFunction(callback)) {\n        return false\n      }\n      if (name === EVENTS.bootstrap) {\n        throw new Error('.once disabled for ' + name)\n      }\n      const detachListener = instance.on(name, ({ payload }) => {\n        callback({ // eslint-disable-line\n          payload: payload,\n          instance: instance,\n          plugins: customPlugins,\n          abort: nonAbortable\n        })\n        // detach listener after its called once\n        detachListener()\n      })\n      return detachListener\n    },\n    /**\n     * Get data about user, activity, or context. Access sub-keys of state with `dot.prop` syntax.\n     * @typedef {Function} GetState\n     * @param  {string} [key] - dot.prop.path value of state\n     * @return {any}\n     *\n     * @example\n     *\n     * // Get the current state of analytics\n     * analytics.getState()\n     *\n     * // Get a subpath of state\n     * analytics.getState('context.offline')\n     */\n    getState: (key) => {\n      const state = store.getState()\n      if (key) return dotProp(state, key)\n      return Object.assign({}, state)\n    },\n    /*\n     * Emit events for other plugins or middleware to react to.\n     * @param  {Object} action - event to dispatch\n     */\n    dispatch: (action) => {\n      const actionData = isString(action) ? { type: action } : action\n      if (isReservedAction(actionData.type)) {\n        throw new Error('reserved action ' + actionData.type)\n      }\n      const _private = action._ || {}\n      // Dispatch actionStart\n      // const autoPrefixType = `${actionData.type.replace(/Start$/, '')}Start`\n\n      const dispatchData = {\n        ...actionData,\n        _: {\n          originalAction: actionData.type,\n          ..._private\n        }\n        // type: `${autoPrefixType}`\n      }\n      store.dispatch(dispatchData)\n    },\n    // Do not use. Will be removed. Here for Backwards compatiblity.\n    // Moved to analytics.plugins.enable\n    enablePlugin: plugins.enable,\n    /// Do not use. Will be removed. Here for Backwards compatiblity.\n    /// Moved to analytics.plugins.disable\n    disablePlugin: plugins.disable,\n    // Do not use. Will be removed. Here for Backwards compatiblity.\n    // New plugins api\n    plugins: plugins,\n    /**\n     * Storage utilities for persisting data.\n     * These methods will allow you to save data in localStorage, cookies, or to the window.\n     * @typedef {Object} Storage\n     * @property {GetItem} getItem - Get value from storage\n     * @property {SetItem} setItem - Set storage value\n     * @property {RemoveItem} removeItem - Remove storage value\n     *\n     * @example\n     *\n     * // Pull storage off analytics instance\n     * const { storage } = analytics\n     *\n     * // Get value\n     * storage.getItem('storage_key')\n     *\n     * // Set value\n     * storage.setItem('storage_key', 'value')\n     *\n     * // Remove value\n     * storage.removeItem('storage_key')\n     */\n    storage: {\n      /**\n       * Get value from storage\n       * @typedef {Function} GetItem\n       * @param {String} key - storage key\n       * @param {Object} [options] - storage options\n       * @return {Any}\n       *\n       * @example\n       *\n       * analytics.storage.getItem('storage_key')\n       */\n      getItem: storage.getItem,\n      /**\n       * Set storage value\n       * @typedef {Function} SetItem\n       * @param {String} key - storage key\n       * @param {any} value - storage value\n       * @param {Object} [options] - storage options\n       *\n       * @example\n       *\n       * analytics.storage.setItem('storage_key', 'value')\n       */\n      setItem: (key, value, options) => {\n        store.dispatch({\n          type: EVENTS.setItemStart,\n          key: key,\n          value: value,\n          options: options\n        })\n      },\n      /**\n       * Remove storage value\n       * @typedef {Function} RemoveItem\n       * @param {String} key - storage key\n       * @param {Object} [options] - storage options\n       *\n       * @example\n       *\n       * analytics.storage.removeItem('storage_key')\n       */\n      removeItem: (key, options) => {\n        store.dispatch({\n          type: EVENTS.removeItemStart,\n          key: key,\n          options: options\n        })\n      },\n    },\n    /*\n     * Set the anonymous ID of the visitor\n     * @param {String} anonymousId - anonymous Id to set\n     * @param {Object} [options] - storage options\n     *\n     * @example\n     *\n     * // Set anonymous ID\n     * analytics.setAnonymousId('1234567')\n     */\n    setAnonymousId: (anonymousId, options) => {\n      /* sets temporary in memory id. Not to be relied on */\n      // set(tempKey(ANONID), anonymousId)\n      instance.storage.setItem(CONSTANTS.ANON_ID, anonymousId, options)\n    },\n    /*\n     * Events exposed by core analytics library and all loaded plugins\n     * @type {Array}\n     */\n    events: {\n      core: coreEvents,\n      plugins: allPluginEvents,\n      // byType: (type) => {} @Todo grab logic from engine and give inspectable events\n    }\n  }\n  const enrichMiddleware = storeAPI => next => action => {\n    if (!action.meta) {\n      action.meta = enrichMeta()\n    }\n    return next(action)\n  }\n  const middlewares = parsedOptions.middlewares.concat([\n    enrichMiddleware,\n    /* Core analytics middleware */\n    dynamicMiddlewares(before), // Before dynamic middleware <-- fixed pageStart .on listener\n    /* Plugin engine */\n    middleware.plugins(instance, getPlugins, {\n      all: allSystemEvents,\n      plugins: allPluginEvents\n    }),\n    middleware.storage(storage),\n    middleware.initialize(instance),\n    middleware.identify(instance, storage),\n    /* after dynamic middleware */\n    dynamicMiddlewares(after)\n  ])\n\n  /* Initial analytics state keys */\n  const coreReducers = {\n    context: context,\n    user: user(storage),\n    page: page,\n    track: track,\n    plugins: pluginsMiddleware(getPlugins),\n    queue: queue\n  }\n\n  let composeEnhancers = compose\n  let composeWithGlobalDebug = compose\n  if (isBrowser && config.debug) {\n    const devTools = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__\n    if (devTools) {\n      composeEnhancers = devTools({ trace: true, traceLimit: 25 })\n    }\n    composeWithGlobalDebug = function() {\n      if (arguments.length === 0) return Debug()\n      if (isObject(typeof arguments[0])) return composeWithDebug(arguments[0])\n      return composeWithDebug().apply(null, arguments)\n    }\n  }\n\n  const initialConfig = makeContext(config)\n\n  const intialPluginState = parsedOptions.pluginsArray.reduce((acc, plugin) => {\n    const { name, config, loaded } = plugin\n    const isEnabled = parsedOptions.pluginEnabled[name]\n    acc[name] = {\n      enabled: isEnabled,\n      // If plugin enabled & has no initialize method, set initialized to true, else false\n      initialized: (isEnabled) ? Boolean(!plugin.initialize) : false,\n      loaded: Boolean(loaded({ config })),\n      config\n    }\n    return acc\n  }, {})\n  \n  const initialState = {\n    context: initialConfig,\n    user: visitorInfo,\n    plugins: intialPluginState,\n    // Todo allow for more userland defined initial state?\n  }\n\n  /* Create analytics store! */\n  const store = createStore(\n    // register reducers\n    combineReducers({ ...coreReducers, ...customReducers }),\n    // set user defined initial state\n    initialState,\n    // register middleware & plugins used\n    composeWithGlobalDebug(\n      composeEnhancers(\n        applyMiddleware(...middlewares),\n      )\n    )\n  )\n\n  /* Supe up dispatch with callback promise resolver. Happens in enrichMeta */\n  function enhanceDispatch(fn) {\n    return function (event, resolver, callbacks) {\n      // console.log('original event', event)\n      const meta = enrichMeta(event.meta, resolver, ensureArray(callbacks))\n      // if (resolver) console.log('dispatch resolver', resolver)\n      // if (callbacks) console.log('dispatch callbacks', callbacks)\n      const newEvent = { ...event, ...{ meta: meta } }\n      // console.log('newEvent', newEvent)\n      return fn.apply(null, [ newEvent ])\n    }\n  }\n\n  // Automatically apply meta to dispatch calls\n  store.dispatch = enhanceDispatch(store.dispatch)\n\n  /* Synchronously call bootstrap & register Plugin methods */\n  const pluginKeys = Object.keys(customPlugins)\n\n  /* Bootstrap analytic plugins */\n  store.dispatch({\n    type: EVENTS.bootstrap,\n    plugins: pluginKeys,\n    config: initialConfig,\n    params: params,\n    user: visitorInfo,\n    initialUser,\n    persistedUser\n  })\n\n  const enabledPlugins = pluginKeys.filter((name) => parsedOptions.pluginEnabled[name])\n  const disabledPlugins = pluginKeys.filter((name) => !parsedOptions.pluginEnabled[name])\n \n  /* Register analytic plugins */\n  store.dispatch({\n    type: EVENTS.registerPlugins,\n    plugins: pluginKeys,\n    enabled: parsedOptions.pluginEnabled,\n  })\n\n  /* dispatch register for individual plugins */\n  parsedOptions.pluginsArray.map((plugin, i) => {\n    const { bootstrap, config, name } = plugin\n    if (bootstrap && isFunction(bootstrap)) {\n      bootstrap({ instance, config, payload: plugin })\n    }\n    /* Register plugins */\n    store.dispatch({\n      type: EVENTS.registerPluginType(name),\n      name: name,\n      enabled: parsedOptions.pluginEnabled[name],\n      plugin: plugin\n    })\n\n    /* All plugins registered initialize, is last loop */\n    if (parsedOptions.pluginsArray.length === (i + 1)) {\n      store.dispatch({\n        type: EVENTS.initializeStart,\n        plugins: enabledPlugins,\n        disabled: disabledPlugins\n      })\n    }\n  })\n\n  if (BROWSER) {\n    /* Watch for network events */\n    watch((offline) => {\n      store.dispatch({\n        type: (offline) ? EVENTS.offline : EVENTS.online,\n      })\n    })\n    /* Tick heartbeat for queued events */\n    heartBeat(store, getPlugins, instance)\n  }\n\n  function appendArguments(fn) {\n    return function () {\n      /* Get original args */\n      const args = Array.prototype.slice.call(arguments)\n      /* Create clone of args */\n      let newArgs = new Array(fn.length)\n      for (let i = 0; i < args.length; i++) {\n        newArgs[i] = args[i]\n      }\n      /* Append new arg to end */\n      newArgs[newArgs.length] = instance\n      // Set instance on extended methods\n      return fn.apply({ instance }, newArgs)\n    }\n  }\n\n  /* Return analytics instance */\n  return instance\n}\n\n// Duplicated strings\nconst before = 'before'\nconst after = 'after'\n\nexport default analytics\n\n/*\n * analytics.init exported for standalone browser build\n * CDN build exposes global _analytics variable\n *\n * Initialize instance with _analytics.init() or _analytics['default']()\n */\nexport { analytics as init }\n\n/*\n * analytics.Analytics exported for node usage\n *\n * Initialize instance with _analytics.init() or _analytics['default']()\n */\nexport { analytics as Analytics }\n/*\n * Core Analytic events. These are exposed for third party plugins & listeners\n * Use these magic strings to attach functions to event names.\n * @type {Object}\n */\nexport { EVENTS }\n\nexport { CONSTANTS }\n", "import compose from './compose'\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\nexport default function applyMiddleware(...middlewares) {\n  return (createStore) => (reducer, preloadedState, enhancer) => {\n    const store = createStore(reducer, preloadedState, enhancer)\n    let dispatch = store.dispatch\n    let chain = []\n\n    const middlewareAPI = {\n      getState: store.getState,\n      dispatch: (action) => dispatch(action)\n    }\n    chain = middlewares.map(middleware => middleware(middlewareAPI))\n    dispatch = compose(...chain)(store.dispatch)\n\n    return {\n      ...store,\n      dispatch\n    }\n  }\n}\n"], "names": ["FUNC", "UNDEF", "ACTION_TEST", "Math", "random", "toString", "$$observable", "Symbol", "observable", "msg", "createStore", "reducer", "preloadedState", "enhancer", "undefined", "Error", "currentReducer", "currentState", "currentListeners", "nextListeners", "isDispatching", "ensureCanMutateNextListeners", "slice", "getState", "subscribe", "listener", "isSubscribed", "push", "index", "indexOf", "splice", "dispatch", "action", "isObject", "type", "listeners", "i", "length", "base", "replaceReducer", "nextReducer", "outerSubscribe", "observer", "TypeError", "observeState", "next", "unsubscribe", "getUndefinedStateErrorMessage", "key", "actionType", "compose", "funcs", "arg", "reduce", "a", "b", "args", "ANON_ID", "PREFIX", "USER_ID", "USER_TRAITS", "ID", "ANONID", "coreEvents", "nonEvents", "acc", "curr", "registerPluginType", "name", "pluginReadyType", "utmRegex", "propRegex", "traitRegex", "initializeMiddleware", "instance", "setItem", "storage", "store", "EVENTS", "bootstrap", "params", "user", "persisted<PERSON>ser", "initialUser", "isKnownId", "userId", "anonymousId", "traits", "paramsArray", "Object", "keys", "an_uid", "an_event", "groupedParams", "match", "cleanName", "replace", "campaign", "props", "raw", "setTimeout", "identify", "track", "userReducer", "state", "setItemEnd", "value", "assign", "reset", "for<PERSON>ach", "removeItem", "getPersistedUserData", "getItem", "tempKey", "identifyMiddleware", "options", "remove", "uuid", "currentId", "currentTraits", "userIdChanged", "old", "new", "stack", "<PERSON><PERSON><PERSON><PERSON>", "id", "payload", "isFunction", "waitFor<PERSON><PERSON>y", "data", "predicate", "timeout", "Promise", "resolve", "reject", "queue", "then", "_", "abort", "reason", "processQueue", "getPlugins", "abortedCalls", "pluginMethods", "plugins", "context", "offline", "actions", "pipeline", "item", "plugin", "loaded", "process", "processIndex", "requeue", "requeueIndex", "processAction", "currentPlugin", "currentMethod", "method", "enrichedPayload", "hasOwnProperty", "enrich", "retVal", "isAborted", "meta", "rid", "config", "pluginEvent", "called", "from", "reQueueActions", "filter", "endsWithStartRegex", "bootstrapRegex", "readyRegex", "async", "processEvent", "allPlugins", "allMatches", "isStartEvent", "abortable", "exact", "map", "x", "pluginName", "during", "makeArgs", "abortablePlugins", "otherPlugin", "event", "abortF", "caller", "pluginsToAbort", "isArray", "includes", "JSON", "stringify", "abortFunction", "notAbortableError", "formatPayload", "argumentFactory", "queueData", "thing", "methodName", "addToQueue", "payloads", "scoped", "curScope", "namespaced", "scopedPayload", "p", "count", "curScopeData", "actionName", "text", "getNameSpacedAction", "sub", "join", "validate<PERSON><PERSON><PERSON>", "val", "currentAct", "pname", "otherPlug", "getConfig", "returnValue", "resolvedAction", "promise", "lastLoop", "currentActionValue", "payloadValue", "shouldAbort", "abortDispatch", "funcArgs", "merged", "nameSpaceEvent", "originalAction", "endAction", "shouldAbortAll", "abortEvent", "pluginState", "pluginData", "getPluginFunctions", "arr", "concat", "getEventNames", "eventType", "namespace", "postFix", "pluginsCount", "isString", "Array", "split", "pluginMiddleware", "systemEvents", "isReady", "updatedAction", "enablePlugin", "initializeStart", "disabled", "fromEnable", "disablePlugin", "initializeEnd", "pluginsArray", "allRegisteredPlugins", "completed", "failed", "waitForPluginsToLoad", "d", "events", "catch", "e", "all", "calls", "ready", "test", "updated", "eventsInfo", "pluginObject", "originalType", "updatedType", "activePlugins", "settings", "fromCallOptions", "isBoolean", "enabled", "fitlerDisabledPlugins", "info", "initialized", "allActivePluginKeys", "core", "word", "nameSpacedEvents", "beforeFuncs", "duringFuncs", "afterFuncs", "beforeNS", "duringNS", "afterNS", "before", "after", "getAllMatchingCalls", "actionBefore", "actionDuring", "after<PERSON>ame", "actionAfter", "<PERSON><PERSON><PERSON><PERSON>", "runPlugins", "storageMiddleware", "DynamicMiddleware", "addMiddleware", "middlewares", "position", "this", "removeMiddleware", "middleware", "findIndex", "dynamicMiddlewares", "middlewareAPI", "act", "createReducer", "newState", "getNameFromEventType", "isEnabled", "Boolean", "initialize", "togglePluginStatus", "baseName", "substring", "status", "pluginKey", "serialize", "obj", "parse", "err", "initialState", "last", "history", "trackReducer", "properties", "trackEvent", "queueReducer", "action<PERSON>hain", "hashRegex", "url<PERSON><PERSON>", "url", "matches", "exec", "getPageData", "pageData", "<PERSON><PERSON><PERSON><PERSON>", "title", "referrer", "document", "location", "innerWidth", "innerHeight", "window", "hash", "search", "canonical", "tags", "getElementsByTagName", "tag", "getAttribute", "canonicalUrl", "href", "currentUrl", "page", "path", "width", "height", "viewData", "osName", "sessionId", "app", "version", "debug", "navigator", "onLine", "os", "userAgent", "library", "timezone", "timeZone", "locale", "online", "excludeItems", "Debug", "set", "origDispatch", "globalContext", "KEY", "composeWithDebug", "apply", "arguments", "ensureArray", "singleOrArray", "generateMeta", "possibleCallbacks", "resolver", "callback", "arg<PERSON><PERSON>y", "prototype", "call", "cb", "get<PERSON>allback", "ts", "Date", "getTime", "analytics", "customReducers", "reducers", "parsedOptions", "NAMESPACE", "ERROR_URL", "definedEvents", "k", "pluginEnabled", "methods", "c", "fn", "newArgs", "allEvents", "allEventsUnique", "Set", "get", "getUserProp", "getUserPropFunc", "customPlugins", "allPluginEvents", "sort", "uniqueEvents", "allSystemEvents", "nonAbortable", "paramsParse", "visitorInfo", "an_aid", "enable", "disable", "readyCalled", "opts", "resolvedId", "identifyStart", "previousId", "eventName", "trackStart", "pageStart", "dotProp", "resetStart", "on", "startRegex", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handler", "once", "detachListener", "actionData", "dispatchData", "setItemStart", "removeItemStart", "setAnonymousId", "CONSTANTS", "storeAPI", "enrichMeta", "coreReducers", "pluginsMiddleware", "composeEnhancers", "composeWithGlobalDebug", "devTools", "__REDUX_DEVTOOLS_EXTENSION_COMPOSE__", "trace", "traceLimit", "initialConfig", "current", "makeContext", "intialPluginState", "reducerKeys", "finalReducers", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shapeAssertionError", "REDUCER", "assertReducerShape", "has<PERSON><PERSON>ed", "nextState", "previousStateForKey", "nextStateForKey", "errorMessage", "combineReducers", "chain", "applyMiddleware", "callbacks", "newEvent", "pluginKeys", "enabledPlugins", "disabledPlugins", "registerPlugins"], "mappings": "wgBAAaA,EAAO,WACPC,EAAQ,YAKRC,EAFA,WAEqBC,KAAKC,SAASC,SAAS,ICFnDC,iBAA+B,YAAeC,SAAWP,GAAQO,OAAOC,YAAe,eAAxD,GA2B/BC,EAAM,OAAST,WACGU,EAAYC,EAASC,EAAgBC,GAM3D,UALWD,IAAmBZ,UAAea,IAAaZ,IACxDY,EAAWD,EACXA,OAAiBE,UAGRD,IAAaZ,EAAO,CAC7B,UAAWY,IAAab,EACtB,UAAUe,MAAM,WAAaN,GAG/B,OAAOI,EAASH,EAATG,CAAsBF,EAASC,GAGxC,UAAWD,IAAYX,EACrB,UAAUe,MD7CS,UC6CON,GAG5B,IAAIO,EAAiBL,EACjBM,EAAeL,EACfM,EAAmB,GACnBC,EAAgBD,EAChBE,GAAgB,EAEpB,SAASC,IACHF,IAAkBD,IACpBC,EAAgBD,EAAiBI,SASrC,SAASC,IACP,OAAON,EA0BT,SAASO,EAAUC,GACjB,UAAWA,IAAazB,EACtB,UAAUe,MAAM,WAAaN,GAG/B,IAAIiB,GAAe,EAKnB,OAHAL,IACAF,EAAcQ,KAAKF,cAGjB,IAAKC,EACH,OAGFA,GAAe,EAEfL,IACA,MAAMO,EAAQT,EAAcU,QAAQJ,GACpCN,EAAcW,OAAOF,EAAO,IA6BhC,SAASG,EAASC,GAehB,IAAKC,EAASD,GACZ,UAAUjB,MAAM,cAGlB,UAAWiB,EAAOE,OAASjC,EACzB,UAAUc,MAAM,WAAad,GAG/B,GAAImB,EACF,UAAUL,MAAM,uBAGlB,IACEK,GAAgB,EAChBH,EAAeD,EAAeC,EAAce,GAF9C,QAIEZ,GAAgB,EAGlB,MAAMe,EAAYjB,EAAmBC,EACrC,IAAK,IAAIiB,EAAI,EAAGA,EAAID,EAAUE,OAAQD,KAEpCX,EADiBU,EAAUC,MAI7B,OAAOJ,EAkET,OAFAD,EAAS,CAAEG,KDjPcI,iBCmPlB,CACLP,SAAAA,EACAP,UAAAA,EACAD,SAAAA,EACAgB,eAzDF,SAAwBC,GACtB,UAAWA,IAAgBxC,EACzB,UAAUe,MAAM,eAAoBN,GAGtCO,EAAiBwB,EACjBT,EAAS,CAAEG,KDpMYI,kBCwPvBhC,CAACA,GA3CH,WACE,MAAMmC,EAAiBjB,EACvB,MAAO,CASLA,UAAUkB,GACR,GAAwB,iBAAbA,EACT,UAAUC,UAAU,mBAGtB,SAASC,IACHF,EAASG,MACXH,EAASG,KAAKtB,KAMlB,OAFAqB,IAEO,CAAEE,YADWL,EAAeG,KAIrCtC,CAACA,KACC,gBC1OR,SAASyC,EAA8BC,EAAKhB,GAC1C,MAAMiB,EAAajB,GAAUA,EAAOE,KAGpC,MAAQ,WAFYe,GAAcA,EAAW5C,YAAe,KAEpD,WAAyC2C,EAAM,YAAc/C,WCG/CiD,KAAWC,GACjC,OAAqB,IAAjBA,EAAMd,OACDe,GAAOA,EAGK,IAAjBD,EAAMd,OACDc,EAAM,GAGRA,EAAME,OAAO,CAACC,EAAGC,IAAM,IAAIC,IAASF,EAAEC,KAAKC,WCNvCC,EAAUC,EAAS,UAKnBC,EAAUD,EAAS,UAKnBE,EAAcF,EAAS,6ECrBvBG,EAAK,SAELC,EAAS,cCHTC,EAAa,CAMxB,YAIA,SAIA,WAIA,kBAIA,aAIA,gBAIA,QAKA,aAKA,QAKA,WAQA,YAKA,OAIA,UAIA,cAQA,aAKA,QAIA,WAIA,eAQA,gBAKA,WAIA,cAIA,kBAIA,gBAOA,kBAIA,eAIA,gBAYA,SAIA,UAQA,eAKA,UAIA,aAIA,iBAKA,kBAKA,aAIA,gBAIA,qBAIWC,EAAY,CAAC,OAAQ,SAAU,SAAU,UAYtD,MALeD,EAAWV,OAAO,CAACY,EAAKC,KACrCD,EAAIC,GAAQA,EACLD,GAPY,CACnBE,mBAAqBC,GAAU,kBAAiBA,IAChDC,gBAAkBD,GAAU,SAAQA,MC5LtC,MAAME,EAAW,QACXC,EAAY,YACZC,EAAa,sBAGKC,EAAqBC,GAC3C,MAAMC,QAAEA,GAAYD,EAASE,QAC7B,OAAOC,GAAShC,GAAQb,IAEtB,GAAIA,EAAOE,OAAS4C,EAAOC,UAAW,CACpC,MAAMC,OAAEA,EAAFC,KAAUA,EAAVC,cAAgBA,EAAhBC,YAA+BA,GAAgBnD,EAC/CoD,EAAYF,EAAcG,SAAWJ,EAAKI,OAE5CH,EAAcI,cAAgBL,EAAKK,aACrCX,EAAQlB,EAASwB,EAAKK,aAGnBF,GACHT,EAAQhB,EAASsB,EAAKI,QAGpBF,EAAYI,QACbZ,EAAQf,OACHwB,GAAaF,EAAcK,OAAUL,EAAcK,OAAS,GAC7DJ,EAAYI,SAUnB,MAAMC,EAAcC,OAAOC,KAAK1D,EAAOgD,QACvC,GAAIQ,EAAYnD,OAAQ,CACtB,MAAMsD,OAAEA,EAAFC,SAAUA,GAAaZ,EACvBa,EAAgBL,EAAYnC,OAAO,CAACY,EAAKjB,KAE7C,GAAIA,EAAI8C,MAAMxB,IAAatB,EAAI8C,MAAM,cAAe,CAClD,MAAMC,EAAY/C,EAAIgD,QAAQ1B,EAAU,IAExCL,EAAIgC,SAD2B,aAAdF,EAA4B,OAASA,GAC9Bf,EAAOhC,GAQjC,OANIA,EAAI8C,MAAMvB,KACZN,EAAIiC,MAAMlD,EAAIgD,QAAQzB,EAAW,KAAOS,EAAOhC,IAE7CA,EAAI8C,MAAMtB,KACZP,EAAIsB,OAAOvC,EAAIgD,QAAQxB,EAAY,KAAOQ,EAAOhC,IAE5CiB,GACN,CACDgC,SAAU,GACVC,MAAO,GACPX,OAAQ,KAGVV,EAAM9C,YACJG,KAAM4C,EAAOE,OACbmB,IAAKnB,GACFa,EACCF,EAAS,CAAEN,OAAQM,GAAW,KAIhCA,GAEFS,WAAW,IAAM1B,EAAS2B,SAASV,EAAQE,EAAcN,QAAS,GAIhEK,GAEFQ,WAAW,IAAM1B,EAAS4B,MAAMV,EAAUC,EAAcK,OAAQ,GAI9DT,OAAOC,KAAKG,EAAcI,UAAU5D,QACtCwC,EAAM9C,SAAS,CACbG,KAAM4C,EAAOmB,SACbA,SAAUJ,EAAcI,YAKhC,OAAOpD,EAAKb,aCnFQuE,EAAY3B,GAClC,gBAAqB4B,EAAQ,GAAIxE,EAAS,IAExC,GAAIA,EAAOE,OAAS4C,EAAO2B,WAAY,CAErC,GAAIzE,EAAOgB,MAAQS,EACjB,YAAY+C,EAAU,CAAElB,YAAatD,EAAO0E,QAG9C,GAAI1E,EAAOgB,MAAQW,EACjB,YAAY6C,EAAU,CAAEnB,OAAQrD,EAAO0E,QAI3C,OAAQ1E,EAAOE,MACb,KAAK4C,EAAOuB,SACV,OAAOZ,OAAOkB,OAAO,GAAIH,EAAO,CAC9BnB,OAAQrD,EAAOqD,OACfE,YACKiB,EAAMjB,OACNvD,EAAOuD,UAGhB,KAAKT,EAAO8B,MAOV,MAJA,CAAEjD,EAASF,EAASG,GAAciD,QAAS7D,IAEzC4B,EAAQkC,WAAW9D,KAEdyC,OAAOkB,OAAO,GAAIH,EAAO,CAC9BnB,OAAQ,KAERC,YAAa,KACbC,OAAQ,KAEZ,QACE,OAAOiB,aAKCO,EAAqBnC,GACnC,MAAO,CACLS,OAAQT,EAAQoC,QAAQrD,GACxB2B,YAAaV,EAAQoC,QAAQvD,GAC7B8B,OAAQX,EAAQoC,QAAQpD,UAIfqD,EAAWjE,GAAQU,EAAS,OAASA,EAASV,WCjDnCkE,EAAmBxC,GACzC,MAAMC,QAAEA,EAAFmC,WAAWA,EAAXE,QAAuBA,GAAYtC,EAASE,QAClD,OAAOC,GAAShC,GAAQb,IACtB,MAAMqD,OAAEA,EAAFE,OAAUA,EAAV4B,QAAkBA,GAAYnF,EAcpC,GAZIA,EAAOE,OAAS4C,EAAO8B,QAEzB,CAAEjD,EAASC,EAAaH,GAAUoD,QAAS7D,IAEzC8D,EAAW9D,KAEb,CAAEa,EAAIC,EAAQ,UAAW+C,QAAS7D,IAEhCoE,EAAOH,EAAQjE,OAIfhB,EAAOE,OAAS4C,EAAOuB,SAAU,CAE9BW,EAAQvD,IACXkB,EAAQlB,EAAS4D,KAGnB,MAAMC,EAAYN,EAAQrD,GACpB4D,EAAgBP,EAAQpD,IAAgB,GAE1C0D,GAAcA,IAAcjC,GAC9BR,EAAM9C,SAAS,CACbG,KAAM4C,EAAO0C,cACbC,IAAK,CACHpC,OAAQiC,EACR/B,OAAQgC,GAEVG,IAAK,CACHrC,OAAAA,EACAE,OAAAA,GAEF4B,QAASA,IAKT9B,GACFV,EAAQhB,EAAS0B,GAIfE,GACFZ,EAAQf,OACH2D,EACAhC,IAIT,OAAO1C,EAAKb,IC1DhB,MAAM2F,EAAQ,GAEd,SAASC,EAAYC,EAAIC,GACnBH,EAAME,IAAOE,EAAWJ,EAAME,MAEhCF,EAAME,GAAIC,UACHH,EAAME,aCFOG,EAAaC,EAAMC,EAAWC,GACpD,WAAWC,QAAQ,CAACC,EAASC,IACvBJ,IACKG,EAAQJ,GAGbE,EAAU,EACLG,OAAYL,GAAMM,OAAO,SAUzBH,QAAQC,GAAWjC,WAAWiC,EAP1B,KAAIG,KAAKC,GACbT,EAAaC,EAAMC,EAAWC,EAAU,IAAIK,KAAKH,EAASC,KCfvE,SAASI,EAAMC,GACb,MAAO,CAAED,MAAOC,YAGFC,EAAa/D,EAAOgE,EAAYnE,GAC9C,MAAMoE,EAAe,GACfC,EAAgBF,KAChBG,QAAEA,EAAFC,QAAWA,EAAXV,MAAoBA,EAApBtD,KAA2BA,GAASJ,EAAMtD,WAGhD,IAFkB0H,EAAQC,SAEVX,GAASA,EAAMY,SAAWZ,EAAMY,QAAQ9G,OAAQ,CAC9D,MAAM+G,EAAWb,EAAMY,QAAQ9F,OAAO,CAACY,EAAKoF,EAAMzH,KAC/BoH,EAAQK,EAAKC,QAAQC,QAEpCtF,EAAIuF,QAAQ7H,KAAK0H,GACjBpF,EAAIwF,aAAa9H,KAAKC,KAEtBqC,EAAIyF,QAAQ/H,KAAK0H,GACjBpF,EAAI0F,aAAahI,KAAKC,IAEjBqC,GACN,CACDwF,aAAc,GACdD,QAAS,GACTE,QAAS,GACTC,aAAc,KAGhB,GAAIP,EAASK,cAAgBL,EAASK,aAAapH,OAAQ,CACzD+G,EAASK,aAAa5C,QAASzE,IAC7B,MAAMwH,EAAgBrB,EAAMY,QAAQ/G,GAG9ByH,EAAgBD,EAAcN,OAC9BQ,EAAgBF,EAAc9B,QAAQ5F,KACtC6H,EAAShB,EAAcc,GAAeC,GAC5C,GAAIC,GAAUhC,EAAWgC,GAAS,CAIhC,MAAMC,EAgEhB,SAAgBlC,EAAU,GAAI7C,EAAO,IACnC,MAAO,CAAEpB,EAAIC,GAAST,OAAO,CAACY,EAAKjB,KAC7B8E,EAAQmC,eAAejH,IAAQiC,EAAKjC,IAASiC,EAAKjC,KAAS8E,EAAQ9E,KAErEiB,EAAIjB,GAAOiC,EAAKjC,IAEXiB,GACN6D,GAvE6BoC,CAAON,EAAc9B,QAAS7C,GAItD,IAAIkF,EACJ,MAAMC,EAAYtB,EAAakB,EAAgBK,KAAKC,KAEpD,IAAKF,IAEHD,EAASJ,EAAO,CACdjC,QAASkC,EACTO,OAAQvB,EAAQa,GAAeU,OAC/B7F,SAAAA,EACAgE,MAAAA,IAGEyB,GAAUlI,EAASkI,IAAWA,EAAOzB,OAEvC,YADAI,EAAakB,EAAgBK,KAAKC,MAAO,GAM7C,IAAKF,EAAW,CACd,MAAMI,EAAe,GAAEV,KAAiBD,IACxChF,EAAM9C,cACDiI,GACH9H,KAAMsI,EAEN/B,EAAG,CACDgC,OAAQD,EACRE,KAAM,qBAQhB,MAAMC,EAAiBpC,EAAMY,QAAQyB,OAAO,CAAClE,EAAO9E,MAEzCwH,EAASK,aAAa5H,QAAQD,IAIzC2G,EAAMY,QAAUwB,ICnFtB,MAAME,EAAqB,SACrBC,EAAiB,aACjBC,EAAa,SA4InBC,eAAeC,GAAahD,KAC1BA,EAD0BjG,OAE1BA,EAF0B0C,SAG1BA,EAH0B8B,MAI1BA,EAJ0B0E,WAK1BA,EAL0BC,WAM1BA,EAN0BtG,MAO1BA,EAP0BC,OAQ1BA,IAEA,MAAMkE,QAAEA,EAAFC,QAAWA,GAAYzC,EACvBuD,EAAS/H,EAAOE,KAChBkJ,EAAerB,EAAOjE,MAAM+E,GAGlC,IAAIQ,EAAYpD,EAAKqD,MAAMC,IAAKC,GACvBA,EAAEC,YAIPL,IACFC,EAAYF,EAAWO,OAAOH,IAAKC,GAC1BA,EAAEC,aAKb,MAAME,EA6WR,SAAyBjH,EAAUkH,GAEjC,gBAAiB5J,EAAQsH,EAAQuC,GAC/B,MAAMtB,OAAEA,EAAFnG,KAAUA,GAASkF,EACzB,IAAIS,EAAU,GAAE3F,KAAQpC,EAAOE,OAC3B2J,IACF9B,EAAS8B,EAAYC,OAGvB,MAAMC,EAAU/J,EAAOE,KAAK4D,MAAM+E,GAetC,SAAuBY,EAAY1B,EAAQ6B,EAAkBC,EAAa7J,GACxE,gBAAiB2G,EAAQK,GACvB,MAAMgD,EAAUH,EAAeA,EAAYzH,KAAOqH,EAClD,IAAIQ,EAAkBjD,GAAWkD,GAAQlD,GAAYA,EAAU4C,EAC/D,GAAIC,IACFI,EAAkBjD,GAAWkD,GAAQlD,GAAYA,EAAU,CAACyC,IACvDQ,EAAeE,SAASV,IAAyC,IAA1BQ,EAAe5J,QACzD,UAAUtB,MAAO,UAASgJ,oBAAyB0B,aAAsBW,KAAKC,UAAUJ,kBAG5F,YACKjK,GACH0G,MAAO,CACLC,OAAQA,EACRK,QAASiD,EACTD,OAAQjC,EACRtB,EAAGuD,MA9BHM,CAAclI,EAAM2F,EAAQ6B,EAAkBC,EAAa7J,GAoCnE,SAA2BA,EAAQ+H,GACjC,MAAO,KACL,UAAUhJ,MAAMiB,EAAOE,KAAO,4CAA8C6H,IArCxEwC,CAAkBvK,EAAQ+H,GAE9B,MAAO,CAGLjC,QAAS0E,GAAcxK,GACvB0C,SAAUA,EACV6F,OAAQA,GAAU,GAClB7B,MAAOqD,IAhYMU,CAAgB/H,EAAU2G,GAIrCqB,EAAYzE,EAAKqD,MAAMjI,OAAO,CAACY,EAAK0I,KACxC,MAAMlB,WAAEA,EAAFmB,WAAcA,GAAeD,EACnC,IAAIE,GAAa,EAUjB,OARKD,EAAW9G,MAAM,gBAAmB8G,EAAW9G,MAAM,YACxD+G,GAAc7D,EAAQyC,GAAYlC,QAGhCN,EAAQC,SAAY0D,EAAW9G,MAAM,4BACvC+G,GAAa,GAEf5I,EAAK,GAAEwH,KAAgBoB,EAChB5I,GACN,IAGG6I,QAAiB7E,EAAKqD,MAAMjI,OAAO2H,MAAO+B,EAAQ7I,EAAM9B,KAC5D,MAAMqJ,WAAEA,GAAevH,EACjB8I,QAAiBD,EACvB,GAAI9E,EAAKgF,YAAchF,EAAKgF,WAAWxB,GAAa,CAClD,MAAMyB,QAAsBjF,EAAKgF,WAAWxB,GAAYpI,OAAO2H,MAAO/G,EAAKkJ,EAAGC,KAE5E,MAAMC,QAAqBpJ,EAC3B,IAAKkJ,EAAEpD,SAAWhC,EAAWoF,EAAEpD,QAC7B,OAAOsD,GAwYjB,SAAwBC,EAAY7B,GAClC,MAAM8B,EAAOC,GAAoBF,GAEjC,GADyCC,GAASA,EAAKnJ,OAASqH,EAC1B,CACpC,MAAMgC,EAAMD,GAAoBD,EAAKxD,QAErC,UAAUhJ,MAAM,CAAE0K,EAAa,6BAA+B6B,EAC5D,yBACC,OAAMC,EAAKxD,UAHG0D,EAAO,MAAQA,EAAI1D,OAAS,SAGP0B,sBAA+B6B,KAClEI,KAAK,QA7YJC,CAAeR,EAAEP,WAAYO,EAAE1B,YAqB/B,MAAMmC,QAAYT,EAAEpD,OAAO,CACzBjC,QAASuF,EACT3I,SAAAA,EACAgE,OAtBgBmF,EAsBAR,EAtBYS,EAsBErC,EAtBKsC,EAsBOZ,EAAE1B,oBArB3B9C,EAAQK,GAMvB,YACK6E,GACHnF,MAAO,CACLC,OAAQA,EACRK,QAASA,GAAW,CAAC8E,GACrB9B,OAAQjC,EACRW,KAXaqD,GAAaD,OAqBhCvD,OAAQyD,EAAUb,EAAE1B,WAAYzC,EAASkC,GACzClC,QAASA,IAxBX,IAAkB6E,EAAYC,EAAOC,EA0BrC,MAAME,EAAchM,EAAS2L,GAAOA,EAAM,GAC1C,OAAOxF,QAAQC,aACVgF,EACAY,KAEJ7F,QAAQC,QAAQrG,IAGnBgL,EAASvB,GAAcyB,OAGvBF,EAASvB,GAAczJ,EAEzB,OAAOoG,QAAQC,QAAQ2E,IACtB5E,QAAQC,QAAQ,KAIb6F,QAAuBjG,EAAKqD,MAAMjI,OAAO2H,MAAOmD,EAASjK,EAAM9B,KACnE,MAAMgM,EAAWnG,EAAKqD,MAAMjJ,SAAYD,EAAI,GACtCqJ,WAAEA,GAAevH,EACjB2F,EAAgBqB,EAAWO,GAC3B4C,QAA2BF,EAEjC,IAAIG,EAAgBxB,EAASrB,GAAeqB,EAASrB,GAAc,GAMnE,GAJIL,IACFkD,EAAeD,GAGbE,GAAYD,EAAc7C,GAS5B,OAPA+C,EAAc,CACZvG,KAAMqG,EACNvE,OAAAA,EACArF,SAAAA,EACA+G,WAAAA,EACA5G,MAAAA,IAEKuD,QAAQC,QAAQgG,GAEzB,GAAIE,GAAYF,EAAoB5C,GAWlC,OATI2C,GACFI,EAAc,CACZvG,KAAMoG,EACNtE,OAAAA,EACArF,SAAAA,EAEAG,MAAAA,IAGGuD,QAAQC,QAAQgG,GAGzB,GAAI3B,EAAUzC,eAAewB,KAAyC,IAA1BiB,EAAUjB,GAYpD,OAVA5G,EAAM9C,SAAS,CACbG,KAAO,QACPoH,OAAQmC,EACR3D,QAASwG,EAET7F,EAAG,CACDgC,OAAS,QACTC,KAAM,oBAGHtC,QAAQC,QAAQgG,GAmBzB,MAAMI,EAAW9C,EAASmB,EAASrB,GAAaP,EAAWO,IAKrDmC,QAAY/D,EAAcE,GAAQ,CAEtCrB,MAAO+F,EAAS/F,MAEhBZ,QAASwG,EACT5J,SAAAA,EACA6F,OAAQyD,EAAUvC,EAAYzC,EAASkC,GACvClC,QAASA,IAIL0F,OACDL,EAFepM,EAAS2L,GAAOA,EAAM,IAMpCV,EAAgBJ,EAASrB,GAC/B,GAAI8C,GAAYrB,EAAezB,GAE7B+C,EAAc,CACZvG,KAAMiF,EACNnD,OAAAA,EACArF,SAAAA,EACA+G,WAAAA,EACA5G,MAAAA,QAEG,CACL,MAAM8J,EAAkB,GAAE5E,KAAU0B,KACfkD,EAAe7I,MAAM,OAAS,IAAIzD,OACrC,IAAM0H,EAAOjE,MAAMgF,KAAoBf,EAAOjE,MAAMiF,IAGpErG,EAAS3C,cAFeqJ,EAAgBsD,EAASJ,GAI/CpM,KAAMyM,EACNlG,EAAG,CACDgC,OAAQkE,EACRjE,KAAM,gBAMd,OAAOtC,QAAQC,QAAQqG,IACtBtG,QAAQC,QAAQrG,IAGnB,KAAK+H,EAAOjE,MAAM+E,IACbd,EAAOjE,MAAM,oBAGbiE,EAAOjE,MAAMiF,IACbhB,EAAOjE,MAAMgF,IACbf,EAAOjE,MAAM,YACbiE,EAAOjE,MAAM,mBAChB,CASA,GARIhB,EAAOkE,QAAQmD,SAASpC,GAQxBmE,EAAezF,GAAKyF,EAAezF,EAAEmG,iBAAmB7E,EAE1D,OAAOmE,EAGT,IAAIW,OACCX,EACA,CACDzF,EAAG,CACDmG,eAAgBV,EAAehM,KAC/BuI,OAAQyD,EAAehM,KACvBwI,KAAM,eAMRoE,GAAeZ,EAAgBjG,EAAKqD,MAAMjJ,UAAY0H,EAAOjE,MAAM,UACrE+I,OACKA,EACA,CACD3M,KAAMgM,EAAehM,KAAO,aAKlC2C,EAAM9C,SAAS8M,GAGjB,OAAOX,EAGT,SAASM,GAAcvG,KAAEA,EAAF8B,OAAQA,EAAR0B,WAA0BA,EAA1B5G,MAAsCA,IAC3D,MACMkK,EAAahF,EAAS,WADX0B,EAAc,IAAMA,EAAa,IAElD5G,EAAM9C,cACDkG,GACH/F,KAAM6M,EACNtG,EAAG,CACDgC,OAAQsE,EACRrE,KAAM,YAKZ,SAASsD,EAAU5J,EAAM4K,EAAa9D,GACpC,MAAM+D,EAAaD,EAAY5K,IAAS8G,EAAW9G,GACnD,OAAI6K,GAAcA,EAAW1E,OACpB0E,EAAW1E,OAEb,GAGT,SAAS2E,EAAmBtC,EAAY5D,GACtC,OAAOA,EAAQ3F,OAAO,CAAC8L,EAAK7F,IACjBA,EAAOsD,GAAqBuC,EAAIC,OAAO,CAC9CxC,WAAYA,EACZnB,WAAYnC,EAAOlF,KACnB2F,OAAQT,EAAOsD,KAHcuC,EAK9B,IAaL,SAASE,EAAcC,EAAWC,GAChC,MAAMxF,EAAsBuF,EAVhBtJ,QAAQ6E,EAAoB,IAWlC2E,EAAWD,EAAc,IAAGA,IAAc,GAOhD,MAAO,CALO,GAAED,IAAYE,IAER,GAAEzF,IAASyF,IAElB,GAAEzF,OAAYyF,KAyC7B,SAASjB,IAAY7F,MAAEA,GAAS+C,GAC9B,QAAK/C,KACS,IAAVA,GACGyD,GAASzD,EAAO+C,IAAgB/C,GAASyD,GAASzD,EAAMM,QAASyC,IAG1E,SAASqD,IAAepG,MAAEA,GAAS+G,GACjC,IAAK/G,EAAO,SACZ,IAAc,IAAVA,GAAkBgH,EAAShH,GAAQ,SACvC,MAAMM,QAAEA,GAAYN,EACpB,OAAQwD,GAAQxD,IAAWA,EAAMrG,SAAWoN,GAAmBvD,GAAQlD,IAAaA,EAAQ3G,SAAWoN,EAGzG,SAASvD,GAAQiD,GACf,OAAOQ,MAAMzD,QAAQiD,GAGvB,SAAShD,GAASgD,EAAK/K,GACrB,SAAK+K,IAAQjD,GAAQiD,KACdA,EAAIhD,SAAS/H,GA8EtB,SAASoJ,GAAoB1B,GAC3B,MAAM8D,EAAQ9D,EAAMhG,MAAM,aAC1B,QAAK8J,GAGE,CACL7F,OAAQ6F,EAAM,GACdxL,KAAMwL,EAAM,IAIhB,SAASpD,GAAcxK,GACrB,OAAOyD,OAAOC,KAAK1D,GAAQqB,OAAO,CAACY,EAAKjB,KAE1B,SAARA,IAIFiB,EAAIjB,GADFf,EAASD,EAAOgB,IACPyC,OAAOkB,OAAO,GAAI3E,EAAOgB,IAEzBhB,EAAOgB,IALXiB,GAQR,aCjnBmB4L,GAAiBnL,EAAUmE,EAAYiH,GAC7D,MAAMC,EAAU,GAChB,OAAOlL,GAAShC,GAAQmI,MAAAA,IACtB,MAAM9I,KAAEA,EAAFwG,MAAQA,EAARM,QAAeA,GAAYhH,EACjC,IAAIgO,EAAgBhO,EAEpB,GAAI0G,EACF,OAAO7F,EAAKb,GAuCd,GAnCIE,IAAS4C,EAAOmL,cAClBpL,EAAM9C,SAAS,CACbG,KAAM4C,EAAOoL,gBACblH,QAASA,EACTmH,SAAU,GACVC,YAAY,EACZ/F,KAAMrI,EAAOqI,OAIbnI,IAAS4C,EAAOuL,eAElBjK,WAAW,IAAMwB,EAAY5F,EAAOqI,KAAKC,IAAK,CAAExC,QAAS9F,IAAW,GAuBlEE,IAAS4C,EAAOwL,cAAe,CACjC,MAAMpF,EAAarC,IACb0H,EAAe9K,OAAOC,KAAKwF,GAC3BsF,EAAuBD,EAAa3F,OAAQxG,GACzC4E,EAAQmD,SAAS/H,IACvBmH,IAAKnH,GACC8G,EAAW9G,IAEpB,IAAIqM,EAAY,GACZC,EAAS,GACTP,EAAWnO,EAAOmO,SAEtB,MAAMQ,EAAuBH,EAAqBjF,IAAKjC,IACrD,MAAMC,OAAEA,EAAFnF,KAAUA,EAAVmG,OAAgBA,GAAWjB,EAGjC,OAAOtB,EAAasB,EAFH,IAAMC,EAAO,CAAEgB,OAAAA,IAEM,KAAK/B,KAAMoI,IAC1Cb,EAAQ3L,KAEXS,EAAM9C,SAAS,CACbG,KAAM4C,EAAOT,gBAAgBD,GAC7BA,KAAMA,EACNyM,OAAQpL,OAAOC,KAAK4D,GAAQsB,OAAQxG,IAC1BJ,EAAUmI,SAAS/H,MAG/B2L,EAAQ3L,IAAQ,GAElBqM,EAAYA,EAAUrB,OAAOhL,GAEtBkF,IAENwH,MAAOC,IAGR,GAAIA,aAAahQ,MACf,UAAUA,MAAMgQ,GAIlB,OAFAL,EAASA,EAAOtB,OAAO2B,EAAE3M,MAElB2M,MAIX3I,QAAQ4I,IAAIL,GAAsBnI,KAAMyI,IAEtC,MAAMnJ,EAAU,CACdkB,QAASyH,EACTC,OAAQA,EACRP,SAAUA,GAEZ/J,WAAW,KACLmK,EAAalO,SAAYsO,EAAqBtO,OAAS8N,EAAS9N,QAClEwC,EAAM9C,cACD,CAAEG,KAAM4C,EAAOoM,OACfpJ,KAIN,KAKP,GAAI5F,IAAS4C,EAAOC,UAAW,CACzB,kBAAkBoM,KAAKjP,IAEzBkE,WAAW,IAAMwC,EAAa/D,EAAOgE,EAAYnE,GAAW,GAE9D,MAAM0M,uBDhHmBpP,EAAQ6G,EAAYnE,EAAUG,EAAOwM,GAClE,MAAMC,EAAevJ,EAAWc,GAAcA,IAAeA,EACvD0I,EAAevP,EAAOE,KACtBsP,EAAcD,EAAavL,QAAQ6E,EAAoB,IAG7D,GAAI7I,EAAOyG,GAAKzG,EAAOyG,EAAEgC,OAEvB,OAAOzI,EAGT,MAAMwE,EAAQ9B,EAASnD,WAEvB,IAAIkQ,WEpBwCvG,EAAYwG,EAAW,GAAIvK,EAAU,IACjF,OAAO1B,OAAOC,KAAKwF,GAAYN,OAAQxG,IACrC,MAAMuN,EAAkBxK,EAAQ6B,SAAW,GAE3C,OAAI4I,EAAUD,EAAgBvN,IACrBuN,EAAgBvN,IAGG,IAAxBuN,EAAgBX,OAIhBU,EAAStN,KAAoC,IAA3BsN,EAAStN,GAAMyN,WAIpCtG,IAAKnH,GAAS8G,EAAW9G,IFIR0N,CAAsBR,EAAc9K,EAAMwC,QAAShH,EAAOmF,SAG1EoK,IAAiBzM,EAAOoL,iBAAmBlO,EAAOoO,aAEpDqB,EAAgBhM,OAAOC,KAAKc,EAAMwC,SAAS4B,OAAQxG,IACjD,MAAM2N,EAAOvL,EAAMwC,QAAQ5E,GAC3B,OAAOpC,EAAOgH,QAAQmD,SAAS/H,KAAU2N,EAAKC,cAC7CzG,IAAKnH,GAASkN,EAAalN,KAIhC,MAAM6N,EAAsBR,EAAclG,IAAK4B,GAAMA,EAAE/I,MAEjD+G,EAubR,SAA6BmE,EAAWmC,EAAevG,GACrD,MAGMgH,EAHa7C,EAAcC,GAGT/D,IAAK4G,GACpBjD,EAAmBiD,EAAMV,IAGlC,OAAOA,EAAcpO,OAAO,CAACY,EAAKqF,KAChC,MAAMlF,KAAEA,GAASkF,EACX8I,EAAmB/C,EAAcC,EAAWlL,IAE1CiO,EAAaC,EAAaC,GAAeH,EAAiB7G,IAAK4G,GAC9DjD,EAAmBiD,EAAMV,IAYlC,OATIY,EAAYhQ,SACd4B,EAAIuO,SAASpO,GAAQiO,GAEnBC,EAAYjQ,SACd4B,EAAIwO,SAASrO,GAAQkO,GAEnBC,EAAWlQ,SACb4B,EAAIyO,QAAQtO,GAAQmO,GAEftO,GACN,CACD0O,OAAQT,EAAK,GACbM,SAAU,GACV9G,OAAQwG,EAAK,GACbO,SAAU,GACVG,MAAOV,EAAK,GACZQ,QAAS,KAvdQG,CAAoBtB,EAAcE,GAU/CqB,QAAqB7H,EAAa,CACtCjJ,OAAQA,EACRiG,KAAM,CACJqD,MAAOH,EAAWwH,OAClB1F,WAAY9B,EAAWqH,UAEzBhM,MAAOA,EACP0E,WAAYoG,EACZnG,WAAAA,EACAzG,SAAAA,EACAG,MAAAA,EACAC,OAAQuM,IAKV,GAAIvC,GAAegE,EAAcb,EAAoB5P,QACnD,OAAOyQ,EAUT,IAAIC,EAkCJ,GA/BEA,EAFExB,IAAiBC,EAEJsB,QAMM7H,EAAa,CAChCjJ,YACK8Q,GACH5Q,KAAMsP,IAERvJ,KAAM,CACJqD,MAAOH,EAAWO,OAClBuB,WAAY9B,EAAWsH,UAEzBjM,MAAOA,EACP0E,WAAYoG,EACZnG,WAAAA,EACAzG,SAAAA,EACAG,MAAAA,EACAC,OAAQuM,IAWRE,EAAazL,MAAM+E,GAAqB,CAC1C,MAAMmI,EAAa,GAAExB,OACfyB,QAAoBhI,EAAa,CACrCjJ,YACK+Q,GACH7Q,KAAM8Q,IAER/K,KAAM,CACJqD,MAAOH,EAAWyH,MAClB3F,WAAY9B,EAAWuH,SAEzBlM,MAAOA,EACP0E,WAAYoG,EACZnG,WAAAA,EACAzG,SAAAA,EACAG,MAAAA,EACAC,OAAQuM,IAKN4B,EAAY5I,MAAQ4I,EAAY5I,KAAK6I,aAQvCtL,EAAYqL,EAAY5I,KAAKC,IAAK,CAAExC,QAASmL,IAIjD,OAAOH,ECnBmBK,CAAWnR,EAAQ6G,EAAYnE,EAAUG,EAAOiL,GACtE,OAAOjN,EAAKuO,GAGd,OAAOvO,EAAKmN,aE3HQoD,GAAkBxO,GACxC,OAAOC,GAAShC,GAAQb,IACtB,MAAME,KAAEA,EAAFc,IAAQA,EAAR0D,MAAaA,EAAbS,QAAoBA,GAAYnF,EACtC,GAAIE,IAAS4C,EAAOH,SAAWzC,IAAS4C,EAAOgC,WAAY,CACzD,GAAI9E,EAAO0G,MACT,OAAO7F,EAAKb,GAGVE,IAAS4C,EAAOH,QAClBC,EAAQD,QAAQ3B,EAAK0D,EAAOS,GAE5BvC,EAAQkC,WAAW9D,EAAKmE,GAG5B,OAAOtE,EAAKb,UCZKqR,sBACnBV,OAAS,QACTC,MAAQ,QACRU,cAAgB,CAACC,EAAaC,KAC5BC,KAAKD,GAAYC,KAAKD,GAAUpE,OAAOmE,SAEzCG,iBAAmB,CAACC,EAAYH,KAC9B,MAAM5R,EAAQ6R,KAAKD,GAAUI,UAAUhD,GAAKA,IAAM+C,IACnC,IAAX/R,IAEJ6R,KAAKD,GAAY,IACZC,KAAKD,GAAUlS,MAAM,EAAGM,MACxB6R,KAAKD,GAAUlS,MAAMM,EAAQ,WAapCiS,mBAAsBL,GACb3O,GAAShC,GAAQb,IACtB,MAAM8R,EAAgB,CACpBvS,SAAUsD,EAAMtD,SAChBQ,SAAWgS,GAAQlP,EAAM9C,SAASgS,IAGpC,OAAO7Q,KADOuQ,KAAKD,GAAUjI,IAAIoI,GAAcA,EAAWG,IACnD5Q,CAAkBL,EAAlBK,CAAwBlB,cCjCbgS,GAAcnL,GACpC,gBAAwBrC,EAAQ,GAAIxE,GAClC,IAAIiS,EAAW,GACf,GAAoB,uBAAhBjS,EAAOE,KACT,OAAOsE,EAET,GAAI,2BAA2B2K,KAAKnP,EAAOE,MAAO,CAChD,MAAMkC,EAAO8P,GAAqBlS,EAAOE,KAAM,kBACzCoH,EAAST,IAAazE,GAC5B,IAAKkF,IAAWlF,EACd,OAAOoC,EAET,MAAM2N,EAAYnS,EAAO6P,QACnBtH,EAASjB,EAAOiB,OAStB,OARA0J,EAAS7P,GAAQ,CACfyN,QAASsC,EAETnC,cAAcmC,GAAaC,SAAS9K,EAAO+K,YAE3C9K,SAAS4K,GAAaC,QAAQ9K,EAAOC,OAAO,CAAEgB,OAAAA,KAC9CA,OAAAA,QAEU/D,EAAUyN,GAExB,GAAI,uBAAuB9C,KAAKnP,EAAOE,MAAO,CAC5C,MAAMkC,EAAO8P,GAAqBlS,EAAOE,KAAM4C,EAAOuP,YAChD/K,EAAST,IAAazE,GAC5B,OAAKkF,GAAWlF,GAIhB6P,EAAS7P,QACJoC,EAAMpC,GACN,CACD4N,aAAa,EAEbzI,OAAQ6K,QAAQ9K,EAAOC,OAAO,CAAEgB,OANrBjB,EAAOiB,iBASV/D,EAAUyN,IAXbzN,EAaX,GAAI,kBAAkB2K,KAAKnP,EAAOE,MAMhC,OAJA+R,EAASjS,EAAOoC,WACXoC,EAAMxE,EAAOoC,MACb,CAAEmF,QAAQ,SAEH/C,EAAUyN,GAExB,OAAQjS,EAAOE,MAUb,KAAK4C,EAAOuL,cACV,YACK7J,EACA8N,GAAmBtS,EAAOgH,SAAS,EAAOxC,IAGjD,KAAK1B,EAAOmL,aACV,YACKzJ,EACA8N,GAAmBtS,EAAOgH,SAAS,EAAMxC,IAEhD,QACE,OAAOA,IAKf,SAAS0N,GAAqBhS,EAAMqS,GAClC,OAAOrS,EAAKsS,UAAUD,EAASlS,OAAS,EAAGH,EAAKG,QAGlD,SAASiS,GAAmBtL,EAASyL,EAAQxT,GAC3C,OAAO+H,EAAQ3F,OAAO,CAACY,EAAKyQ,KAC1BzQ,EAAIyQ,QACCzT,EAAayT,GACb,CACD7C,QAAS4C,IAGNxQ,GACNhD,YC5FmB0T,GAAUC,GAChC,IACC,OAAOxI,KAAKyI,MAAMzI,KAAKC,UAAUuI,IAChC,MAAOE,IACT,OAAOF,ECCT,MAAMG,GAAe,CACnBC,KAAM,GACNC,QAAS,aAIaC,GAAa1O,EAAQuO,GAAc/S,GACzD,MAAME,KAAEA,EAAF4J,MAAQA,EAARqJ,WAAeA,EAAfhO,QAA2BA,EAA3BkD,KAAoCA,GAASrI,EAEnD,GAAQE,IACD4C,EAAOwB,MAAZ,CACE,MAAM8O,EAAaT,MACjB7I,MAAAA,EACAqJ,WAAAA,GACI1P,OAAOC,KAAKyB,GAAS9E,QAAW,CAAE8E,QAASA,IAC/CkD,KAAAA,KAEF,YACK7D,EACA,CACDwO,KAAMI,EAENH,QAASzO,EAAMyO,QAAQ7F,OAAOgG,KAIlC,OAAO5O,ECxBb,MAAMuO,GAAe,CACnB5L,QAAS,aAGakM,GAAa7O,EAAQuO,GAAc/S,GACzD,MAAME,KAAEA,EAAF4F,QAAQA,GAAY9F,EAE1B,OAAQE,GACN,IAAK,QACH,IAAIoT,EAOJ,OAJEA,EADExN,GAAWA,EAAQ5F,MAAQ4F,EAAQ5F,OAAS4C,EAAOuB,SACvC,CAACrE,GAAQoN,OAAO5I,EAAM2C,SAEtB3C,EAAM2C,QAAQiG,OAAOpN,QAGhCwE,GACH2C,QAASmM,IAEb,IAAK,UACH,MAAO,GAET,QACE,OAAO9O,GCzBb,MAAM+O,GAAY,OAYlB,SAASC,GAAQC,GACf,MACMC,EADQ,oCACQC,KAAKF,GAE3B,MAAO,KADYC,GAAWA,EAAQ,GAAMA,EAAQ,GAAG9F,MAAM,KAAK,GAAG5J,QAAQuP,GAAW,IAAM,UAgCnFK,GAAc,CAACC,EAAW,MACrC,IAAKC,EAAW,OAAOD,EACvB,MAAME,MAAEA,EAAFC,SAASA,GAAaC,UACtBC,SAAEA,EAAFC,WAAYA,EAAZC,YAAwBA,GAAgBC,QACxCC,KAAEA,EAAFC,OAAQA,GAAWL,EACnBT,EA5BR,SAAoBc,GAClB,MAAMC,EAvBR,WACE,IAAKV,EAAW,OAChB,MAAMW,EAAOR,SAASS,qBAAqB,QAC3C,IAAK,IAAWC,EAAPvU,EAAI,EAAQuU,EAAMF,EAAKrU,GAAIA,IAClC,GAAgC,cAA5BuU,EAAIC,aAAa,OACnB,OAAOD,EAAIC,aAAa,QAkBVC,GAClB,OAAKL,EACEA,EAAU1Q,MAAM,MAAQ0Q,EAAYA,EAAYD,EADhCF,OAAOH,SAASY,KAAK9Q,QAAQuP,GAAW,IA0BnDwB,CAAWR,GACjBS,EAAO,CACXjB,MAAOA,EACPN,IAAKA,EACLwB,KAAMzB,GAAQC,GACda,KAAMA,EACNC,OAAQA,EACRW,MAAOf,EACPgB,OAAQf,GAMV,OAJIJ,GAAyB,KAAbA,IACdgB,EAAKhB,SAAWA,QAIbgB,EAEAnB,IAIDd,GAAe,CACnBC,KAAM,GACNC,QAAS,aAIa+B,GAAKxQ,EAAQuO,GAAc/S,GACjD,MAAMmT,WAAEA,EAAFhO,QAAcA,EAAdkD,KAAuBA,GAASrI,EACtC,GAAQA,EAAOE,OACR4C,EAAOkS,KAAZ,CACE,MAAMI,EAAWzC,MACfQ,WAAAA,EACA9K,KAAAA,GACI5E,OAAOC,KAAKyB,GAAS9E,QAAW,CAAE8E,QAASA,KAEjD,YACKX,EACA,CACDwO,KAAMoC,EAENnC,QAASzO,EAAMyO,QAAQ7F,OAAOgI,KAIlC,OAAO5Q,EC/Fb,IAAI6Q,GACArB,GASFqB,GCdO,KDePrB,GAAW,GAGb,MAAMjB,GAAe,CACnB/C,aAAa,EACbsF,UAAWjQ,IACXkQ,IAAK,KACLC,QAAS,KACTC,OAAO,EACPvO,UAAU4M,IAAc4B,UAAUC,OAClCC,GAAI,CACFxT,KC1BK,MD4BPyT,UAAY/B,EAAa4B,UAAUG,UAAY,OAC/CC,QAAS,CACP1T,KlBjCoB,YkBmCpBoT,QApCJ,WAsCEO,cA3BEC,EA4BFC,YA7BEA,EA8BFhS,SAAU,GACV+P,SAAUA,aAIY/M,GAAQzC,EAAQuO,GAAc/S,GACpD,MAAMgQ,YAAEA,GAAgBxL,GAClBtE,KAAEA,EAAF+D,SAAQA,GAAajE,EAC3B,OAAQE,GACN,KAAK4C,EAAOmB,SACV,YACKO,EACA,CAAEP,SAAUA,IAEnB,KAAKnB,EAAOoE,QACV,YACK1C,EACA,CAAE0C,SAAS,IAElB,KAAKpE,EAAOoT,OACV,YACK1R,EACA,CAAE0C,SAAS,IAElB,QACE,OAAK8I,EAOExL,OALAuO,GACAvO,EACA,CAAEwL,aAAa,KAO5B,MAAMmG,GAAe,CAAC,UAAW,WAAY,oBExE7BC,KAId,OAFAC,EpBLsB,YoBKR,IAEN3X,GACC,CAACC,EAASC,EAAgBC,KAC/B,MAAMgE,EAAQnE,EAAYC,EAASC,EAAgBC,GAC7CyX,EAAezT,EAAM9C,SAM3B,OAAO0D,OAAOkB,OAAO9B,EAAO,CAAE9C,SALZC,IAEhBuW,EAAcC,GAAd,UAA6B7W,KADnBK,EAAOA,QAAUA,GAEpBsW,EAAatW,gBAOZyW,GAAiBlO,GAC/B,kBACE,OAAOrH,EAAQA,EAAQwV,MAAM,KAAMC,WAAYP,gBCtB3BQ,GAAYC,GAClC,OAAKA,EACD3M,EAAQ2M,GAAuBA,EAC5B,CAACA,GAFmB,YCULC,GAAazO,EAAO,GAAIhC,EAAS0Q,GACrD,MAAMzO,EAAMjD,IARhB,IAAiC2R,EAAUC,EAavC,OAJI5Q,IAEFV,EAAM2C,IAXqB0O,EAWU3Q,EAXA4Q,WCCCC,GAC1C,MAAM1V,EAAO0V,GAAYvJ,MAAMwJ,UAAU7X,MAAM8X,KAAKT,WACpD,IAAIU,EACJ,IAAK,IAAIjX,EAAI,EAAGA,EAAIoB,EAAKnB,OAAQD,IAC/B,GAAI2F,EAAWvE,EAAKpB,IAAK,CACvBiX,EAAK7V,EAAKpB,GAAI,MAGlB,OAAOiX,EDE2CC,CAAYP,GAVtD9Q,IACFgR,GAAUA,EAAShR,GACvB+Q,EAAS/Q,WAWJoC,GACHC,IAAKA,EACLiP,QEpBOC,MAAOC,WFqBTpR,EAAgB,CAAE6K,aAAa,GAApB,IG8BtB,SAASwG,GAAUnP,EAAS,IAC1B,MAAMoP,EAAiBpP,EAAOqP,UAAY,GACpCzU,EAAcoF,EAAOpF,aAAe,GAUpC0U,GAAiBtP,EAAOvB,SAAW,IAAI3F,OAAO,CAACY,EAAKqF,KACxD,GAAIvB,EAAWuB,GAGb,OADArF,EAAIsP,YAActP,EAAIsP,YAAYnE,OAAO9F,GAClCrF,EAIT,GADIqF,EAAOwQ,YAAWxQ,EAAOlF,KAAOkF,EAAOwQ,YACtCxQ,EAAOlF,KAEV,UAAUrD,MAAMgZ,+BAGbzQ,EAAOiB,SAAQjB,EAAOiB,OAAS,IAEpC,MAAMyP,EAAiB1Q,EAAOxE,OAAUW,OAAOC,KAAK4D,EAAOxE,QAAQyG,IAAK0O,GAC/D3Q,EAAOxE,OAAOmV,IAClB,GAKLhW,EAAIiW,cAAc5Q,EAAOlF,SAHqB,IAAnBkF,EAAOuI,UAC0B,IAA1BvI,EAAOiB,OAAOsH,gBAGzCvI,EAAOuI,QAEVvI,EAAO6Q,UACTlW,EAAIkW,QAAQ7Q,EAAOlF,MAAQqB,OAAOC,KAAK4D,EAAO6Q,SAAS9W,OAAO,CAACC,EAAG8W,KA83BtE,IAAyBC,EA33BnB,OADA/W,EAAE8W,IA43BiBC,EA53BI/Q,EAAO6Q,QAAQC,cA+3BxC,MAAM5W,EAAOmM,MAAMwJ,UAAU7X,MAAM8X,KAAKT,WAExC,IAAI2B,EAAU,IAAI3K,MAAM0K,EAAGhY,QAC3B,IAAK,IAAID,EAAI,EAAGA,EAAIoB,EAAKnB,OAAQD,IAC/BkY,EAAQlY,GAAKoB,EAAKpB,GAKpB,OAFAkY,EAAQA,EAAQjY,QAAUqC,EAEnB2V,EAAG3B,MAAM,CAAEhU,SAAAA,GAAY4V,KAv4BrBhX,GACN,WAEIgG,EAAO6Q,SAGhB,MAEMI,EAFkB9U,OAAOC,KAAK4D,GAEF8F,OAAO4K,GAEnCQ,EAAkB,IAAIC,IAAIxW,EAAI4M,OAAOzB,OAAOmL,IAKlD,GAJAtW,EAAI4M,OAASlB,MAAMjF,KAAK8P,GAExBvW,EAAIsM,aAAetM,EAAIsM,aAAanB,OAAO9F,GAEvCrF,EAAI+E,QAAQM,EAAOlF,MACrB,UAAUrD,MAAMuI,EAAOlF,KAAO,iBAOhC,OALAH,EAAI+E,QAAQM,EAAOlF,MAAQkF,EACtBrF,EAAI+E,QAAQM,EAAOlF,MAAMmF,SAE5BtF,EAAI+E,QAAQM,EAAOlF,MAAMmF,OAAS,KAAM,GAEnCtF,GACN,CACD+E,QAAS,GACTkR,cAAe,GACfC,QAAS,GACT5J,aAAc,GACdgD,YAAa,GACb1C,OAAQ,KAIJjM,EAAW2F,EAAO3F,QAAW2F,EAAO3F,QAAU,CAClDoC,QAAS0T,EACT/V,QAAS0T,EACTvR,WAAYM,GAGRuT,WtB5EwB/V,GAC9B,gBAA4B5B,EAAK0B,EAAUoD,GAGzC,OADkBpD,EAASnD,SAAS,QAAQyB,KASxC8E,GAAW7F,EAAS6F,IAAYA,EAAQ9E,GAInC8E,EAAQ9E,GAIK+D,EAAqBnC,GAAS5B,IAS7C0X,EAAIzT,EAAQjE,KAAS,OsB+CV4X,CAAgBhW,GAGpC,IAAIiW,EAAgBhB,EAAc7Q,QAGlC,MAAM8R,EAAkBjB,EAAchJ,OAAOjG,OAAQxG,IAC3CJ,EAAUmI,SAAS/H,IAC1B2W,OACGC,EAAe,IAAIP,IAAIK,EAAgB1L,OAAOrL,GAAY6G,OAAQxG,IAC9DJ,EAAUmI,SAAS/H,KAEvB6W,EAAkBtL,MAAMjF,KAAKsQ,GAAcD,OAG3ClS,EAAa,IAAMgS,GAEnBvH,cACJA,EADII,iBAEJA,EAFIG,mBAGJA,GACE,IAAIR,GAEF6H,EAAe,KAEnB,UAAUna,MAAM,8BAIZiE,EAASmW,IAETjW,EAAgB6B,EAAqBnC,GACrCwW,OACDlW,EACAC,EACEH,EAAOW,OAAe,CAAEN,OAAQL,EAAOW,QAAtB,GACjBX,EAAOqW,OAAe,CAAE/V,YAAaN,EAAOqW,QAA3B,IAGnBD,EAAY9V,cACf8V,EAAY9V,YAAc+B,KAkB5B,MAAM2B,KAkBJsS,OAAQ,CAACtS,EAASiQ,QACL7Q,QAASC,IAClBxD,GAAM9C,SAAS,CACbG,KAAM4C,EAAOmL,aACbjH,QAAS4P,GAAY5P,GACrBP,EAAG,CAAEmG,eAAgB9J,EAAOmL,eAC3B5H,EAAS,CAAE4Q,MAmBlBsC,QAAS,CAACvS,EAASiQ,QACN7Q,QAASC,IAClBxD,GAAM9C,SAAS,CACbG,KAAM4C,EAAOuL,cACbrH,QAAS4P,GAAY5P,GACrBP,EAAG,CAAEmG,eAAgB9J,EAAOuL,gBAC3BhI,EAAS,CAAC4Q,OAiCdY,EAAcM,SAGnB,IAAIqB,GAAc,EAgBlB,MAAM9W,EAAW,CA4Cf2B,SAAU2E,MAAO3F,EAAQE,EAAQ4B,EAAS8R,KACxC,MAAMpR,EAAK6H,EAASrK,GAAUA,EAAS,KACjC4C,EAAOhG,EAASoD,GAAUA,EAASE,EACnCkW,EAAOtU,GAAW,GAClBlC,EAAOP,EAASO,OAGtBoT,EAAIpR,EAAQpD,GAAKgE,GAEjB,MAAM6T,EAAa7T,GAAMI,EAAK5C,QAAUsV,EAAY9W,EAAIa,EAAUuD,GAElE,WAAWG,QAASC,IAClBxD,GAAM9C,YACJG,KAAM4C,EAAO6W,cACbtW,OAAQqW,EACRnW,OAAQ0C,GAAQ,GAChBd,QAASsU,EACTnW,YAAaL,EAAKK,aAEdL,EAAK4C,IAAO5C,EAAK4C,KAAOA,GAAO,CAAE+T,WAAY3W,EAAK4C,KACrDQ,EAAS,CAAC9C,EAAQ4B,EAAS8R,OAmDlC3S,MAAO0E,MAAO6Q,EAAW/T,EAASX,EAAS8R,KACzC,MAAM7U,EAAOnC,EAAS4Z,GAAaA,EAAU/P,MAAQ+P,EACrD,IAAKzX,IAASsL,EAAStL,GACrB,UAAUrD,MAAM,gBAElB,MAAMkH,EAAOhG,EAAS4Z,GAAaA,EAAa/T,GAAW,GACrD2T,EAAOxZ,EAASkF,GAAWA,EAAU,GAE3C,WAAWiB,QAASC,IAClBxD,GAAM9C,SAAS,CACbG,KAAM4C,EAAOgX,WACbhQ,MAAO1H,EACP+Q,WAAYlN,EACZd,QAASsU,EACTpW,OAAQsV,EAAY9W,EAAIa,EAAUoD,GAClCxC,YAAaqV,EAAY7W,EAAQY,EAAUoD,IAC1CO,EAAS,CAACP,EAASX,EAAS8R,OA4CnCjC,KAAMhM,MAAO/C,EAAMd,EAAS8R,KAC1B,MAAMrI,EAAI3O,EAASgG,GAAQA,EAAO,GAC5BwT,EAAOxZ,EAASkF,GAAWA,EAAU,GAU3C,WAAWiB,QAASC,IAClBxD,GAAM9C,SAAS,CACbG,KAAM4C,EAAOiX,UACb5G,WAAYS,GAAYhF,GACxBzJ,QAASsU,EACTpW,OAAQsV,EAAY9W,EAAIa,EAAUkM,GAClCtL,YAAaqV,EAAY7W,EAAQY,EAAUkM,IAC1CvI,EAAS,CAACJ,EAAMd,EAAS8R,OAoBhChU,KAAOjC,IACL,GAAIA,IAAQa,GAAc,OAARb,EAChB,OAAO2X,EAAY9W,EAAIa,GAEzB,GAAI1B,IAAQc,GAAkB,WAARd,EACpB,OAAO2X,EAAY7W,EAAQY,GAE7B,MAAMO,EAAOP,EAASnD,SAAS,QAC/B,OAAKyB,EACEgZ,EAAQ/W,EAAMjC,GADJiC,GAanB2B,MAAQqS,OACK7Q,QAASC,IAClBxD,GAAM9C,SAAS,CACbG,KAAM4C,EAAOmX,YACZ5T,EAAS4Q,KAehB/H,MAAQ+H,IAEFuC,GAAavC,EAAS,CAAEjQ,QAAAA,EAAStE,SAAAA,IAC9BA,EAASwX,GAAGpX,EAAOoM,MAAQ1F,IAChCyN,EAASzN,GACTgQ,GAAc,KAyBlBU,GAAI,CAAC9X,EAAM6U,KACT,IAAK7U,IAAS2D,EAAWkR,GACvB,SAEF,GAAI7U,IAASU,EAAOC,UAClB,UAAUhE,MAAM,oBAAsBqD,GAExC,MAAM+X,EAAa,gBACnB,GAAa,MAAT/X,EAAc,CAChB,MAAMgY,EAAgBvX,GAAShC,GAAQb,IACjCA,EAAOE,KAAK4D,MAAMqW,IACpBlD,EAAS,CACPnR,QAAS9F,EACT0C,SAAAA,EACAsE,QAAS6R,IAGNhY,EAAKb,IAERqa,EAAexX,GAAShC,GAAQb,IAC/BA,EAAOE,KAAK4D,MAAMqW,IACrBlD,EAAS,CACPnR,QAAS9F,EACT0C,SAAAA,EACAsE,QAAS6R,IAGNhY,EAAKb,IAQd,OANAsR,EAAc8I,EAAezJ,IAC7BW,EAAc+I,EAAczJ,IAKrB,KACLc,EAAiB0I,EAAezJ,IAChCe,EAAiB2I,EAAczJ,KAInC,MAAMY,EAAYpP,EAAK0B,MAAMqW,GAAexJ,GAASC,GAC/C0J,EAAUzX,GAAShC,GAAQb,IAE3BA,EAAOE,OAASkC,GAClB6U,EAAS,CACPnR,QAAS9F,EACT0C,SAAUA,EACVsE,QAAS6R,EACTnS,MAAOwS,IAQJrY,EAAKb,IAGd,OADAsR,EAAcgJ,EAAS9I,GAChB,IAAME,EAAiB4I,EAAS9I,IAwBzC+I,KAAM,CAACnY,EAAM6U,KACX,IAAK7U,IAAS2D,EAAWkR,GACvB,SAEF,GAAI7U,IAASU,EAAOC,UAClB,UAAUhE,MAAM,sBAAwBqD,GAE1C,MAAMoY,EAAiB9X,EAASwX,GAAG9X,EAAM,EAAG0D,QAAAA,MAC1CmR,EAAS,CACPnR,QAASA,EACTpD,SAAUA,EACVsE,QAAS6R,EACTnS,MAAOwS,IAGTsB,MAEF,OAAOA,GAgBTjb,SAAWyB,IACT,MAAMwD,EAAQ3B,GAAMtD,WACpB,OAAIyB,EAAYgZ,EAAQxV,EAAOxD,GACxByC,OAAOkB,OAAO,GAAIH,IAM3BzE,SAAWC,IACT,MAAMya,EAAa/M,EAAS1N,GAAU,CAAEE,KAAMF,GAAWA,EACzD,GxBtfG+B,EAAWoI,SwBsfOsQ,EAAWva,MAC9B,UAAUnB,MAAM,mBAAqB0b,EAAWva,MAElD,MAIMwa,OACDD,GACHhU,KACEmG,eAAgB6N,EAAWva,MAPdF,EAAOyG,GAAK,MAY7B5D,GAAM9C,SAAS2a,IAIjBzM,aAAcjH,EAAQsS,OAGtBjL,cAAerH,EAAQuS,QAGvBvS,QAASA,EAuBTpE,QAAS,CAYPoC,QAASpC,EAAQoC,QAYjBrC,QAAS,CAAC3B,EAAK0D,EAAOS,KACpBtC,GAAM9C,SAAS,CACbG,KAAM4C,EAAO6X,aACb3Z,IAAKA,EACL0D,MAAOA,EACPS,QAASA,KAabL,WAAY,CAAC9D,EAAKmE,KAChBtC,GAAM9C,SAAS,CACbG,KAAM4C,EAAO8X,gBACb5Z,IAAKA,EACLmE,QAASA,MAcf0V,eAAgB,CAACvX,EAAa6B,KAG5BzC,EAASE,QAAQD,QAAQmY,EAAmBxX,EAAa6B,IAM3D0J,OAAQ,CACNqB,KAAMnO,EACNiF,QAAS8R,IAUPvH,EAAcsG,EAActG,YAAYnE,OAAO,CAN5B2N,GAAYla,GAAQb,IACtCA,EAAOqI,OACVrI,EAAOqI,KAAO2S,MAETna,EAAKb,IAKZ6R,EAAmBlB,IAEnBgB,GAAmBjP,EAAUmE,EAAY,CACvCmI,IAAKiK,EACLjS,QAAS8R,IAEXnH,GAAmB/O,GACnB+O,EAAsBjP,GACtBiP,EAAoBjP,GAEpBmP,EAAmBjB,MAIfqK,EAAe,CACnBhU,QAASA,GACThE,KAAMA,EAAKL,GACXoS,KAAMA,GACN1Q,MAAOA,GACP0C,QAASkU,GAAkBrU,GAC3BN,MAAOA,IAGT,IAAI4U,EAAmBja,EACnBka,EAAyBla,EAC7B,GAAI4S,GAAavL,EAAOkN,MAAO,CAC7B,MAAM4F,EAAWhH,OAAOiH,qCACpBD,IACFF,EAAmBE,EAAS,CAAEE,OAAO,EAAMC,WAAY,MAEzDJ,EAAyB,WACvB,OAAyB,IAArBzE,UAAUtW,OAAqB+V,KAC/BnW,SAAgB0W,UAAU,IAAYF,KACnCA,KAAmBC,MAAM,KAAMC,YAI1C,MAAM8E,WP5xBoBlT,GAC1B,OAAO9E,OAAOC,KAAK6E,GAAQlH,OAAO,CAACY,EAAKyZ,KAClCvF,GAAahM,SAASuR,KAG1BzZ,EAAIyZ,GAAWnT,EAAOmT,IAFbzZ,GAIR,IOqxBmB0Z,CAAYpT,GAE5BqT,GAAoB/D,EAActJ,aAAalN,OAAO,CAACY,EAAKqF,KAChE,MAAMlF,KAAEA,EAAFmG,OAAQA,EAARhB,OAAgBA,GAAWD,EAC3B6K,EAAY0F,EAAcK,cAAc9V,GAQ9C,OAPAH,EAAIG,GAAQ,CACVyN,QAASsC,EAETnC,cAAcmC,GAAaC,SAAS9K,EAAO+K,YAC3C9K,OAAQ6K,QAAQ7K,EAAO,CAAEgB,OAAAA,KACzBA,OAAAA,GAEKtG,GACN,IAEG8Q,GAAe,CACnB9L,QAASwU,EACTxY,KAAMmW,EACNpS,QAAS4U,IAKL/Y,GAAQnE,W5BxzBwBkZ,GACtC,MAAMiE,EAAcpY,OAAOC,KAAKkU,GAC1BkE,EAAgB,GACtB,IAAK,IAAI1b,EAAI,EAAGA,EAAIyb,EAAYxb,OAAQD,IAAK,CAC3C,MAAMY,EAAM6a,EAAYzb,UAQbwX,EAAS5W,KAAShD,IAC3B8d,EAAc9a,GAAO4W,EAAS5W,IAGlC,MAAM+a,EAAmBtY,OAAOC,KAAKoY,GAOrC,IAAIE,EACJ,KArDF,SAA4BpE,GAC1BnU,OAAOC,KAAKkU,GAAU/S,QAAQ7D,IAC5B,MAAMrC,EAAUiZ,EAAS5W,GAEzB,UADqBrC,OAAQG,EAAW,CAAEoB,KF1CnBI,mBE4CGrC,UACjBU,OAAQG,EAAW,CAAEoB,KAAMhC,MAAmBD,EAErD,UAAUc,MAAMkd,WAAgBjb,EAAM,IAAM/C,KA8C9Cie,CAAmBJ,GACnB,MAAO/M,GACPiN,EAAsBjN,EAGxB,gBAA4BvK,EAAQ,GAAIxE,GACtC,GAAIgc,EACF,MAAMA,EAUR,IAAIG,GAAa,EACjB,MAAMC,EAAY,GAClB,IAAK,IAAIhc,EAAI,EAAGA,EAAI2b,EAAiB1b,OAAQD,IAAK,CAChD,MAAMY,EAAM+a,EAAiB3b,GAEvBic,EAAsB7X,EAAMxD,GAC5Bsb,GAAkB3d,EAFRmd,EAAc9a,IAEEqb,EAAqBrc,GACrD,UAAWsc,IAAoBre,EAAO,CACpC,MAAMse,EAAexb,EAA8BC,EAAKhB,GACxD,UAAUjB,MAAMwd,GAElBH,EAAUpb,GAAOsb,EACjBH,EAAaA,GAAcG,IAAoBD,EAEjD,OAAOF,EAAaC,EAAY5X,G4BkwBhCgY,MAAqBvB,EAAiBtD,IAEtC5E,GAEAqI,EACED,cCt3BqC5J,GACzC,OAAQ7S,GAAgB,CAACC,EAASC,EAAgBC,KAChD,MAAMgE,EAAQnE,EAAYC,EAASC,EAAgBC,GACnD,IAAIkB,EAAW8C,EAAM9C,SACjB0c,EAAQ,GAEZ,MAAM3K,EAAgB,CACpBvS,SAAUsD,EAAMtD,SAChBQ,SAAWC,GAAWD,EAASC,IAKjC,OAHAyc,EAAQlL,EAAYhI,IAAIoI,GAAcA,EAAWG,IACjD/R,EAAWmB,KAAWub,EAAXvb,CAAkB2B,EAAM9C,eAG9B8C,GACH9C,SAAAA,KDw2BE2c,IAAmBnL,MAMzB,IAAyB8G,GAazBxV,GAAM9C,UAbmBsY,GAaQxV,GAAM9C,kBAZpB+J,EAAOkN,EAAU2F,GAEhC,MAGMC,OAAgB9S,EAAU,CAAEzB,KAHrB2S,GAAWlR,EAAMzB,KAAM2O,EAAUJ,GAAY+F,MAK1D,OAAOtE,GAAG3B,MAAM,KAAM,CAAEkG,MAQ5B,MAAMC,GAAapZ,OAAOC,KAAKmV,GAG/BhW,GAAM9C,SAAS,CACbG,KAAM4C,EAAOC,UACbiE,QAAS6V,GACTtU,OAAQkT,EACRzY,OAAQA,EACRC,KAAMmW,EACNjW,YAAAA,EACAD,cAAAA,IAGF,MAAM4Z,GAAiBD,GAAWjU,OAAQxG,GAASyV,EAAcK,cAAc9V,IACzE2a,GAAkBF,GAAWjU,OAAQxG,IAAUyV,EAAcK,cAAc9V,IA6DjF,OA1DAS,GAAM9C,SAAS,CACbG,KAAM4C,EAAOka,gBACbhW,QAAS6V,GACThN,QAASgI,EAAcK,gBAIzBL,EAActJ,aAAahF,IAAI,CAACjC,EAAQlH,KACtC,MAAM2C,UAAEA,EAAFwF,OAAaA,EAAbnG,KAAqBA,GAASkF,EAChCvE,GAAagD,EAAWhD,IAC1BA,EAAU,CAAEL,SAAAA,EAAU6F,OAAAA,EAAQzC,QAASwB,IAGzCzE,GAAM9C,SAAS,CACbG,KAAM4C,EAAOX,mBAAmBC,GAChCA,KAAMA,EACNyN,QAASgI,EAAcK,cAAc9V,GACrCkF,OAAQA,IAINuQ,EAActJ,aAAalO,SAAYD,EAAI,GAC7CyC,GAAM9C,SAAS,CACbG,KAAM4C,EAAOoL,gBACblH,QAAS8V,GACT3O,SAAU4O,OAiCTra,EAIT,MAAMiO,GAAS,SACTC,GAAQ"}