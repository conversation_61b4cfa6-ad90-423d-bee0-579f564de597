var dt=Object.defineProperty;var pt=(t,e,s)=>e in t?dt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var $=(t,e,s)=>pt(t,typeof e!="symbol"?e+"":e,s);import{r as y}from"./index-n_C3wGZ6.js";const Gs={LOGO:"/logo.png",VIDEO:"/VIDEO.mp4"},Xs={BACKGROUND:-1},He={PORT:8e3,ADMIN_USERNAME:"admin"},ht=`http://0:0:0:0:${He.PORT}`,A={ADMIN_LOGIN:"/api/auth/adminlogin",ADMIN_VERIFY:"/api/auth/verify",REGISTER_USER:"/api/users/userdata",REGISTER_CREATOR:"/api/users/creatordata",GET_USERS:"/api/users/registereduserdata",GET_CREATORS:"/api/users/registeredcreatordata",USER_ANALYTICS:"/api/users/analytics",SUBMIT_NOT_INTERESTED:"/api/notint/notinteresteddata",SUBMIT_FEEDBACK:"/api/feedback/feedbackdata",GET_FEEDBACK:"/api/feedback/all",FEEDBACK_ANALYTICS:"/api/feedback/analytics",FEEDBACK_SUMMARY:"/api/feedback/summary",DOWNLOAD_DATA:"/api/data/downloaddata",EXPORT_JSON:"/api/data/export/json",GET_STATS:"/api/data/stats",HEALTH_CHECK:"/health"},fe={ADMIN_TOKEN:"lawvriksh_admin_token",USER_PREFERENCES:"lawvriksh_user_preferences",FORM_DRAFTS:"lawvriksh_form_drafts"},q={NETWORK_ERROR:"Network error. Please check your connection and try again.",VALIDATION_ERROR:"Please check your input and try again.",UNAUTHORIZED:"You are not authorized to access this resource.",SERVER_ERROR:"Server error. Please try again later.",UNKNOWN_ERROR:"An unexpected error occurred. Please try again."},te={REGISTRATION_SUCCESS:"Thank you for joining our waiting list! We'll be in touch soon.",FEEDBACK_SUCCESS:"Thank you for your valuable feedback! Your input helps us improve our platform.",NOT_INTERESTED_SUCCESS:"Thank you for your time and feedback."};function qe(t,e){return function(){return t.apply(e,arguments)}}const{toString:mt}=Object.prototype,{getPrototypeOf:Te}=Object,{iterator:re,toStringTag:ze}=Symbol,oe=(t=>e=>{const s=mt.call(e);return t[s]||(t[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),x=t=>(t=t.toLowerCase(),e=>oe(e)===t),ie=t=>e=>typeof e===t,{isArray:V}=Array,v=ie("undefined");function Et(t){return t!==null&&!v(t)&&t.constructor!==null&&!v(t.constructor)&&N(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}const Ke=x("ArrayBuffer");function gt(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&Ke(t.buffer),e}const yt=ie("string"),N=ie("function"),Ve=ie("number"),ae=t=>t!==null&&typeof t=="object",St=t=>t===!0||t===!1,Z=t=>{if(oe(t)!=="object")return!1;const e=Te(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(ze in t)&&!(re in t)},bt=x("Date"),Rt=x("File"),wt=x("Blob"),Tt=x("FileList"),At=t=>ae(t)&&N(t.pipe),Ot=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||N(t.append)&&((e=oe(t))==="formdata"||e==="object"&&N(t.toString)&&t.toString()==="[object FormData]"))},kt=x("URLSearchParams"),[Ct,Nt,_t,Dt]=["ReadableStream","Request","Response","Headers"].map(x),xt=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function G(t,e,{allOwnKeys:s=!1}={}){if(t===null||typeof t>"u")return;let n,r;if(typeof t!="object"&&(t=[t]),V(t))for(n=0,r=t.length;n<r;n++)e.call(null,t[n],n,t);else{const o=s?Object.getOwnPropertyNames(t):Object.keys(t),i=o.length;let c;for(n=0;n<i;n++)c=o[n],e.call(null,t[c],c,t)}}function Je(t,e){e=e.toLowerCase();const s=Object.keys(t);let n=s.length,r;for(;n-- >0;)if(r=s[n],e===r.toLowerCase())return r;return null}const M=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,$e=t=>!v(t)&&t!==M;function Ee(){const{caseless:t}=$e(this)&&this||{},e={},s=(n,r)=>{const o=t&&Je(e,r)||r;Z(e[o])&&Z(n)?e[o]=Ee(e[o],n):Z(n)?e[o]=Ee({},n):V(n)?e[o]=n.slice():e[o]=n};for(let n=0,r=arguments.length;n<r;n++)arguments[n]&&G(arguments[n],s);return e}const Ut=(t,e,s,{allOwnKeys:n}={})=>(G(e,(r,o)=>{s&&N(r)?t[o]=qe(r,s):t[o]=r},{allOwnKeys:n}),t),Ft=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),Lt=(t,e,s,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),s&&Object.assign(t.prototype,s)},It=(t,e,s,n)=>{let r,o,i;const c={};if(e=e||{},t==null)return e;do{for(r=Object.getOwnPropertyNames(t),o=r.length;o-- >0;)i=r[o],(!n||n(i,t,e))&&!c[i]&&(e[i]=t[i],c[i]=!0);t=s!==!1&&Te(t)}while(t&&(!s||s(t,e))&&t!==Object.prototype);return e},Pt=(t,e,s)=>{t=String(t),(s===void 0||s>t.length)&&(s=t.length),s-=e.length;const n=t.indexOf(e,s);return n!==-1&&n===s},Bt=t=>{if(!t)return null;if(V(t))return t;let e=t.length;if(!Ve(e))return null;const s=new Array(e);for(;e-- >0;)s[e]=t[e];return s},Mt=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&Te(Uint8Array)),jt=(t,e)=>{const n=(t&&t[re]).call(t);let r;for(;(r=n.next())&&!r.done;){const o=r.value;e.call(t,o[0],o[1])}},Ht=(t,e)=>{let s;const n=[];for(;(s=t.exec(e))!==null;)n.push(s);return n},qt=x("HTMLFormElement"),zt=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,n,r){return n.toUpperCase()+r}),Ce=(({hasOwnProperty:t})=>(e,s)=>t.call(e,s))(Object.prototype),Kt=x("RegExp"),We=(t,e)=>{const s=Object.getOwnPropertyDescriptors(t),n={};G(s,(r,o)=>{let i;(i=e(r,o,t))!==!1&&(n[o]=i||r)}),Object.defineProperties(t,n)},Vt=t=>{We(t,(e,s)=>{if(N(t)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const n=t[s];if(N(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},Jt=(t,e)=>{const s={},n=r=>{r.forEach(o=>{s[o]=!0})};return V(t)?n(t):n(String(t).split(e)),s},$t=()=>{},Wt=(t,e)=>t!=null&&Number.isFinite(t=+t)?t:e;function vt(t){return!!(t&&N(t.append)&&t[ze]==="FormData"&&t[re])}const Gt=t=>{const e=new Array(10),s=(n,r)=>{if(ae(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[r]=n;const o=V(n)?[]:{};return G(n,(i,c)=>{const f=s(i,r+1);!v(f)&&(o[c]=f)}),e[r]=void 0,o}}return n};return s(t,0)},Xt=x("AsyncFunction"),Yt=t=>t&&(ae(t)||N(t))&&N(t.then)&&N(t.catch),ve=((t,e)=>t?setImmediate:e?((s,n)=>(M.addEventListener("message",({source:r,data:o})=>{r===M&&o===s&&n.length&&n.shift()()},!1),r=>{n.push(r),M.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",N(M.postMessage)),Zt=typeof queueMicrotask<"u"?queueMicrotask.bind(M):typeof process<"u"&&process.nextTick||ve,Qt=t=>t!=null&&N(t[re]),a={isArray:V,isArrayBuffer:Ke,isBuffer:Et,isFormData:Ot,isArrayBufferView:gt,isString:yt,isNumber:Ve,isBoolean:St,isObject:ae,isPlainObject:Z,isReadableStream:Ct,isRequest:Nt,isResponse:_t,isHeaders:Dt,isUndefined:v,isDate:bt,isFile:Rt,isBlob:wt,isRegExp:Kt,isFunction:N,isStream:At,isURLSearchParams:kt,isTypedArray:Mt,isFileList:Tt,forEach:G,merge:Ee,extend:Ut,trim:xt,stripBOM:Ft,inherits:Lt,toFlatObject:It,kindOf:oe,kindOfTest:x,endsWith:Pt,toArray:Bt,forEachEntry:jt,matchAll:Ht,isHTMLForm:qt,hasOwnProperty:Ce,hasOwnProp:Ce,reduceDescriptors:We,freezeMethods:Vt,toObjectSet:Jt,toCamelCase:zt,noop:$t,toFiniteNumber:Wt,findKey:Je,global:M,isContextDefined:$e,isSpecCompliantForm:vt,toJSONObject:Gt,isAsyncFn:Xt,isThenable:Yt,setImmediate:ve,asap:Zt,isIterable:Qt};function E(t,e,s,n,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),s&&(this.config=s),n&&(this.request=n),r&&(this.response=r,this.status=r.status?r.status:null)}a.inherits(E,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Ge=E.prototype,Xe={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Xe[t]={value:t}});Object.defineProperties(E,Xe);Object.defineProperty(Ge,"isAxiosError",{value:!0});E.from=(t,e,s,n,r,o)=>{const i=Object.create(Ge);return a.toFlatObject(t,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),E.call(i,t.message,e,s,n,r),i.cause=t,i.name=t.name,o&&Object.assign(i,o),i};const es=null;function ge(t){return a.isPlainObject(t)||a.isArray(t)}function Ye(t){return a.endsWith(t,"[]")?t.slice(0,-2):t}function Ne(t,e,s){return t?t.concat(e).map(function(r,o){return r=Ye(r),!s&&o?"["+r+"]":r}).join(s?".":""):e}function ts(t){return a.isArray(t)&&!t.some(ge)}const ss=a.toFlatObject(a,{},null,function(e){return/^is[A-Z]/.test(e)});function ce(t,e,s){if(!a.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,s=a.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,m){return!a.isUndefined(m[g])});const n=s.metaTokens,r=s.visitor||l,o=s.dots,i=s.indexes,f=(s.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(e);if(!a.isFunction(r))throw new TypeError("visitor must be a function");function u(d){if(d===null)return"";if(a.isDate(d))return d.toISOString();if(a.isBoolean(d))return d.toString();if(!f&&a.isBlob(d))throw new E("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(d)||a.isTypedArray(d)?f&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function l(d,g,m){let b=d;if(d&&!m&&typeof d=="object"){if(a.endsWith(g,"{}"))g=n?g:g.slice(0,-2),d=JSON.stringify(d);else if(a.isArray(d)&&ts(d)||(a.isFileList(d)||a.endsWith(g,"[]"))&&(b=a.toArray(d)))return g=Ye(g),b.forEach(function(T,F){!(a.isUndefined(T)||T===null)&&e.append(i===!0?Ne([g],F,o):i===null?g:g+"[]",u(T))}),!1}return ge(d)?!0:(e.append(Ne(m,g,o),u(d)),!1)}const p=[],S=Object.assign(ss,{defaultVisitor:l,convertValue:u,isVisitable:ge});function h(d,g){if(!a.isUndefined(d)){if(p.indexOf(d)!==-1)throw Error("Circular reference detected in "+g.join("."));p.push(d),a.forEach(d,function(b,w){(!(a.isUndefined(b)||b===null)&&r.call(e,b,a.isString(w)?w.trim():w,g,S))===!0&&h(b,g?g.concat(w):[w])}),p.pop()}}if(!a.isObject(t))throw new TypeError("data must be an object");return h(t),e}function _e(t){const e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function Ae(t,e){this._pairs=[],t&&ce(t,this,e)}const Ze=Ae.prototype;Ze.append=function(e,s){this._pairs.push([e,s])};Ze.toString=function(e){const s=e?function(n){return e.call(this,n,_e)}:_e;return this._pairs.map(function(r){return s(r[0])+"="+s(r[1])},"").join("&")};function ns(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Qe(t,e,s){if(!e)return t;const n=s&&s.encode||ns;a.isFunction(s)&&(s={serialize:s});const r=s&&s.serialize;let o;if(r?o=r(e,s):o=a.isURLSearchParams(e)?e.toString():new Ae(e,s).toString(n),o){const i=t.indexOf("#");i!==-1&&(t=t.slice(0,i)),t+=(t.indexOf("?")===-1?"?":"&")+o}return t}class De{constructor(){this.handlers=[]}use(e,s,n){return this.handlers.push({fulfilled:e,rejected:s,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){a.forEach(this.handlers,function(n){n!==null&&e(n)})}}const et={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},rs=typeof URLSearchParams<"u"?URLSearchParams:Ae,os=typeof FormData<"u"?FormData:null,is=typeof Blob<"u"?Blob:null,as={isBrowser:!0,classes:{URLSearchParams:rs,FormData:os,Blob:is},protocols:["http","https","file","blob","url","data"]},Oe=typeof window<"u"&&typeof document<"u",ye=typeof navigator=="object"&&navigator||void 0,cs=Oe&&(!ye||["ReactNative","NativeScript","NS"].indexOf(ye.product)<0),ls=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",us=Oe&&window.location.href||"http://localhost",fs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Oe,hasStandardBrowserEnv:cs,hasStandardBrowserWebWorkerEnv:ls,navigator:ye,origin:us},Symbol.toStringTag,{value:"Module"})),k={...fs,...as};function ds(t,e){return ce(t,new k.classes.URLSearchParams,Object.assign({visitor:function(s,n,r,o){return k.isNode&&a.isBuffer(s)?(this.append(n,s.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},e))}function ps(t){return a.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function hs(t){const e={},s=Object.keys(t);let n;const r=s.length;let o;for(n=0;n<r;n++)o=s[n],e[o]=t[o];return e}function tt(t){function e(s,n,r,o){let i=s[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=s.length;return i=!i&&a.isArray(r)?r.length:i,f?(a.hasOwnProp(r,i)?r[i]=[r[i],n]:r[i]=n,!c):((!r[i]||!a.isObject(r[i]))&&(r[i]=[]),e(s,n,r[i],o)&&a.isArray(r[i])&&(r[i]=hs(r[i])),!c)}if(a.isFormData(t)&&a.isFunction(t.entries)){const s={};return a.forEachEntry(t,(n,r)=>{e(ps(n),r,s,0)}),s}return null}function ms(t,e,s){if(a.isString(t))try{return(e||JSON.parse)(t),a.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(s||JSON.stringify)(t)}const X={transitional:et,adapter:["xhr","http","fetch"],transformRequest:[function(e,s){const n=s.getContentType()||"",r=n.indexOf("application/json")>-1,o=a.isObject(e);if(o&&a.isHTMLForm(e)&&(e=new FormData(e)),a.isFormData(e))return r?JSON.stringify(tt(e)):e;if(a.isArrayBuffer(e)||a.isBuffer(e)||a.isStream(e)||a.isFile(e)||a.isBlob(e)||a.isReadableStream(e))return e;if(a.isArrayBufferView(e))return e.buffer;if(a.isURLSearchParams(e))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let c;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return ds(e,this.formSerializer).toString();if((c=a.isFileList(e))||n.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return ce(c?{"files[]":e}:e,f&&new f,this.formSerializer)}}return o||r?(s.setContentType("application/json",!1),ms(e)):e}],transformResponse:[function(e){const s=this.transitional||X.transitional,n=s&&s.forcedJSONParsing,r=this.responseType==="json";if(a.isResponse(e)||a.isReadableStream(e))return e;if(e&&a.isString(e)&&(n&&!this.responseType||r)){const i=!(s&&s.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(c){if(i)throw c.name==="SyntaxError"?E.from(c,E.ERR_BAD_RESPONSE,this,null,this.response):c}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:k.classes.FormData,Blob:k.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],t=>{X.headers[t]={}});const Es=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),gs=t=>{const e={};let s,n,r;return t&&t.split(`
`).forEach(function(i){r=i.indexOf(":"),s=i.substring(0,r).trim().toLowerCase(),n=i.substring(r+1).trim(),!(!s||e[s]&&Es[s])&&(s==="set-cookie"?e[s]?e[s].push(n):e[s]=[n]:e[s]=e[s]?e[s]+", "+n:n)}),e},xe=Symbol("internals");function W(t){return t&&String(t).trim().toLowerCase()}function Q(t){return t===!1||t==null?t:a.isArray(t)?t.map(Q):String(t)}function ys(t){const e=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=s.exec(t);)e[n[1]]=n[2];return e}const Ss=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function de(t,e,s,n,r){if(a.isFunction(n))return n.call(this,e,s);if(r&&(e=s),!!a.isString(e)){if(a.isString(n))return e.indexOf(n)!==-1;if(a.isRegExp(n))return n.test(e)}}function bs(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,s,n)=>s.toUpperCase()+n)}function Rs(t,e){const s=a.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+s,{value:function(r,o,i){return this[n].call(this,e,r,o,i)},configurable:!0})})}let _=class{constructor(e){e&&this.set(e)}set(e,s,n){const r=this;function o(c,f,u){const l=W(f);if(!l)throw new Error("header name must be a non-empty string");const p=a.findKey(r,l);(!p||r[p]===void 0||u===!0||u===void 0&&r[p]!==!1)&&(r[p||f]=Q(c))}const i=(c,f)=>a.forEach(c,(u,l)=>o(u,l,f));if(a.isPlainObject(e)||e instanceof this.constructor)i(e,s);else if(a.isString(e)&&(e=e.trim())&&!Ss(e))i(gs(e),s);else if(a.isObject(e)&&a.isIterable(e)){let c={},f,u;for(const l of e){if(!a.isArray(l))throw TypeError("Object iterator must return a key-value pair");c[u=l[0]]=(f=c[u])?a.isArray(f)?[...f,l[1]]:[f,l[1]]:l[1]}i(c,s)}else e!=null&&o(s,e,n);return this}get(e,s){if(e=W(e),e){const n=a.findKey(this,e);if(n){const r=this[n];if(!s)return r;if(s===!0)return ys(r);if(a.isFunction(s))return s.call(this,r,n);if(a.isRegExp(s))return s.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,s){if(e=W(e),e){const n=a.findKey(this,e);return!!(n&&this[n]!==void 0&&(!s||de(this,this[n],n,s)))}return!1}delete(e,s){const n=this;let r=!1;function o(i){if(i=W(i),i){const c=a.findKey(n,i);c&&(!s||de(n,n[c],c,s))&&(delete n[c],r=!0)}}return a.isArray(e)?e.forEach(o):o(e),r}clear(e){const s=Object.keys(this);let n=s.length,r=!1;for(;n--;){const o=s[n];(!e||de(this,this[o],o,e,!0))&&(delete this[o],r=!0)}return r}normalize(e){const s=this,n={};return a.forEach(this,(r,o)=>{const i=a.findKey(n,o);if(i){s[i]=Q(r),delete s[o];return}const c=e?bs(o):String(o).trim();c!==o&&delete s[o],s[c]=Q(r),n[c]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const s=Object.create(null);return a.forEach(this,(n,r)=>{n!=null&&n!==!1&&(s[r]=e&&a.isArray(n)?n.join(", "):n)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,s])=>e+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...s){const n=new this(e);return s.forEach(r=>n.set(r)),n}static accessor(e){const n=(this[xe]=this[xe]={accessors:{}}).accessors,r=this.prototype;function o(i){const c=W(i);n[c]||(Rs(r,i),n[c]=!0)}return a.isArray(e)?e.forEach(o):o(e),this}};_.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(_.prototype,({value:t},e)=>{let s=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[s]=n}}});a.freezeMethods(_);function pe(t,e){const s=this||X,n=e||s,r=_.from(n.headers);let o=n.data;return a.forEach(t,function(c){o=c.call(s,o,r.normalize(),e?e.status:void 0)}),r.normalize(),o}function st(t){return!!(t&&t.__CANCEL__)}function J(t,e,s){E.call(this,t??"canceled",E.ERR_CANCELED,e,s),this.name="CanceledError"}a.inherits(J,E,{__CANCEL__:!0});function nt(t,e,s){const n=s.config.validateStatus;!s.status||!n||n(s.status)?t(s):e(new E("Request failed with status code "+s.status,[E.ERR_BAD_REQUEST,E.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function ws(t){const e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function Ts(t,e){t=t||10;const s=new Array(t),n=new Array(t);let r=0,o=0,i;return e=e!==void 0?e:1e3,function(f){const u=Date.now(),l=n[o];i||(i=u),s[r]=f,n[r]=u;let p=o,S=0;for(;p!==r;)S+=s[p++],p=p%t;if(r=(r+1)%t,r===o&&(o=(o+1)%t),u-i<e)return;const h=l&&u-l;return h?Math.round(S*1e3/h):void 0}}function As(t,e){let s=0,n=1e3/e,r,o;const i=(u,l=Date.now())=>{s=l,r=null,o&&(clearTimeout(o),o=null),t.apply(null,u)};return[(...u)=>{const l=Date.now(),p=l-s;p>=n?i(u,l):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},n-p)))},()=>r&&i(r)]}const se=(t,e,s=3)=>{let n=0;const r=Ts(50,250);return As(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-n,u=r(f),l=i<=c;n=i;const p={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:u||void 0,estimated:u&&c&&l?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[e?"download":"upload"]:!0};t(p)},s)},Ue=(t,e)=>{const s=t!=null;return[n=>e[0]({lengthComputable:s,total:t,loaded:n}),e[1]]},Fe=t=>(...e)=>a.asap(()=>t(...e)),Os=k.hasStandardBrowserEnv?((t,e)=>s=>(s=new URL(s,k.origin),t.protocol===s.protocol&&t.host===s.host&&(e||t.port===s.port)))(new URL(k.origin),k.navigator&&/(msie|trident)/i.test(k.navigator.userAgent)):()=>!0,ks=k.hasStandardBrowserEnv?{write(t,e,s,n,r,o){const i=[t+"="+encodeURIComponent(e)];a.isNumber(s)&&i.push("expires="+new Date(s).toGMTString()),a.isString(n)&&i.push("path="+n),a.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(t){const e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Cs(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function Ns(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function rt(t,e,s){let n=!Cs(e);return t&&(n||s==!1)?Ns(t,e):e}const Le=t=>t instanceof _?{...t}:t;function H(t,e){e=e||{};const s={};function n(u,l,p,S){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:S},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function r(u,l,p,S){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u,p,S)}else return n(u,l,p,S)}function o(u,l){if(!a.isUndefined(l))return n(void 0,l)}function i(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return n(void 0,u)}else return n(void 0,l)}function c(u,l,p){if(p in e)return n(u,l);if(p in t)return n(void 0,u)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,l,p)=>r(Le(u),Le(l),p,!0)};return a.forEach(Object.keys(Object.assign({},t,e)),function(l){const p=f[l]||r,S=p(t[l],e[l],l);a.isUndefined(S)&&p!==c||(s[l]=S)}),s}const ot=t=>{const e=H({},t);let{data:s,withXSRFToken:n,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:c}=e;e.headers=i=_.from(i),e.url=Qe(rt(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(s)){if(k.hasStandardBrowserEnv||k.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[u,...l]=f?f.split(";").map(p=>p.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...l].join("; "))}}if(k.hasStandardBrowserEnv&&(n&&a.isFunction(n)&&(n=n(e)),n||n!==!1&&Os(e.url))){const u=r&&o&&ks.read(o);u&&i.set(r,u)}return e},_s=typeof XMLHttpRequest<"u",Ds=_s&&function(t){return new Promise(function(s,n){const r=ot(t);let o=r.data;const i=_.from(r.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:u}=r,l,p,S,h,d;function g(){h&&h(),d&&d(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout;function b(){if(!m)return;const T=_.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),C={data:!c||c==="text"||c==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:T,config:t,request:m};nt(function(I){s(I),g()},function(I){n(I),g()},C),m=null}"onloadend"in m?m.onloadend=b:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(b)},m.onabort=function(){m&&(n(new E("Request aborted",E.ECONNABORTED,t,m)),m=null)},m.onerror=function(){n(new E("Network Error",E.ERR_NETWORK,t,m)),m=null},m.ontimeout=function(){let F=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const C=r.transitional||et;r.timeoutErrorMessage&&(F=r.timeoutErrorMessage),n(new E(F,C.clarifyTimeoutError?E.ETIMEDOUT:E.ECONNABORTED,t,m)),m=null},o===void 0&&i.setContentType(null),"setRequestHeader"in m&&a.forEach(i.toJSON(),function(F,C){m.setRequestHeader(C,F)}),a.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),c&&c!=="json"&&(m.responseType=r.responseType),u&&([S,d]=se(u,!0),m.addEventListener("progress",S)),f&&m.upload&&([p,h]=se(f),m.upload.addEventListener("progress",p),m.upload.addEventListener("loadend",h)),(r.cancelToken||r.signal)&&(l=T=>{m&&(n(!T||T.type?new J(null,t,m):T),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const w=ws(r.url);if(w&&k.protocols.indexOf(w)===-1){n(new E("Unsupported protocol "+w+":",E.ERR_BAD_REQUEST,t));return}m.send(o||null)})},xs=(t,e)=>{const{length:s}=t=t?t.filter(Boolean):[];if(e||s){let n=new AbortController,r;const o=function(u){if(!r){r=!0,c();const l=u instanceof Error?u:this.reason;n.abort(l instanceof E?l:new J(l instanceof Error?l.message:l))}};let i=e&&setTimeout(()=>{i=null,o(new E(`timeout ${e} of ms exceeded`,E.ETIMEDOUT))},e);const c=()=>{t&&(i&&clearTimeout(i),i=null,t.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),t=null)};t.forEach(u=>u.addEventListener("abort",o));const{signal:f}=n;return f.unsubscribe=()=>a.asap(c),f}},Us=function*(t,e){let s=t.byteLength;if(s<e){yield t;return}let n=0,r;for(;n<s;)r=n+e,yield t.slice(n,r),n=r},Fs=async function*(t,e){for await(const s of Ls(t))yield*Us(s,e)},Ls=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}const e=t.getReader();try{for(;;){const{done:s,value:n}=await e.read();if(s)break;yield n}}finally{await e.cancel()}},Ie=(t,e,s,n)=>{const r=Fs(t,e);let o=0,i,c=f=>{i||(i=!0,n&&n(f))};return new ReadableStream({async pull(f){try{const{done:u,value:l}=await r.next();if(u){c(),f.close();return}let p=l.byteLength;if(s){let S=o+=p;s(S)}f.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(f){return c(f),r.return()}},{highWaterMark:2})},le=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",it=le&&typeof ReadableStream=="function",Is=le&&(typeof TextEncoder=="function"?(t=>e=>t.encode(e))(new TextEncoder):async t=>new Uint8Array(await new Response(t).arrayBuffer())),at=(t,...e)=>{try{return!!t(...e)}catch{return!1}},Ps=it&&at(()=>{let t=!1;const e=new Request(k.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),Pe=64*1024,Se=it&&at(()=>a.isReadableStream(new Response("").body)),ne={stream:Se&&(t=>t.body)};le&&(t=>{["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!ne[e]&&(ne[e]=a.isFunction(t[e])?s=>s[e]():(s,n)=>{throw new E(`Response type '${e}' is not supported`,E.ERR_NOT_SUPPORT,n)})})})(new Response);const Bs=async t=>{if(t==null)return 0;if(a.isBlob(t))return t.size;if(a.isSpecCompliantForm(t))return(await new Request(k.origin,{method:"POST",body:t}).arrayBuffer()).byteLength;if(a.isArrayBufferView(t)||a.isArrayBuffer(t))return t.byteLength;if(a.isURLSearchParams(t)&&(t=t+""),a.isString(t))return(await Is(t)).byteLength},Ms=async(t,e)=>{const s=a.toFiniteNumber(t.getContentLength());return s??Bs(e)},js=le&&(async t=>{let{url:e,method:s,data:n,signal:r,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:u,headers:l,withCredentials:p="same-origin",fetchOptions:S}=ot(t);u=u?(u+"").toLowerCase():"text";let h=xs([r,o&&o.toAbortSignal()],i),d;const g=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let m;try{if(f&&Ps&&s!=="get"&&s!=="head"&&(m=await Ms(l,n))!==0){let C=new Request(e,{method:"POST",body:n,duplex:"half"}),L;if(a.isFormData(n)&&(L=C.headers.get("content-type"))&&l.setContentType(L),C.body){const[I,Y]=Ue(m,se(Fe(f)));n=Ie(C.body,Pe,I,Y)}}a.isString(p)||(p=p?"include":"omit");const b="credentials"in Request.prototype;d=new Request(e,{...S,signal:h,method:s.toUpperCase(),headers:l.normalize().toJSON(),body:n,duplex:"half",credentials:b?p:void 0});let w=await fetch(d,S);const T=Se&&(u==="stream"||u==="response");if(Se&&(c||T&&g)){const C={};["status","statusText","headers"].forEach(ke=>{C[ke]=w[ke]});const L=a.toFiniteNumber(w.headers.get("content-length")),[I,Y]=c&&Ue(L,se(Fe(c),!0))||[];w=new Response(Ie(w.body,Pe,I,()=>{Y&&Y(),g&&g()}),C)}u=u||"text";let F=await ne[a.findKey(ne,u)||"text"](w,t);return!T&&g&&g(),await new Promise((C,L)=>{nt(C,L,{data:F,headers:_.from(w.headers),status:w.status,statusText:w.statusText,config:t,request:d})})}catch(b){throw g&&g(),b&&b.name==="TypeError"&&/Load failed|fetch/i.test(b.message)?Object.assign(new E("Network Error",E.ERR_NETWORK,t,d),{cause:b.cause||b}):E.from(b,b&&b.code,t,d)}}),be={http:es,xhr:Ds,fetch:js};a.forEach(be,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});const Be=t=>`- ${t}`,Hs=t=>a.isFunction(t)||t===null||t===!1,ct={getAdapter:t=>{t=a.isArray(t)?t:[t];const{length:e}=t;let s,n;const r={};for(let o=0;o<e;o++){s=t[o];let i;if(n=s,!Hs(s)&&(n=be[(i=String(s)).toLowerCase()],n===void 0))throw new E(`Unknown adapter '${i}'`);if(n)break;r[i||"#"+o]=n}if(!n){const o=Object.entries(r).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=e?o.length>1?`since :
`+o.map(Be).join(`
`):" "+Be(o[0]):"as no adapter specified";throw new E("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:be};function he(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new J(null,t)}function Me(t){return he(t),t.headers=_.from(t.headers),t.data=pe.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),ct.getAdapter(t.adapter||X.adapter)(t).then(function(n){return he(t),n.data=pe.call(t,t.transformResponse,n),n.headers=_.from(n.headers),n},function(n){return st(n)||(he(t),n&&n.response&&(n.response.data=pe.call(t,t.transformResponse,n.response),n.response.headers=_.from(n.response.headers))),Promise.reject(n)})}const lt="1.10.0",ue={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{ue[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});const je={};ue.transitional=function(e,s,n){function r(o,i){return"[Axios v"+lt+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,c)=>{if(e===!1)throw new E(r(i," has been removed"+(s?" in "+s:"")),E.ERR_DEPRECATED);return s&&!je[i]&&(je[i]=!0,console.warn(r(i," has been deprecated since v"+s+" and will be removed in the near future"))),e?e(o,i,c):!0}};ue.spelling=function(e){return(s,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};function qs(t,e,s){if(typeof t!="object")throw new E("options must be an object",E.ERR_BAD_OPTION_VALUE);const n=Object.keys(t);let r=n.length;for(;r-- >0;){const o=n[r],i=e[o];if(i){const c=t[o],f=c===void 0||i(c,o,t);if(f!==!0)throw new E("option "+o+" must be "+f,E.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new E("Unknown option "+o,E.ERR_BAD_OPTION)}}const ee={assertOptions:qs,validators:ue},U=ee.validators;let j=class{constructor(e){this.defaults=e||{},this.interceptors={request:new De,response:new De}}async request(e,s){try{return await this._request(e,s)}catch(n){if(n instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(e,s){typeof e=="string"?(s=s||{},s.url=e):s=e||{},s=H(this.defaults,s);const{transitional:n,paramsSerializer:r,headers:o}=s;n!==void 0&&ee.assertOptions(n,{silentJSONParsing:U.transitional(U.boolean),forcedJSONParsing:U.transitional(U.boolean),clarifyTimeoutError:U.transitional(U.boolean)},!1),r!=null&&(a.isFunction(r)?s.paramsSerializer={serialize:r}:ee.assertOptions(r,{encode:U.function,serialize:U.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),ee.assertOptions(s,{baseUrl:U.spelling("baseURL"),withXsrfToken:U.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[s.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],d=>{delete o[d]}),s.headers=_.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(s)===!1||(f=f&&g.synchronous,c.unshift(g.fulfilled,g.rejected))});const u=[];this.interceptors.response.forEach(function(g){u.push(g.fulfilled,g.rejected)});let l,p=0,S;if(!f){const d=[Me.bind(this),void 0];for(d.unshift.apply(d,c),d.push.apply(d,u),S=d.length,l=Promise.resolve(s);p<S;)l=l.then(d[p++],d[p++]);return l}S=c.length;let h=s;for(p=0;p<S;){const d=c[p++],g=c[p++];try{h=d(h)}catch(m){g.call(this,m);break}}try{l=Me.call(this,h)}catch(d){return Promise.reject(d)}for(p=0,S=u.length;p<S;)l=l.then(u[p++],u[p++]);return l}getUri(e){e=H(this.defaults,e);const s=rt(e.baseURL,e.url,e.allowAbsoluteUrls);return Qe(s,e.params,e.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(e){j.prototype[e]=function(s,n){return this.request(H(n||{},{method:e,url:s,data:(n||{}).data}))}});a.forEach(["post","put","patch"],function(e){function s(n){return function(o,i,c){return this.request(H(c||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}j.prototype[e]=s(),j.prototype[e+"Form"]=s(!0)});let zs=class ut{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(o){s=o});const n=this;this.promise.then(r=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](r);n._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(c=>{n.subscribe(c),o=c}).then(r);return i.cancel=function(){n.unsubscribe(o)},i},e(function(o,i,c){n.reason||(n.reason=new J(o,i,c),s(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const s=this._listeners.indexOf(e);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const e=new AbortController,s=n=>{e.abort(n)};return this.subscribe(s),e.signal.unsubscribe=()=>this.unsubscribe(s),e.signal}static source(){let e;return{token:new ut(function(r){e=r}),cancel:e}}};function Ks(t){return function(s){return t.apply(null,s)}}function Vs(t){return a.isObject(t)&&t.isAxiosError===!0}const Re={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Re).forEach(([t,e])=>{Re[e]=t});function ft(t){const e=new j(t),s=qe(j.prototype.request,e);return a.extend(s,j.prototype,e,{allOwnKeys:!0}),a.extend(s,e,null,{allOwnKeys:!0}),s.create=function(r){return ft(H(t,r))},s}const R=ft(X);R.Axios=j;R.CanceledError=J;R.CancelToken=zs;R.isCancel=st;R.VERSION=lt;R.toFormData=ce;R.AxiosError=E;R.Cancel=R.CanceledError;R.all=function(e){return Promise.all(e)};R.spread=Ks;R.isAxiosError=Vs;R.mergeConfig=H;R.AxiosHeaders=_;R.formToJSON=t=>tt(a.isHTMLForm(t)?new FormData(t):t);R.getAdapter=ct.getAdapter;R.HttpStatusCode=Re;R.default=R;const{Axios:Qs,AxiosError:en,CanceledError:tn,isCancel:sn,CancelToken:nn,VERSION:rn,all:on,Cancel:an,isAxiosError:cn,spread:ln,toFormData:un,AxiosHeaders:fn,HttpStatusCode:dn,formToJSON:pn,getAdapter:hn,mergeConfig:mn}=R,B=class B{constructor(){$(this,"tokenData",null);$(this,"initialized",!1)}static getInstance(){return B.instance||(B.instance=new B),B.instance}initialize(){!this.initialized&&this.isBrowser()&&(this.loadTokenFromStorage(),this.initialized=!0)}isBrowser(){return typeof window<"u"&&typeof localStorage<"u"}loadTokenFromStorage(){if(this.isBrowser())try{const e=localStorage.getItem(fe.ADMIN_TOKEN);if(e){const s=JSON.parse(e);this.isTokenValid(s)?this.tokenData=s:this.clearToken()}}catch(e){console.error("Error loading token from storage:",e),this.clearToken()}}saveTokenToStorage(e){if(this.isBrowser())try{localStorage.setItem(fe.ADMIN_TOKEN,JSON.stringify(e))}catch(s){console.error("Error saving token to storage:",s)}}isTokenValid(e){return Date.now()<e.expires_at}setToken(e){this.initialize();const s=Date.now()+e.expires_in*1e3;this.tokenData={access_token:e.access_token,token_type:e.token_type,expires_in:e.expires_in,expires_at:s},this.saveTokenToStorage(this.tokenData)}getToken(){return this.initialize(),this.tokenData&&this.isTokenValid(this.tokenData)?this.tokenData.access_token:(this.clearToken(),null)}getAuthHeader(){const e=this.getToken();return e?`Bearer ${e}`:null}isAuthenticated(){return this.initialize(),this.getToken()!==null}clearToken(){this.tokenData=null,this.isBrowser()&&localStorage.removeItem(fe.ADMIN_TOKEN)}getTokenExpiration(){return this.initialize(),this.tokenData?new Date(this.tokenData.expires_at):null}getTimeUntilExpiration(){return this.initialize(),this.tokenData?Math.max(0,Math.floor((this.tokenData.expires_at-Date.now())/1e3)):0}isTokenExpiringSoon(){const e=this.getTimeUntilExpiration();return e>0&&e<300}};$(B,"instance");let we=B,me=null;const z=()=>(me||(me=we.getInstance()),me),Js={isAuthenticated:()=>typeof window>"u"?!1:z().isAuthenticated(),getAuthHeader:()=>typeof window>"u"?null:z().getAuthHeader(),getToken:()=>typeof window>"u"?null:z().getToken(),setToken:t=>{typeof window>"u"||z().setToken(t)},logout:()=>{typeof window>"u"||z().clearToken()},getTokenInfo:()=>{if(typeof window>"u")return{expiresAt:null,timeUntilExpiration:0,isExpiringSoon:!1};const t=z();return{expiresAt:t.getTokenExpiration(),timeUntilExpiration:t.getTimeUntilExpiration(),isExpiringSoon:t.isTokenExpiringSoon()}}},P=Js,En={username:He.ADMIN_USERNAME,password:"admin123"};class $s{constructor(){$(this,"client");this.client=R.create({baseURL:ht,timeout:3e4,headers:{"Content-Type":"application/json"}}),this.client.interceptors.request.use(e=>{const s=P.getAuthHeader();return s&&(e.headers.Authorization=s),e},e=>Promise.reject(e)),this.client.interceptors.response.use(e=>e,e=>{const s=this.handleError(e);return Promise.reject(s)})}handleError(e){if(e.response){const s=e.response.status,n=e.response.data;switch(s){case 401:return P.logout(),{message:q.UNAUTHORIZED,status:s,code:"UNAUTHORIZED"};case 422:return{message:(n==null?void 0:n.detail)||q.VALIDATION_ERROR,status:s,code:"VALIDATION_ERROR"};case 500:return{message:q.SERVER_ERROR,status:s,code:"SERVER_ERROR"};default:return{message:(n==null?void 0:n.message)||q.UNKNOWN_ERROR,status:s,code:"API_ERROR"}}}else return e.request?{message:q.NETWORK_ERROR,code:"NETWORK_ERROR"}:{message:e.message||q.UNKNOWN_ERROR,code:"UNKNOWN_ERROR"}}async healthCheck(){return(await this.client.get(A.HEALTH_CHECK)).data}async adminLogin(e){return(await this.client.post(A.ADMIN_LOGIN,e)).data}async verifyAdminToken(){return(await this.client.get(A.ADMIN_VERIFY)).data}async registerUser(e){return(await this.client.post(A.REGISTER_USER,e)).data}async registerCreator(e){return(await this.client.post(A.REGISTER_CREATOR,e)).data}async submitNotInterested(e){return(await this.client.post(A.SUBMIT_NOT_INTERESTED,e)).data}async submitFeedback(e){return(await this.client.post(A.SUBMIT_FEEDBACK,e)).data}async getUsers(){return(await this.client.get(A.GET_USERS)).data}async getCreators(){return(await this.client.get(A.GET_CREATORS)).data}async getFeedback(){return(await this.client.get(A.GET_FEEDBACK)).data}async getUserAnalytics(){return(await this.client.get(A.USER_ANALYTICS)).data}async getFeedbackAnalytics(){return(await this.client.get(A.FEEDBACK_ANALYTICS)).data}async getFeedbackSummary(){return(await this.client.get(A.FEEDBACK_SUMMARY)).data}async downloadData(){return(await this.client.post(A.DOWNLOAD_DATA,{})).data}async exportDataJson(){return(await this.client.get(A.EXPORT_JSON)).data}async getStats(){return(await this.client.get(A.GET_STATS)).data}}const O=new $s,D={healthCheck:()=>O.healthCheck(),login:t=>O.adminLogin(t),verifyToken:()=>O.verifyAdminToken(),registerUser:t=>O.registerUser(t),registerCreator:t=>O.registerCreator(t),submitNotInterested:t=>O.submitNotInterested(t),submitFeedback:t=>O.submitFeedback(t),getUsers:()=>O.getUsers(),getCreators:()=>O.getCreators(),getFeedback:()=>O.getFeedback(),getUserAnalytics:()=>O.getUserAnalytics(),getFeedbackAnalytics:()=>O.getFeedbackAnalytics(),getFeedbackSummary:()=>O.getFeedbackSummary(),downloadData:()=>O.downloadData(),exportDataJson:()=>O.exportDataJson(),getStats:()=>O.getStats()};function K(t=null){const[e,s]=y.useState(t),[n,r]=y.useState(!1),[o,i]=y.useState(null),[c,f]=y.useState(!1),u=y.useCallback(()=>{s(t),r(!1),i(null),f(!1)},[t]);return{data:e,loading:n,error:o,success:c,setData:s,setLoading:r,setError:i,setSuccess:f,reset:u}}function gn(){const[t,e]=y.useState(!1),[s,n]=y.useState(!1),[r,o]=y.useState(null);y.useEffect(()=>{typeof window<"u"&&e(P.isAuthenticated())},[]);const i=y.useCallback(async u=>{n(!0),o(null);try{const l=await D.login(u);return l.success&&l.data?(P.setToken(l.data),e(!0),!0):(o(l.message||"Login failed"),!1)}catch(l){return o(l.message),!1}finally{n(!1)}},[]),c=y.useCallback(()=>{P.logout(),e(!1),o(null)},[]),f=y.useCallback(async()=>{if(!P.isAuthenticated())return e(!1),!1;try{const l=(await D.verifyToken()).success;return e(l),l}catch{return e(!1),!1}},[]);return{isAuthenticated:t,isLoading:s,error:r,login:i,logout:c,verifyToken:f,tokenInfo:P.getTokenInfo()}}function yn(){const[t,e]=y.useState({isSubmitting:!1,error:null,success:!1,successMessage:null}),s=y.useCallback(async o=>{e(i=>({...i,isSubmitting:!0,error:null,success:!1}));try{const i=await D.registerUser(o);return i.success?(e(c=>({...c,isSubmitting:!1,success:!0,successMessage:te.REGISTRATION_SUCCESS})),!0):(e(c=>({...c,isSubmitting:!1,error:i.message||"Registration failed"})),!1)}catch(i){const c=i;return e(f=>({...f,isSubmitting:!1,error:c.message})),!1}},[]),n=y.useCallback(async o=>{e(i=>({...i,isSubmitting:!0,error:null,success:!1}));try{const i=await D.registerCreator(o);return i.success?(e(c=>({...c,isSubmitting:!1,success:!0,successMessage:te.REGISTRATION_SUCCESS})),!0):(e(c=>({...c,isSubmitting:!1,error:i.message||"Registration failed"})),!1)}catch(i){const c=i;return e(f=>({...f,isSubmitting:!1,error:c.message})),!1}},[]),r=y.useCallback(()=>{e({isSubmitting:!1,error:null,success:!1,successMessage:null})},[]);return{...t,registerUser:s,registerCreator:n,reset:r}}function Sn(){const[t,e]=y.useState({isSubmitting:!1,error:null,success:!1,successMessage:null}),s=y.useCallback(async r=>{e(o=>({...o,isSubmitting:!0,error:null,success:!1}));try{const o=await D.submitNotInterested(r);return o.success?(e(i=>({...i,isSubmitting:!1,success:!0,successMessage:te.NOT_INTERESTED_SUCCESS})),!0):(e(i=>({...i,isSubmitting:!1,error:o.message||"Submission failed"})),!1)}catch(o){const i=o;return e(c=>({...c,isSubmitting:!1,error:i.message})),!1}},[]),n=y.useCallback(()=>{e({isSubmitting:!1,error:null,success:!1,successMessage:null})},[]);return{...t,submit:s,reset:n}}function bn(){const[t,e]=y.useState({isSubmitting:!1,error:null,success:!1,successMessage:null}),s=y.useCallback(async r=>{e(o=>({...o,isSubmitting:!0,error:null,success:!1}));try{const o=await D.submitFeedback(r);return o.success?(e(i=>({...i,isSubmitting:!1,success:!0,successMessage:te.FEEDBACK_SUCCESS})),!0):(e(i=>({...i,isSubmitting:!1,error:o.message||"Submission failed"})),!1)}catch(o){const i=o;return e(c=>({...c,isSubmitting:!1,error:i.message})),!1}},[]),n=y.useCallback(()=>{e({isSubmitting:!1,error:null,success:!1,successMessage:null})},[]);return{...t,submit:s,reset:n}}function Rn(){const t=K(),e=K(),s=K(),n=K(),r=K(),o=K(),i=y.useCallback(async()=>{t.setLoading(!0),t.setError(null);try{const h=await D.getUsers();h.success?(t.setData(h.data),t.setSuccess(!0)):t.setError(h.message||"Failed to fetch users")}catch(h){const d=h;t.setError(d.message)}finally{t.setLoading(!1)}},[]),c=y.useCallback(async()=>{e.setLoading(!0),e.setError(null);try{const h=await D.getCreators();h.success?(e.setData(h.data),e.setSuccess(!0)):e.setError(h.message||"Failed to fetch creators")}catch(h){const d=h;e.setError(d.message)}finally{e.setLoading(!1)}},[]),f=y.useCallback(async()=>{s.setLoading(!0),s.setError(null);try{const h=await D.getFeedback();h.success?(s.setData(h.data),s.setSuccess(!0)):s.setError(h.message||"Failed to fetch feedback")}catch(h){const d=h;s.setError(d.message)}finally{s.setLoading(!1)}},[]),u=y.useCallback(async()=>{n.setLoading(!0),n.setError(null);try{const h=await D.getUserAnalytics();h.success?(n.setData(h.data),n.setSuccess(!0)):n.setError(h.message||"Failed to fetch user analytics")}catch(h){const d=h;n.setError(d.message)}finally{n.setLoading(!1)}},[]),l=y.useCallback(async()=>{r.setLoading(!0),r.setError(null);try{const h=await D.getFeedbackAnalytics();h.success?(r.setData(h.data),r.setSuccess(!0)):r.setError(h.message||"Failed to fetch feedback analytics")}catch(h){const d=h;r.setError(d.message)}finally{r.setLoading(!1)}},[]),p=y.useCallback(async()=>{o.setLoading(!0),o.setError(null);try{const h=await D.getStats();h.success?(o.setData(h.data),o.setSuccess(!0)):o.setError(h.message||"Failed to fetch stats")}catch(h){const d=h;o.setError(d.message)}finally{o.setLoading(!1)}},[]),S=y.useCallback(async()=>{await Promise.all([i(),c(),f(),u(),l(),p()])},[i,c,f,u,l,p]);return{users:t,creators:e,feedback:s,userAnalytics:n,feedbackAnalytics:r,stats:o,fetchUsers:i,fetchCreators:c,fetchFeedback:f,fetchUserAnalytics:u,fetchFeedbackAnalytics:l,fetchStats:p,fetchAllData:S}}function wn(){const[t,e]=y.useState(null),[s,n]=y.useState(!1),[r,o]=y.useState(null),i=y.useCallback(async()=>{n(!0);try{const c=await D.healthCheck();e(c.status==="healthy"),o(new Date)}catch{e(!1),o(new Date)}finally{n(!1)}},[]);return y.useEffect(()=>{i()},[i]),{isHealthy:t,isChecking:s,lastChecked:r,checkHealth:i}}export{Gs as A,En as D,Xs as Z,Sn as a,bn as b,gn as c,Rn as d,wn as e,D as f,yn as u};
