{"version": 3, "file": "global-storage-utils.js", "sources": ["../../../analytics-util-types/dist/analytics-util-types.module.js", "../../src/index.js"], "sourcesContent": ["var n=\"function\",t=\"string\",e=\"undefined\",r=\"boolean\",o=\"object\",u=\"array\",i=\"number\",c=\"symbol\",a=\"null\",f=\"error\",s=\"typeError\",l=\"syntaxError\",d=\"asyncFunction\",p=\"generatorFunction\",y=\"asyncGeneratorFunction\",g=function(){},b=\"any\",m=\"*\",v=\"none\",h=\"hidden\",j=\"__\",O=\"form\",S=\"input\",A=\"button\",E=\"select\",N=\"change\",w=\"submit\",D=/^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/,z=/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z=/^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/,F=\"undefined\"!=typeof process?process:{},P=F.env&&F.env.NODE_ENV||\"\",x=\"production\"===P,C=\"staging\"===P,L=\"development\"===P,$=\"undefined\"!=typeof document,T=$&&\"localhost\"===window.location.hostname,_=null!=F.versions&&null!=F.versions.node,k=\"undefined\"!=typeof Deno&&void 0!==Deno.core,B=\"object\"==typeof self&&self.constructor&&\"DedicatedWorkerGlobalScope\"===self.constructor.name,G=$&&\"nodejs\"===window.name||\"undefined\"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes(\"Node.js\")||navigator.userAgent.includes(\"jsdom\"));function M(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=M.bind(null,\"toUpperCase\"),H=M.bind(null,\"toLowerCase\");function J(n){return Y(n)?U(\"null\"):\"object\"==typeof n?yn(n):Object.prototype.toString.call(n).slice(8,-1)}function R(n,t){void 0===t&&(t=!0);var e=J(n);return t?H(e):e}function V(n,t){return typeof t===n}var W=V.bind(null,\"function\"),q=V.bind(null,\"string\"),I=V.bind(null,\"undefined\");function K(n){return!I(n)}var Q=V.bind(null,\"boolean\"),X=V.bind(null,\"symbol\");function Y(n){return null===n}function nn(n){return\"number\"===R(n)&&!isNaN(n)}function tn(n){return!isNaN(parseFloat(n))}function en(n){return!!W(n)&&/^class /.test(Function.prototype.toString.call(n))}function rn(n){return\"array\"===R(n)}function on(n){if(!un(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function un(n){return n&&(\"object\"==typeof n||null!==n)}function cn(n){if(!q(n)||!Z.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0}function an(n){if(Y(n))return!0;switch(typeof n){case\"string\":case\"number\":case\"symbol\":case\"undefined\":case\"boolean\":return!0;default:return!1}}function fn(n,t){return on(n)&&W(n[t])}function sn(n){return!!n&&!!(!I(Promise)&&n instanceof Promise||n.then&&W(n.then))}function ln(n){return un(n)&&W(n.throw)&&W(n.return)&&W(n.next)}function dn(n){return\"generatorFunction\"===R(n)}function pn(n){return\"asyncFunction\"===R(n)}function yn(n){return W(n.constructor)?n.constructor.name:null}function gn(n){return n instanceof Set}function bn(n){return n instanceof Map}function mn(n){return n instanceof RegExp}function vn(n){return!(!n.constructor||!W(n.constructor.isBuffer))&&n.constructor.isBuffer(n)}function hn(n){return n instanceof Error||q(n.message)&&n.constructor&&nn(n.constructor.stackTraceLimit)}function jn(n){return un(n)&&q(n.message)&&q(n.name)}function On(n,t){if(\"object\"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=R(new n(\"\"));if(hn(t))for(;t;){if(R(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var Sn=On.bind(null,TypeError),An=On.bind(null,SyntaxError);function En(n){if(!W(n))return!1;var t=/{(\\r|\\n|\\s)*}/gm,e=g+\"\";return e===(n.toString().match(t)||[\"\"])[0].replace(t,e)}function Nn(n){try{if(nn(n.length)&&W(n.callee))return!0}catch(n){if(-1!==n.message.indexOf(\"callee\"))return!0}return!1}function wn(n){return!(q(n)&&\"false\"===n.toLowerCase()||!n)}function Dn(n){return!n}function zn(n){return!0===n}function Zn(n){return!1===n}function Fn(n){return!(n.length>320)&&z.test(n)}function Pn(n){return n instanceof Date||W(n.toDateString)&&W(n.getDate)&&W(n.setDate)}function xn(n){return D.test(n)}function Cn(n){return!(!Y(n)&&(rn(n)?n.length:gn(n)||bn(n)?n.size:on(n)&&Object.keys(n).length))}function Ln(n){return NodeList.prototype.isPrototypeOf(n)}function $n(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?Tn(n,t):e}function Tn(n,t){return void 0===t&&(t=\"\"),n&&n.nodeName===t.toUpperCase()}function _n(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var kn=_n($n,\"form\"),Bn=_n($n,\"button\"),Gn=_n($n,\"input\"),Mn=_n($n,\"select\");function Un(n,t){if(!n||\"hidden\"===getComputedStyle(n).visibility)return!0;for(;n;){if(null!=t&&n===t)return!1;if(\"none\"===getComputedStyle(n).display)return!0;n=n.parentElement}return!1}function Hn(n){return n?rn(n)?n:[n]:[]}export{m as ALL,b as ANY,u as ARRAY,d as ASYNC_FUNCTION,y as ASYNC_GENERATOR_FUNCTION,r as BOOLEAN,A as BUTTON,N as CHANGE,P as ENV,f as ERROR,O as FORM,n as FUNCTION,p as GENERATOR_FUNCTION,h as HIDDEN,S as INPUT,v as NONE,a as NULL,i as NUMBER,o as OBJECT,j as PREFIX,z as REGEX_EMAIL,D as REGEX_ISO,Z as REGEX_JSON,E as SELECT,t as STRING,w as SUBMIT,c as SYMBOL,l as SYNTAX_ERROR,s as TYPE_ERROR,e as UNDEFINED,yn as ctorName,Hn as ensureArray,R as getType,J as getTypeName,Nn as isArguments,rn as isArray,pn as isAsyncFunction,Q as isBoolean,$ as isBrowser,vn as isBuffer,Bn as isButton,en as isClass,Pn as isDate,K as isDefined,k as isDeno,L as isDev,$n as isElement,Fn as isEmail,Cn as isEmpty,hn as isError,jn as isErrorLike,Zn as isFalse,Dn as isFalsy,kn as isForm,W as isFunction,ln as isGenerator,dn as isGeneratorFunction,Un as isHidden,Gn as isInput,xn as isIsoDate,G as isJsDom,cn as isJson,T as isLocalHost,bn as isMap,fn as isMethod,En as isNoOp,_ as isNode,Ln as isNodeList,Tn as isNodeType,Y as isNull,nn as isNumber,tn as isNumberLike,on as isObject,un as isObjectLike,an as isPrimitive,x as isProd,sn as isPromise,mn as isRegex,Mn as isSelect,gn as isSet,C as isStaging,q as isString,X as isSymbol,An as isSyntaxError,zn as isTrue,wn as isTruthy,Sn as isTypeError,I as isUndefined,B as isWebWorker,g as noOp};\n//# sourceMappingURL=analytics-util-types.module.js.map\n", "import { OBJECT, PREFIX, UNDEFINED } from '@analytics/type-utils'\n\nexport const GLOBAL = 'global'\n\nexport const KEY = PREFIX + GLOBAL + PREFIX\n\nexport const globalContext = (typeof self === OBJECT && self.self === self && self) || (typeof global === OBJECT && global[GLOBAL] === global && global) || this\n\n/* initialize global object */\nif (!globalContext[KEY]) {\n  globalContext[KEY] = {}\n}\n/**\n * Get value from global context\n * @param {string} key - Key of value to get\n * @returns {*} value\n */\nexport function get(key) {\n  return globalContext[KEY][key]\n}\n\n/**\n * Set value to global context\n * @param {string} key - Key of value to set\n * @param {*} value \n * @returns value\n */\nexport function set(key, value) {\n  return globalContext[KEY][key] = value\n}\n\n/**\n * Remove value to global context\n * @param {string} key - Key of value to remove\n */\nexport function remove(key) {\n  delete globalContext[KEY][key]\n}\n\n/**\n * Wrap localStorage & session storage checks\n * @param {string} type - localStorage or sessionStorage\n * @param {string} storageOperation - getItem, setItem, removeItem\n * @param {function} fallbackFunction - fallback function\n */\nexport function wrap(type, operation, fallback) {\n  let fn\n  try {\n    if (hasSupport(type)) {\n      const storage = window[type]\n      fn = storage[operation].bind(storage)\n    }\n  } catch(e) {}\n  return fn || fallback\n}\n\nconst cache = {}\nexport function hasSupport(type) {\n  if (typeof cache[type] !== UNDEFINED) {\n    return cache[type]\n  }\n  try {\n    const storage = window[type]\n    // test for private safari\n    storage.setItem(UNDEFINED, UNDEFINED)\n    storage.removeItem(UNDEFINED)\n  } catch (err) {\n    return cache[type] = false\n  }\n  return cache[type] = true\n}\n\n/*\n// () => localStorage)\n// () => sessionStorage)\nexport function isSupported(getStorage) {\n  try {\n    const testKey = '__' + undef\n    getStorage().setItem(testKey, testKey)\n    getStorage().removeItem(testKey)\n    return true\n  } catch (e) {\n    return false\n  }\n}\n*/\n"], "names": ["UNDEFINED", "OBJECT", "process", "<PERSON><PERSON><PERSON><PERSON>", "method", "s", "char<PERSON>t", "slice", "<PERSON><PERSON>", "window", "name", "navigator", "userAgent", "includes", "text", "bind", "lower", "val", "toLowerCase", "upper", "x", "constructor", "ctorName", "Object", "prototype", "toString", "call", "getTypeName", "type", "kind", "typeOf", "isString", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNull", "getType", "message", "n", "isNaN", "isNumber", "stackTraceLimit", "isError", "typeName", "getPrototypeOf", "element", "nodeName", "toUpperCase", "isNodeType", "isEl", "fn", "boundArgs", "errorType", "TypeError", "SyntaxError", "bind<PERSON><PERSON><PERSON>", "isElement", "KEY", "PREFIX", "globalContext", "self", "global", "this", "cache", "hasSupport", "storage", "setItem", "removeItem", "err", "key", "operation", "fallback", "e"], "mappings": "sCAOaA,EAAY,YAEZC,EAAS,SAFG,6BAwCsBC,YAelCC,EAvDY,6BA2EzB,WAAcC,EAAQC,GACpB,SAASC,OAAO,GAAGF,KAAYC,EAAEE,MAAM,GA5EhB,0BA+DiCC,KAMlCL,GAA6B,WAAhBM,OAAOC,MArEnB,oCAqEiDC,IAAkCA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,gBAU5MC,EAAKC,KAAK,KAAM,eACxBC,EAAQF,EAAKC,KAAK,KAAM,0BAiBNE,EAAKC,sBAC3B,MAlB4B,SAOFD,GAC1B,SAAWA,GAAaE,EAlFN,QAJE,4BA+VGC,GACvB,SAAkBA,EAAEC,aAAeD,EAAEC,YAAYX,KAAO,KAzQvBY,CAASL,GAAOM,OAAOC,UAAUC,SAASC,KAAKT,GAAKV,MAAM,GAAI,GASlFoB,CAAYV,GAEzB,SAAuBD,EAAMY,GAAQA,aAUvBC,EAAMZ,GACpB,kBAAsBY,QAQEC,EAAOf,KAAK,KAzHd,YAgIXgB,EAAWD,EAAOf,KAAK,KA/Hd,UAMA,WA8JCK,GACrB,cAAOA,aA4QUY,EAASC,GAC1B,GAAqB,oBAAYC,EAAOD,GAAQ,SAEhD,GAAIA,eAA0B,SAC9B,MAAiBE,EAAQ,MAAY,KAErC,YAnCsBf,GACtB,2BAA8BW,EAASX,EAAEgB,UAAYhB,EAAEC,sBAvNhCgB,GACvB,MA1LoB,WA0LbF,EAAQE,KAAkBC,MAAMD,GAsN+BE,CAASnB,EAAEC,YAAYmB,iBAkCzFC,CAAQR,GACV,KAAOA,GAAO,CACZ,GAAIE,EAAQF,KAAWS,EACrB,SAEAT,EAAQV,OAAOoB,eAAeV,uBA8JZW,EAAShB,GACjC,MAAagB,sBAA8BA,0BAC3C,UAAchB,WAUWgB,EAAShB,GAClC,gBADkCA,IAAAA,EAAO,IAClCgB,GAAWA,EAAQC,WAAajB,EAAKkB,cAVrCC,CAAWH,EAAShB,GADAoB,aAcXC,oCAChB,kBACE,sDAAsBC,KAteCpB,EAAOf,KAAK,KArId,aAoJAe,EAAOf,KAAK,KAnJd,WAyJCe,EAAOf,KAAK,KArJd,UA4bKoC,EAAUpC,KAAK,KAAMqC,WAEnBD,EAAUpC,KAAK,KAAMsC,aAiL5BC,EAASC,EA9lBX,QAqmBID,EAASC,EAnmBX,UA0mBCD,EAASC,EA3mBX,SAknBGD,EAASC,EAhnBX,cC5BTC,EAAMC,aAENC,SAAwBC,OAAS1D,GAAU0D,KAAKA,OAASA,MAAQA,aAAiBC,SAAW3D,GAAU2D,OAAM,SAAaA,QAAUA,aAAWC,EAGvJH,EAAcF,KACjBE,EAAcF,GAAO,IA8CvB,IAAMM,EAAQ,YACEC,EAAWnC,GACzB,UAAWkC,EAAMlC,KAAU5B,EACzB,OAAO8D,EAAMlC,GAEf,IACE,IAAMoC,EAAUvD,OAAOmB,GAEvBoC,EAAQC,QAAQjE,EAAWA,GAC3BgE,EAAQE,WAAWlE,GACnB,MAAOmE,GACP,OAAOL,EAAMlC,IAAQ,EAEvB,OAAOkC,EAAMlC,IAAQ,kBAnED,gCAeFwC,GAClB,OAAOV,EAAcF,GAAKY,uDAiBLA,UACdV,EAAcF,GAAKY,mBATRA,EAAKnC,GACvB,OAAOyB,EAAcF,GAAKY,GAAOnC,mBAiBdL,EAAMyC,EAAWC,GACpC,IAAIrB,EACJ,IACE,GAAIc,EAAWnC,GAAO,CACpB,IAAMoC,EAAUvD,OAAOmB,GACvBqB,EAAKe,EAAQK,GAAWtD,KAAKiD,IAE/B,MAAMO,IACR,OAAOtB,GAAMqB"}