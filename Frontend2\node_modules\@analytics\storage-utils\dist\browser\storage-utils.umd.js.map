{"version": 3, "file": "storage-utils.umd.js", "sources": ["../../../analytics-util-types/dist/analytics-util-types.module.js", "../../../analytics-util-storage-global/dist/analytics-util-global-storage.module.js", "../../../analytics-util-storage-cookie/dist/analytics-util-cookie.module.js", "../../../analytics-util-storage-local/dist/analytics-util-localstorage.module.js", "../../../analytics-util-storage-session/dist/analytics-util-session-storage.module.js", "../../src/utils/parse.js", "../../src/index.js"], "sourcesContent": ["var n=\"function\",t=\"string\",e=\"undefined\",r=\"boolean\",o=\"object\",u=\"array\",i=\"number\",c=\"symbol\",a=\"null\",f=\"error\",s=\"typeError\",l=\"syntaxError\",d=\"asyncFunction\",p=\"generatorFunction\",y=\"asyncGeneratorFunction\",g=function(){},b=\"any\",m=\"*\",v=\"none\",h=\"hidden\",j=\"__\",O=\"form\",S=\"input\",A=\"button\",E=\"select\",N=\"change\",w=\"submit\",D=/^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z)$/,z=/^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,Z=/^\\{[\\s\\S]*\\}$|^\\[[\\s\\S]*\\]$/,F=\"undefined\"!=typeof process?process:{},P=F.env&&F.env.NODE_ENV||\"\",x=\"production\"===P,C=\"staging\"===P,L=\"development\"===P,$=\"undefined\"!=typeof document,T=$&&\"localhost\"===window.location.hostname,_=null!=F.versions&&null!=F.versions.node,k=\"undefined\"!=typeof Deno&&void 0!==Deno.core,B=\"object\"==typeof self&&self.constructor&&\"DedicatedWorkerGlobalScope\"===self.constructor.name,G=$&&\"nodejs\"===window.name||\"undefined\"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes(\"Node.js\")||navigator.userAgent.includes(\"jsdom\"));function M(n,t){return t.charAt(0)[n]()+t.slice(1)}var U=M.bind(null,\"toUpperCase\"),H=M.bind(null,\"toLowerCase\");function J(n){return Y(n)?U(\"null\"):\"object\"==typeof n?yn(n):Object.prototype.toString.call(n).slice(8,-1)}function R(n,t){void 0===t&&(t=!0);var e=J(n);return t?H(e):e}function V(n,t){return typeof t===n}var W=V.bind(null,\"function\"),q=V.bind(null,\"string\"),I=V.bind(null,\"undefined\");function K(n){return!I(n)}var Q=V.bind(null,\"boolean\"),X=V.bind(null,\"symbol\");function Y(n){return null===n}function nn(n){return\"number\"===R(n)&&!isNaN(n)}function tn(n){return!isNaN(parseFloat(n))}function en(n){return!!W(n)&&/^class /.test(Function.prototype.toString.call(n))}function rn(n){return\"array\"===R(n)}function on(n){if(!un(n))return!1;for(var t=n;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(n)===t}function un(n){return n&&(\"object\"==typeof n||null!==n)}function cn(n){if(!q(n)||!Z.test(n))return!1;try{JSON.parse(n)}catch(n){return!1}return!0}function an(n){if(Y(n))return!0;switch(typeof n){case\"string\":case\"number\":case\"symbol\":case\"undefined\":case\"boolean\":return!0;default:return!1}}function fn(n,t){return on(n)&&W(n[t])}function sn(n){return!!n&&!!(!I(Promise)&&n instanceof Promise||n.then&&W(n.then))}function ln(n){return un(n)&&W(n.throw)&&W(n.return)&&W(n.next)}function dn(n){return\"generatorFunction\"===R(n)}function pn(n){return\"asyncFunction\"===R(n)}function yn(n){return W(n.constructor)?n.constructor.name:null}function gn(n){return n instanceof Set}function bn(n){return n instanceof Map}function mn(n){return n instanceof RegExp}function vn(n){return!(!n.constructor||!W(n.constructor.isBuffer))&&n.constructor.isBuffer(n)}function hn(n){return n instanceof Error||q(n.message)&&n.constructor&&nn(n.constructor.stackTraceLimit)}function jn(n){return un(n)&&q(n.message)&&q(n.name)}function On(n,t){if(\"object\"!=typeof t||Y(t))return!1;if(t instanceof n)return!0;var e=R(new n(\"\"));if(hn(t))for(;t;){if(R(t)===e)return!0;t=Object.getPrototypeOf(t)}return!1}var Sn=On.bind(null,TypeError),An=On.bind(null,SyntaxError);function En(n){if(!W(n))return!1;var t=/{(\\r|\\n|\\s)*}/gm,e=g+\"\";return e===(n.toString().match(t)||[\"\"])[0].replace(t,e)}function Nn(n){try{if(nn(n.length)&&W(n.callee))return!0}catch(n){if(-1!==n.message.indexOf(\"callee\"))return!0}return!1}function wn(n){return!(q(n)&&\"false\"===n.toLowerCase()||!n)}function Dn(n){return!n}function zn(n){return!0===n}function Zn(n){return!1===n}function Fn(n){return!(n.length>320)&&z.test(n)}function Pn(n){return n instanceof Date||W(n.toDateString)&&W(n.getDate)&&W(n.setDate)}function xn(n){return D.test(n)}function Cn(n){return!(!Y(n)&&(rn(n)?n.length:gn(n)||bn(n)?n.size:on(n)&&Object.keys(n).length))}function Ln(n){return NodeList.prototype.isPrototypeOf(n)}function $n(n,t){var e=n instanceof Element||n instanceof HTMLDocument;return e&&t?Tn(n,t):e}function Tn(n,t){return void 0===t&&(t=\"\"),n&&n.nodeName===t.toUpperCase()}function _n(n){var t=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(t))}}var kn=_n($n,\"form\"),Bn=_n($n,\"button\"),Gn=_n($n,\"input\"),Mn=_n($n,\"select\");function Un(n,t){if(!n||\"hidden\"===getComputedStyle(n).visibility)return!0;for(;n;){if(null!=t&&n===t)return!1;if(\"none\"===getComputedStyle(n).display)return!0;n=n.parentElement}return!1}function Hn(n){return n?rn(n)?n:[n]:[]}export{m as ALL,b as ANY,u as ARRAY,d as ASYNC_FUNCTION,y as ASYNC_GENERATOR_FUNCTION,r as BOOLEAN,A as BUTTON,N as CHANGE,P as ENV,f as ERROR,O as FORM,n as FUNCTION,p as GENERATOR_FUNCTION,h as HIDDEN,S as INPUT,v as NONE,a as NULL,i as NUMBER,o as OBJECT,j as PREFIX,z as REGEX_EMAIL,D as REGEX_ISO,Z as REGEX_JSON,E as SELECT,t as STRING,w as SUBMIT,c as SYMBOL,l as SYNTAX_ERROR,s as TYPE_ERROR,e as UNDEFINED,yn as ctorName,Hn as ensureArray,R as getType,J as getTypeName,Nn as isArguments,rn as isArray,pn as isAsyncFunction,Q as isBoolean,$ as isBrowser,vn as isBuffer,Bn as isButton,en as isClass,Pn as isDate,K as isDefined,k as isDeno,L as isDev,$n as isElement,Fn as isEmail,Cn as isEmpty,hn as isError,jn as isErrorLike,Zn as isFalse,Dn as isFalsy,kn as isForm,W as isFunction,ln as isGenerator,dn as isGeneratorFunction,Un as isHidden,Gn as isInput,xn as isIsoDate,G as isJsDom,cn as isJson,T as isLocalHost,bn as isMap,fn as isMethod,En as isNoOp,_ as isNode,Ln as isNodeList,Tn as isNodeType,Y as isNull,nn as isNumber,tn as isNumberLike,on as isObject,un as isObjectLike,an as isPrimitive,x as isProd,sn as isPromise,mn as isRegex,Mn as isSelect,gn as isSet,C as isStaging,q as isString,X as isSymbol,An as isSyntaxError,zn as isTrue,wn as isTruthy,Sn as isTypeError,I as isUndefined,B as isWebWorker,g as noOp};\n//# sourceMappingURL=analytics-util-types.module.js.map\n", "import{PREFIX as t,OBJECT as e,UNDEFINED as r}from\"@analytics/type-utils\";var l=\"global\",o=t+\"global\"+t,n=typeof self===e&&self.self===self&&self||typeof global===e&&global.global===global&&global||void 0;function a(t){return n[o][t]}function f(t,e){return n[o][t]=e}function i(t){delete n[o][t]}function u(t,e,r){var l;try{if(b(t)){var o=window[t];l=o[e].bind(o)}}catch(t){}return l||r}n[o]||(n[o]={});var c={};function b(t){if(typeof c[t]!==r)return c[t];try{var e=window[t];e.setItem(r,r),e.removeItem(r)}catch(e){return c[t]=!1}return c[t]=!0}export{l as GLOBAL,o as KEY,a as get,n as globalContext,b as hasSupport,i as remove,f as set,u as wrap};\n//# sourceMappingURL=analytics-util-global-storage.module.js.map\n", "import{remove as e,set as o,get as n}from\"@analytics/global-storage-utils\";var t=\"cookie\",i=a(),r=d,c=d;function u(o){return i?d(o,\"\",-1):e(o)}function a(){if(void 0!==i)return i;var e=\"cookiecookie\";try{d(e,e),i=-1!==document.cookie.indexOf(e),u(e)}catch(e){i=!1}return i}function d(e,t,r,c,u,a){if(\"undefined\"!=typeof window){var d=arguments.length>1;return!1===i&&(d?o(e,t):n(e)),d?document.cookie=e+\"=\"+encodeURIComponent(t)+(r?\"; expires=\"+new Date(+new Date+1e3*r).toUTCString()+(c?\"; path=\"+c:\"\")+(u?\"; domain=\"+u:\"\")+(a?\"; secure\":\"\"):\"\"):decodeURIComponent(((\"; \"+document.cookie).split(\"; \"+e+\"=\")[1]||\"\").split(\";\")[0])}}export{t as COOKIE,r as getCookie,a as hasCookies,u as removeCookie,c as setCookie};\n//# sourceMappingURL=analytics-util-cookie.module.js.map\n", "import{hasSupport as l,wrap as o,get as a,set as t,remove as e}from\"@analytics/global-storage-utils\";var r=\"localStorage\",g=l.bind(null,\"localStorage\"),c=o(\"localStorage\",\"getItem\",a),m=o(\"localStorage\",\"setItem\",t),S=o(\"localStorage\",\"removeItem\",e);export{r as LOCAL_STORAGE,c as getItem,g as hasLocalStorage,S as removeItem,m as setItem};\n//# sourceMappingURL=analytics-util-localstorage.module.js.map\n", "import{hasSupport as e,wrap as s,get as o,set as t,remove as r}from\"@analytics/global-storage-utils\";var a=\"sessionStorage\",i=e.bind(null,\"sessionStorage\"),g=s(\"sessionStorage\",\"getItem\",o),n=s(\"sessionStorage\",\"setItem\",t),l=s(\"sessionStorage\",\"removeItem\",r);export{a as SESSION_STORAGE,g as getSessionItem,i as hasSessionStorage,l as removeSessionItem,n as setSessionItem};\n//# sourceMappingURL=analytics-util-session-storage.module.js.map\n", "import { isObject } from '@analytics/type-utils'\n/**\n * Safe JSON parse\n * @param  {*} input - value to parse\n * @return {*} parsed input\n */\nexport default function parse(input) {\n  let value = input\n  try {\n    value = JSON.parse(input)\n    if (value === 'true') return true\n    if (value === 'false') return false\n    if (isObject(value)) return value\n    if (parseFloat(value) === value) {\n      value = parseFloat(value)\n    }\n  } catch (e) { }\n  if (value === null || value === \"\") {\n    return\n  }\n  return value\n}\n", "import { set, get, remove, globalContext, GLOBAL } from '@analytics/global-storage-utils'\nimport { getCookie, setCookie, removeCookie, hasCookies, COOKIE } from '@analytics/cookie-utils'\nimport { hasLocalStorage, LOCAL_STORAGE } from '@analytics/localstorage-utils'\nimport { hasSessionStorage, SESSION_STORAGE } from '@analytics/session-storage-utils'\nimport { isUndefined, isString, ANY, ALL } from '@analytics/type-utils'\nimport parse from './utils/parse'\n\n// Verify support\nconst hasStorage = hasLocalStorage()\nconst hasSessionSupport = hasSessionStorage()\nconst hasCookiesSupport = hasCookies()\n\n/**\n * Get storage item from localStorage, cookie, or window\n * @param  {string} key - key of item to get\n * @param  {object|string} [options] - storage options. If string location of where to get storage\n * @param  {string} [options.storage] - Define type of storage to pull from.\n * @return {Any}  the value of key\n */\nexport function getItem(key, options) {\n  if (!key) return\n  const type = getStorageType(options)\n  const getFirst = !useAll(type)\n\n  /* 1. Try localStorage */\n  const localValue = useLocal(type) ? parse(localStorage.getItem(key)) : undefined\n  if (getFirst && !isUndefined(localValue)) {\n    return localValue\n  }\n\n  /* 2. Fallback to cookie */\n  const cookieVal = useCookie(type) ? parse(getCookie(key)) : undefined\n  if (getFirst && cookieVal) {\n    return cookieVal\n  }\n\n  /* 3. Fallback to sessionStorage */\n  const sessionVal = useSession(type) ? parse(sessionStorage.getItem(key)) : undefined\n  if (getFirst && sessionVal) {\n    return sessionVal\n  }\n\n  /* 4. Fallback to window/global. */\n  const globalValue = get(key)\n\n  return getFirst ? globalValue : {\n    localStorage: localValue,\n    sessionStorage: sessionVal,\n    cookie: cookieVal,\n    global: globalValue\n  }\n}\n\n/**\n * Store values in localStorage, cookie, or window\n * @param {string} key - key of item to set\n * @param {*} value - value of item to set\n * @param {object|string} [options] - storage options. If string location of where to get storage\n * @param {string} [options.storage] - Define type of storage to pull from.\n * @returns {object} returns old value, new values, & location of storage\n */\nexport function setItem(key, value, options) {\n  if (!key || isUndefined(value)) {\n    return\n  }\n  const data = {}\n  const type = getStorageType(options)\n  const saveValue = JSON.stringify(value)\n  const setFirst = !useAll(type)\n\n  /* 1. Try localStorage */\n  if (useLocal(type)) {\n    // console.log('SET as localstorage', saveValue)\n    data[LOCAL_STORAGE] = format(LOCAL_STORAGE, value, parse(localStorage.getItem(key)))\n    // Set LocalStorage item\n    localStorage.setItem(key, saveValue)\n    if (setFirst) {\n      return data[LOCAL_STORAGE]\n    }\n  }\n\n  /* 2. Fallback to cookie */\n  if (useCookie(type)) {\n    // console.log('SET as cookie', saveValue)\n    data[COOKIE] = format(COOKIE, value, parse(getCookie(key)))\n    // Set Cookie\n    setCookie(key, saveValue)\n    if (setFirst) {\n      return data[COOKIE]\n    }\n  }\n\n  /* 3. Try sessionStorage */\n  if (useSession(type)) {\n    // console.log('SET as localstorage', saveValue)\n    data[SESSION_STORAGE] = format(SESSION_STORAGE, value, parse(sessionStorage.getItem(key)))\n    // Set sessionStorage item\n    sessionStorage.setItem(key, saveValue)\n    if (setFirst) {\n      return data[SESSION_STORAGE]\n    }\n  }\n\n  /* 4. Fallback to window/global */\n  data[GLOBAL] = format(GLOBAL, value, get(key))\n  // Set global value\n  set(key, value)\n  // Return set value(s)\n  return (setFirst) ? data[GLOBAL] : data\n}\n\n/**\n * Remove values from localStorage, cookie, or window\n * @param {string} key - key of item to set\n * @param {object|string} [options] - storage options. If string location of where to get storage\n * @param {string} [options.storage] - Define type of storage to pull from.\n */\nexport function removeItem(key, options) {\n  if (!key) return\n  const type = getStorageType(options)\n  const values = getItem(key, ALL)\n\n  const data = {}\n  /* 1. Try localStorage */\n  if (!isUndefined(values.localStorage) && useLocal(type)) {\n    localStorage.removeItem(key)\n    data[LOCAL_STORAGE] = values.localStorage\n  }\n  /* 2. Fallback to cookie */\n  if (!isUndefined(values.cookie) && useCookie(type)) {\n    removeCookie(key)\n    data[COOKIE] = values.cookie\n  }\n  /* 3. Try sessionStorage */\n  if (!isUndefined(values.sessionStorage) && useSession(type)) {\n    sessionStorage.removeItem(key)\n    data[SESSION_STORAGE] = values.sessionStorage\n  }\n  /* 4. Fallback to window/global */\n  if (!isUndefined(values.global) && useGlobal(type)) {\n    remove(key)\n    data[GLOBAL] = values.global\n  }\n  return data\n}\n\nfunction getStorageType(opts) {\n  if (!opts) return ANY\n  return isString(opts) ? opts : opts.storage\n}\n\nfunction useGlobal(storage) {\n  return useType(storage, GLOBAL)\n}\n\nfunction useLocal(storage) {\n  // If has localStorage and storage option not defined, or is set to 'localStorage' or '*'\n  return hasStorage && useType(storage, LOCAL_STORAGE)\n}\n\nfunction useCookie(storage) {\n  // If has cookies and storage option not defined, or is set to 'cookies' or '*'\n  return hasCookiesSupport && useType(storage, COOKIE)\n}\n\nfunction useSession(storage) {\n  // If has sessionStorage and storage option not defined, or is set to 'sessionStorage' or '*'\n  return hasSessionSupport && useType(storage, SESSION_STORAGE)\n}\n\nfunction useAll(storage) {\n  return storage === ALL || storage === 'all'\n}\n\nfunction useType(storage, type) {\n  return (storage === ANY || storage === type || useAll(storage))\n}\n\n/**\n * Format response\n * @param {string} location \n * @param {*} current - current value\n * @param {*} previous - previous value\n * @returns \n */\nfunction format(location, current, previous) {\n  return { location, current, previous }\n}\n\n// const TYPES = {\n//   ALL,\n//   ANY,\n//   GLOBAL,\n//   COOKIE,\n//   LOCAL_STORAGE,\n//   SESSION_STORAGE,   \n// }\n\nexport {\n  ALL,\n  ANY,\n  GLOBAL,\n  COOKIE,\n  LOCAL_STORAGE,\n  SESSION_STORAGE,\n  getCookie,\n  setCookie,\n  removeCookie,\n  globalContext,\n  hasSessionStorage,\n  hasLocalStorage,\n  hasCookies\n}\n\nexport default {\n  setItem,\n  getItem,\n  removeItem\n}"], "names": ["UNDEFINED", "OBJECT", "ANY", "process", "<PERSON><PERSON><PERSON><PERSON>", "method", "s", "char<PERSON>t", "slice", "<PERSON><PERSON>", "window", "name", "navigator", "userAgent", "includes", "text", "bind", "lower", "val", "toLowerCase", "upper", "x", "constructor", "ctorName", "Object", "prototype", "toString", "call", "getTypeName", "type", "kind", "typeOf", "isString", "isUndefined", "<PERSON><PERSON><PERSON><PERSON>", "value", "isNull", "getType", "message", "n", "isNaN", "isNumber", "stackTraceLimit", "isError", "typeName", "getPrototypeOf", "element", "nodeName", "toUpperCase", "isNodeType", "isEl", "fn", "boundArgs", "errorType", "TypeError", "SyntaxError", "bind<PERSON><PERSON><PERSON>", "isElement", "KEY", "PREFIX", "globalContext", "self", "global", "this", "key", "operation", "fallback", "hasSupport", "storage", "e", "setItem", "removeItem", "err", "isSupported", "hasCookies", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "<PERSON><PERSON><PERSON><PERSON>", "remove", "COOKIE", "tmp", "document", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "ttl", "path", "domain", "secure", "arguments", "length", "isSet", "set", "get", "encodeURIComponent", "toUTCString", "decodeURIComponent", "split", "hasLocalStorage", "wrap", "hasSessionStorage", "parse", "input", "JSON", "obj", "isObjectLike", "proto", "isObject", "parseFloat", "hasStorage", "hasSessionSupport", "hasCookiesSupport", "getItem", "options", "getStorageType", "get<PERSON><PERSON><PERSON>", "useAll", "localValue", "useLocal", "localStorage", "undefined", "cookieVal", "useCookie", "sessionVal", "useSession", "sessionStorage", "globalValue", "data", "saveValue", "stringify", "<PERSON><PERSON><PERSON><PERSON>", "LOCAL_STORAGE", "format", "SESSION_STORAGE", "GLOBAL", "values", "useType", "opts", "location", "current", "previous"], "mappings": "yOAOaA,EAAY,YAEZC,EAAS,SAaTC,EAAM,MAfM,6BAwCsBC,YAelCC,EAvDY,6BA2EzB,WAAcC,EAAQC,GACpB,SAASC,OAAO,GAAGF,KAAYC,EAAEE,MAAM,GA5EhB,0BA+DiCC,KAMlCL,GAA6B,WAAhBM,OAAOC,MArEnB,oCAqEiDC,IAAkCA,UAAUC,YAA4BD,UAAUC,UAAUC,SAAS,YAAcF,UAAUC,UAAUC,SAAS,gBAU5MC,EAAKC,KAAK,KAAM,eACxBC,EAAQF,EAAKC,KAAK,KAAM,0BAiBNE,EAAKC,sBAC3B,MAlB4B,SAOFD,GAC1B,SAAWA,GAAaE,EAlFN,QAJE,4BA+VGC,GACvB,SAAkBA,EAAEC,aAAeD,EAAEC,YAAYX,KAAO,KAzQvBY,CAASL,GAAOM,OAAOC,UAAUC,SAASC,KAAKT,GAAKV,MAAM,GAAI,GASlFoB,CAAYV,GAEzB,SAAuBD,EAAMY,GAAQA,aAUvBC,EAAMZ,GACpB,kBAAsBY,QAQEC,EAAOf,KAAK,KAzHd,YAgIXgB,EAAWD,EAAOf,KAAK,KA/Hd,UAsITiB,EAAcF,EAAOf,KAAK,KArId,aAKH,WA8JCK,GACrB,cAAOA,aA4QUa,EAASC,GAC1B,GAAqB,oBAAYC,EAAOD,GAAQ,SAEhD,GAAIA,eAA0B,SAC9B,MAAiBE,EAAQ,MAAY,KAErC,YAnCsBhB,GACtB,2BAA8BW,EAASX,EAAEiB,UAAYjB,EAAEC,sBAvNhCiB,GACvB,MA1LoB,WA0LbF,EAAQE,KAAkBC,MAAMD,GAsN+BE,CAASpB,EAAEC,YAAYoB,iBAkCzFC,CAAQR,GACV,KAAOA,GAAO,CACZ,GAAIE,EAAQF,KAAWS,EACrB,SAEAT,EAAQX,OAAOqB,eAAeV,uBA8JZW,EAASjB,GACjC,MAAaiB,sBAA8BA,0BAC3C,UAAcjB,WAUWiB,EAASjB,GAClC,gBADkCA,IAAAA,EAAO,IAClCiB,GAAWA,EAAQC,WAAalB,EAAKmB,cAVrCC,CAAWH,EAASjB,GADAqB,aAcXC,oCAChB,kBACE,sDAAsBC,KAvdDrB,EAAOf,KAAK,KAnJd,WAyJCe,EAAOf,KAAK,KArJd,UA4bKqC,EAAUrC,KAAK,KAAMsC,WAEnBD,EAAUrC,KAAK,KAAMuC,aAiL5BC,EAASC,EA9lBX,QAqmBID,EAASC,EAnmBX,UA0mBCD,EAASC,EA3mBX,SAknBGD,EAASC,EAhnBX,gBC9BA,SAETC,EAAMC,aAENC,gBAAiC3D,GAAU4D,KAAKA,OAASA,MAAQA,sBAA4B5D,GAAU6D,OAAMA,SAAaA,QAAUA,aAAAA,EAAWC,WAWxIC,GAClB,SAAqBN,GAAKM,cASRA,EAAK7B,GACvB,SAAqBuB,GAAKM,GAAO7B,aAOZ6B,YACAN,GAAKM,cASPnC,EAAMoC,EAAWC,GACpC,MACA,IACE,GAAIC,EAAWtC,GAAO,CACpB,MAAgBnB,OAAOmB,GACvBsB,EAAKiB,EAAQH,GAAWjD,KAAKoD,UAEzBC,cACKH,IA5CIR,KACjBE,EAAcF,GAAO,IA8CvB,MAAc,GAAA,WACa7B,GACzB,YAAiBA,KAAU7B,EACzB,SAAa6B,GAEf,IACE,MAAgBnB,OAAOmB,GAEvBuC,EAAQE,QAAQtE,EAAWA,GAC3BoE,EAAQG,WAAWvE,SACZwE,GACP,SAAa3C,IAAAA,WAEFA,IAAAA,QCnEO,SAElB4C,EAAcC,IAOLC,EAAYC,EAQZC,EAAYD,EAAAA,WAMIjE,GAC3B,SAAqBiE,EAAOjE,EAAM,IAAK,GAAKmE,EAAOnE,gBAQnD,YAAW8D,EACT,SAEF,MAAYM,eACZ,IAEEH,EAAOI,EAAKA,GACZP,GAAgD,IAAlCQ,SAASL,OAAOM,QAAQF,GAEtCG,EAAaH,SACNX,GACPI,yBA0BY9D,EAAMwB,EAAOiD,EAAKC,EAAMC,EAAQC,GAC9C,GAAsB,4BACtB,MAAcC,UAAUC,OAAS,EAIjC,WAFIhB,IAAwBiB,EAASC,EAAIhF,EAAMwB,GAASyD,EAAIjF,IAExD+E,EACKT,SAASL,OAASjE,EAAO,IAAMkF,mBAAmB1D,IAEpDiD,EAAY,aAAe,UAAU,SAAoB,IAANA,GAAaU,eAEhET,EAAa,UAAYA,EAAjB,KAERC,EAAe,YAAcA,EAAnB,KAEVC,EAAe,WAAL,IANH,IASPQ,qBAAqB,KAAOd,SAASL,QAAQoB,MAAM,KAAOrF,EAAO,KAAK,IAAM,IAAIqF,MAAM,KAAK,WCvF9E,eAMhBC,EAAkB9B,EAAWnD,KAAK,KANlB,gBAaNkF,EAbM,eAac,UAAWN,GAQ/BM,EArBM,eAqBc,UAAWP,GAM5BO,EA3BG,eA2BiB,aAAcpB,SC3B7B,iBAMlBqB,EAAoBhC,EAAWnD,KAAK,KANlB,2BCIAoF,EAAMC,GAC5B,IAAIlE,EAAQkE,EACZ,IAEE,GAAc,UADdlE,EAAQmE,KAAKF,MAAMC,IACG,SACtB,GAAc,UAAVlE,EAAmB,SACvB,YLyNqBoE,GACvB,aAU2BA,GAC3B,WAxOoB,oBAwO4B,OAARA,GAXnCC,CAAaD,GAAM,SAGxB,IADA,MAAYA,EAC4B,OAAjC/E,OAAOqB,eAAe4D,IAC3BA,EAAQjF,OAAOqB,eAAe4D,GAGhC,cAAc5D,eAAe0D,KAASE,EKjOhCC,CAASvE,GAAQ,OAAOA,EACxBwE,WAAWxE,KAAWA,IACxBA,EAAQwE,WAAWxE,IAErB,MAAOkC,IACT,GAAc,OAAVlC,GAA4B,KAAVA,EAGtB,OAAOA,EDLc+D,EAbC,iBAaqB,UAAWN,GAQjCM,EArBC,iBAqBqB,UAAWP,GAM9BO,EA3BF,iBA2BwB,aAAcpB,GErB9D,IAAM8B,EAAaX,IACbY,EAAoBV,IACpBW,EAAoBpC,aASVqC,EAAQ/C,EAAKgD,GAC3B,GAAKhD,EAAL,CACA,IAAMnC,EAAOoF,EAAeD,GACtBE,GAAYC,EAAOtF,GAGnBuF,EAAaC,EAASxF,GAAQuE,EAAMkB,aAAaP,QAAQ/C,SAAQuD,EACvE,GAAIL,IAAajF,EAAYmF,GAC3B,OAAOA,EAIT,IAAMI,EAAYC,EAAU5F,GAAQuE,EAAMzB,EAAUX,SAAQuD,EAC5D,GAAIL,GAAYM,EACd,OAAOA,EAIT,IAAME,EAAaC,EAAW9F,GAAQuE,EAAMwB,eAAeb,QAAQ/C,SAAQuD,EAC3E,GAAIL,GAAYQ,EACd,OAAOA,EAIT,IAAMG,EAAcjC,EAAI5B,GAExB,OAAOkD,EAAWW,EAAc,CAC9BP,aAAcF,EACdQ,eAAgBF,EAChB9C,OAAQ4C,EACR1D,OAAQ+D,aAYIvD,EAAQN,EAAK7B,EAAO6E,GAClC,GAAKhD,IAAO/B,EAAYE,GAAxB,CAGA,IAAM2F,EAAO,GACPjG,EAAOoF,EAAeD,GACtBe,EAAYzB,KAAK0B,UAAU7F,GAC3B8F,GAAYd,EAAOtF,GAGzB,OAAIwF,EAASxF,KAEXiG,EAAKI,GAAiBC,EAAOD,EAAe/F,EAAOiE,EAAMkB,aAAaP,QAAQ/C,KAE9EsD,aAAahD,QAAQN,EAAK+D,GACtBE,GACKH,EAAKI,GAKZT,EAAU5F,KAEZiG,EAAK/C,GAAUoD,EAAOpD,EAAQ5C,EAAOiE,EAAMzB,EAAUX,KAErDa,EAAUb,EAAK+D,GACXE,GACKH,EAAK/C,GAKZ4C,EAAW9F,KAEbiG,EAAKM,GAAmBD,EAAOC,EAAiBjG,EAAOiE,EAAMwB,eAAeb,QAAQ/C,KAEpF4D,eAAetD,QAAQN,EAAK+D,GACxBE,GACKH,EAAKM,IAKhBN,EAAKO,GAAUF,EAAOE,EAAQlG,EAAOyD,EAAI5B,IAEzC2B,EAAI3B,EAAK7B,GAED8F,EAAYH,EAAKO,GAAUP,aASrBvD,EAAWP,EAAKgD,GAC9B,GAAKhD,EAAL,CACA,IAAMnC,EAAOoF,EAAeD,GACtBsB,EAASvB,EAAQ/C,ENjGN,KMmGX8D,EAAO,GAqBb,OAnBK7F,EAAYqG,EAAOhB,eAAiBD,EAASxF,KAChDyF,aAAa/C,WAAWP,GACxB8D,EAAKI,GAAiBI,EAAOhB,eAG1BrF,EAAYqG,EAAO1D,SAAW6C,EAAU5F,KAC3CsD,EAAanB,GACb8D,EAAK/C,GAAUuD,EAAO1D,SAGnB3C,EAAYqG,EAAOV,iBAAmBD,EAAW9F,KACpD+F,eAAerD,WAAWP,GAC1B8D,EAAKM,GAAmBE,EAAOV,iBAG5B3F,EAAYqG,EAAOxE,SAajByE,EAbsC1G,EAarBwG,KAZtBvD,EAAOd,GACP8D,EAAKO,GAAUC,EAAOxE,QAEjBgE,GAGT,SAASb,EAAeuB,GACtB,OAAKA,EACExG,EAASwG,GAAQA,EAAOA,EAAKpE,QADlBlE,EAQpB,SAASmH,EAASjD,GAEhB,OAAOwC,GAAc2B,EAAQnE,EAAS8D,GAGxC,SAAST,EAAUrD,GAEjB,OAAO0C,GAAqByB,EAAQnE,EAASW,GAG/C,SAAS4C,EAAWvD,GAElB,OAAOyC,GAAqB0B,EAAQnE,EAASgE,GAG/C,SAASjB,EAAO/C,GACd,MNpJiB,MMoJVA,GAA+B,QAAZA,EAG5B,SAASmE,EAAQnE,EAASvC,GACxB,OAAQuC,IAAYlE,GAAOkE,IAAYvC,GAAQsF,EAAO/C,GAUxD,SAAS+D,EAAOM,EAAUC,EAASC,GACjC,MAAO,CAAEF,SAAAA,EAAUC,QAAAA,EAASC,SAAAA,GA4B9B,MAAe,CACbrE,QAAAA,EACAyC,QAAAA,EACAxC,WAAAA,SNlMiB"}