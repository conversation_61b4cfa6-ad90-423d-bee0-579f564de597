var analyticsGa=function(e){"use strict";function t(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,a)}return n}function n(e){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?t(Object(r),!0).forEach((function(t){a(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):t(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var r={},o="https://www.googletagmanager.com/gtag/js",i={debug_mode:!1,send_page_view:!1,anonymize_ip:!1,allow_google_signals:!0,allow_ad_personalization_signals:!0,cookie_flags:""},c={gtagName:"gtag",dataLayerName:"ga4DataLayer",measurementIds:[],gtagConfig:i};function g(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=0,a=s(e.measurementIds),g=n(n({},c),e);return{name:"google-analytics",config:g,initialize:function(e){var t=e.config,c=e.instance,g=t.dataLayerName,s=t.customScriptSrc,d=t.gtagName,l=t.gtagConfig,w=t.debug,p=t.nonce,f=g?"&l=".concat(g):"",m=s||"".concat(o,"?id=").concat(a[0]).concat(f);if(!u(m)){var v=document.createElement("script");v.async=!0,v.src=m,p&&v.setAttribute("nonce",p),document.body.appendChild(v)}window[g]||(window[g]=window[g]||[]),window[d]||(window[d]=function(){window[g].push(arguments)}),window[d]("js",new Date);var y=n(n({},i),l||{});!0===w?y.debug_mode=!0:delete y.debug_mode;var b=(c.user()||{}).traits||{};Object.keys(b).length&&window[d]("set","user_properties",b);for(var h=0;h<a.length;h++)r[a[h]]||(window[d]("config",a[h],y),r[a[h]]=!0)},identify:function(e){var t=e.payload,n=e.config.gtagName;window[n]&&a.length&&(t.userId&&window[n]("set",{user_id:t.userId}),Object.keys(t.traits).length&&window[n]("set","user_properties",t.traits))},page:function(e){var r=e.payload,o=e.config,i=e.instance,c=o.gtagName,g=o.gtagConfig;if(window[c]&&a.length){var s=r.properties,u=s.send_to,l=i.getState("context.campaign"),w={page_title:s.title,page_location:s.url,page_path:s.path||document.location.pathname,page_hash:s.hash,page_search:s.page_search,page_referrer:s.referrer},p=d(l),f=i.user("userId"),m=n(n(n(n({},u?{send_to:u}:{}),w),p),f?{user_id:f}:{});g&&g.send_page_view&&0===t||window[c]("event","page_view",m),t++}},track:function(e){var t=e.payload,r=e.config,o=e.instance,i=t.properties,c=t.event,g=o.getState("context.campaign"),s=r.gtagName;if(window[s]&&a.length){var u=d(g),l=o.user("userId"),w=n(n(n({},i),u),l?{user_id:l}:{});window[s]("event",c,w)}},loaded:function(){var e=g.dataLayerName,t=g.customScriptSrc,n=e&&window[e]&&Array.prototype.push===window[e].push;return u(t||o)&&n},methods:{addTag:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};window[g.gtagName]&&(window[g.gtagName]("config",e,t),a&&!a.includes(e)&&(a=a.concat(e)))},disable:function(e){for(var t=e?s(e):a,n=0;n<a.length;n++){var r=a[n];t.includes(r)&&(window["ga-disable-".concat(r)]=!0)}},enable:function(e){for(var t=e?s(e):a,n=0;n<a.length;n++){var r=a[n];t.includes(r)&&(window["ga-disable-".concat(r)]=!1)}}}}}function s(e){if(!e)throw new Error("No GA Measurement ID defined");if(Array.isArray(e))return e;if("string"==typeof e)return[e];throw new Error("GA Measurement ID must be string or array of strings")}function d(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t={},n=e.id,a=e.name,r=e.source,o=e.medium,i=e.content,c=e.keyword;return n&&(t.campaignId=n),a&&(t.campaignName=a),r&&(t.campaignSource=r),o&&(t.campaignMedium=o),i&&(t.campaignContent=i),c&&(t.campaignKeyword=c),t}function u(e){var t=document.querySelectorAll("script[src]"),n=new RegExp("^".concat(e));return Boolean(Object.values(t).filter((function(e){return n.test(e.src)})).length)}var l=g,w=g;return e.default=l,e.init=w,Object.defineProperty(e,"__esModule",{value:!0}),e}({});