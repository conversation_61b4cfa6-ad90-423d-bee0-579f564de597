var utilSessionStorage=function(n){var e="undefined",t="object";"undefined"==typeof process||process;var r="undefined"!=typeof document;function o(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,r&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var i=o.bind(null,"toUpperCase"),u=o.bind(null,"toLowerCase");function l(n,e){void 0===e&&(e=!0);var t=function(n){return f(n)?i("null"):"object"==typeof n?function(n){return a(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?u(t):t}function c(n,e){return typeof e===n}var a=c.bind(null,"function"),s=c.bind(null,"string");function f(n){return null===n}function d(n,e){if("object"!=typeof e||f(e))return!1;if(e instanceof n)return!0;var t=l(new n(""));if(function(n){return n instanceof Error||s(n.message)&&n.constructor&&function(n){return"number"===l(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(l(e)===t)return!0;e=Object.getPrototypeOf(e)}return!1}function v(n,e){var t=n instanceof Element||n instanceof HTMLDocument;return t&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):t}function b(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}c.bind(null,"undefined"),c.bind(null,"boolean"),c.bind(null,"symbol"),d.bind(null,TypeError),d.bind(null,SyntaxError),b(v,"form"),b(v,"button"),b(v,"input"),b(v,"select");var m="__global__",p=typeof self===t&&self.self===self&&self||typeof global===t&&global.global===global&&global||void 0;function g(n,e,t){var r;try{if(S(n)){var o=window[n];r=o[e].bind(o)}}catch(n){}return r||t}p[m]||(p[m]={});var y={};function S(n){if(typeof y[n]!==e)return y[n];try{var t=window[n];t.setItem(e,e),t.removeItem(e)}catch(e){return y[n]=!1}return y[n]=!0}var I="sessionStorage",j=S.bind(null,I),w=g(I,"getItem",function(n){return p[m][n]}),E=g(I,"setItem",function(n,e){return p[m][n]=e}),A=g(I,"removeItem",function(n){delete p[m][n]});return n.SESSION_STORAGE=I,n.getSessionItem=w,n.hasSessionStorage=j,n.removeSessionItem=A,n.setSessionItem=E,n}({});
//# sourceMappingURL=session-storage-utils.js.map
