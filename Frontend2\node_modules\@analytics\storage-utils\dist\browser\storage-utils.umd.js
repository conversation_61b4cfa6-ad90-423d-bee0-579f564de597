!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e||self).utilStorage={})}(this,function(e){var t="undefined",n="object",o="any";"undefined"==typeof process||process;var r="undefined"!=typeof document;function i(e,t){return t.charAt(0)[e]()+t.slice(1)}"undefined"!=typeof Deno&&Deno,r&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var a=i.bind(null,"toUpperCase"),u=i.bind(null,"toLowerCase");function l(e,t){void 0===t&&(t=!0);var n=function(e){return d(e)?a("null"):"object"==typeof e?function(e){return s(e.constructor)?e.constructor.name:null}(e):Object.prototype.toString.call(e).slice(8,-1)}(e);return t?u(n):n}function c(e,t){return typeof t===e}var s=c.bind(null,"function"),f=c.bind(null,"string"),g=c.bind(null,"undefined");function d(e){return null===e}function m(e,t){if("object"!=typeof t||d(t))return!1;if(t instanceof e)return!0;var n=l(new e(""));if(function(e){return e instanceof Error||f(e.message)&&e.constructor&&function(e){return"number"===l(e)&&!isNaN(e)}(e.constructor.stackTraceLimit)}(t))for(;t;){if(l(t)===n)return!0;t=Object.getPrototypeOf(t)}return!1}function v(e,t){var n=e instanceof Element||e instanceof HTMLDocument;return n&&t?function(e,t){return void 0===t&&(t=""),e&&e.nodeName===t.toUpperCase()}(e,t):n}function p(e){var t=[].slice.call(arguments,1);return function(){return e.apply(void 0,[].slice.call(arguments).concat(t))}}c.bind(null,"boolean"),c.bind(null,"symbol"),m.bind(null,TypeError),m.bind(null,SyntaxError),p(v,"form"),p(v,"button"),p(v,"input"),p(v,"select");var b="global",S="__global__",y=typeof self===n&&self.self===self&&self||typeof global===n&&global.global===global&&global||void 0;function I(e){return y[S][e]}function O(e,t){return y[S][e]=t}function k(e){delete y[S][e]}function h(e,t,n){var o;try{if(C(e)){var r=window[e];o=r[t].bind(r)}}catch(e){}return o||n}y[S]||(y[S]={});var j={};function C(e){if(typeof j[e]!==t)return j[e];try{var n=window[e];n.setItem(t,t),n.removeItem(t)}catch(t){return j[e]=!1}return j[e]=!0}var w="cookie",A=T(),L=x,E=x;function N(e){return A?x(e,"",-1):k(e)}function T(){if(void 0!==A)return A;var e="cookiecookie";try{x(e,e),A=-1!==document.cookie.indexOf(e),N(e)}catch(e){A=!1}return A}function x(e,t,n,o,r,i){if("undefined"!=typeof window){var a=arguments.length>1;return!1===A&&(a?O(e,t):I(e)),a?document.cookie=e+"="+encodeURIComponent(t)+(n?"; expires="+new Date(+new Date+1e3*n).toUTCString()+(o?"; path="+o:"")+(r?"; domain="+r:"")+(i?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+e+"=")[1]||"").split(";")[0])}}var _="localStorage",D=C.bind(null,"localStorage");h("localStorage","getItem",I),h("localStorage","setItem",O),h("localStorage","removeItem",k);var U="sessionStorage",P=C.bind(null,"sessionStorage");function R(e){var t=e;try{if("true"===(t=JSON.parse(e)))return!0;if("false"===t)return!1;if(function(e){if(!function(e){return e&&("object"==typeof e||null!==e)}(e))return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}(t))return t;parseFloat(t)===t&&(t=parseFloat(t))}catch(e){}if(null!==t&&""!==t)return t}h("sessionStorage","getItem",I),h("sessionStorage","setItem",O),h("sessionStorage","removeItem",k);var G=D(),F=P(),J=T();function B(e,t){if(e){var n=M(t),o=!Q(n),r=Y(n)?R(localStorage.getItem(e)):void 0;if(o&&!g(r))return r;var i=q(n)?R(L(e)):void 0;if(o&&i)return i;var a=z(n)?R(sessionStorage.getItem(e)):void 0;if(o&&a)return a;var u=I(e);return o?u:{localStorage:r,sessionStorage:a,cookie:i,global:u}}}function H(e,t,n){if(e&&!g(t)){var o={},r=M(n),i=JSON.stringify(t),a=!Q(r);return Y(r)&&(o[_]=W(_,t,R(localStorage.getItem(e))),localStorage.setItem(e,i),a)?o[_]:q(r)&&(o[w]=W(w,t,R(L(e))),E(e,i),a)?o[w]:z(r)&&(o[U]=W(U,t,R(sessionStorage.getItem(e))),sessionStorage.setItem(e,i),a)?o[U]:(o[b]=W(b,t,I(e)),O(e,t),a?o[b]:o)}}function K(e,t){if(e){var n=M(t),o=B(e,"*"),r={};return!g(o.localStorage)&&Y(n)&&(localStorage.removeItem(e),r[_]=o.localStorage),!g(o.cookie)&&q(n)&&(N(e),r[w]=o.cookie),!g(o.sessionStorage)&&z(n)&&(sessionStorage.removeItem(e),r[U]=o.sessionStorage),!g(o.global)&&V(n,b)&&(k(e),r[b]=o.global),r}}function M(e){return e?f(e)?e:e.storage:o}function Y(e){return G&&V(e,_)}function q(e){return J&&V(e,w)}function z(e){return F&&V(e,U)}function Q(e){return"*"===e||"all"===e}function V(e,t){return e===o||e===t||Q(e)}function W(e,t,n){return{location:e,current:t,previous:n}}var X={setItem:H,getItem:B,removeItem:K};e.ALL="*",e.ANY=o,e.COOKIE=w,e.GLOBAL=b,e.LOCAL_STORAGE=_,e.SESSION_STORAGE=U,e.default=X,e.getCookie=L,e.getItem=B,e.globalContext=y,e.hasCookies=T,e.hasLocalStorage=D,e.hasSessionStorage=P,e.removeCookie=N,e.removeItem=K,e.setCookie=E,e.setItem=H});
//# sourceMappingURL=storage-utils.umd.js.map
