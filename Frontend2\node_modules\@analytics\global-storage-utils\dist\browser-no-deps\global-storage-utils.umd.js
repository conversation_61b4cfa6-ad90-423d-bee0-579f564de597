!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@analytics/type-utils")):"function"==typeof define&&define.amd?define(["exports","@analytics/type-utils"],t):t((e||self).utilGlobalStorage={},e.typeUtils)}(this,function(e,t){var o="global",n=t.PREFIX+o+t.PREFIX,l=typeof self===t.OBJECT&&self.self===self&&self||typeof global===t.OBJECT&&global.global===global&&global||void 0;l[n]||(l[n]={});var i={};function r(e){if(typeof i[e]!==t.UNDEFINED)return i[e];try{var o=window[e];o.setItem(t.UNDEFINED,t.UNDEFINED),o.removeItem(t.UNDEFINED)}catch(t){return i[e]=!1}return i[e]=!0}e.GLOBAL=o,e.KEY=n,e.get=function(e){return l[n][e]},e.globalContext=l,e.hasSupport=r,e.remove=function(e){delete l[n][e]},e.set=function(e,t){return l[n][e]=t},e.wrap=function(e,t,o){var n;try{if(r(e)){var l=window[e];n=l[t].bind(l)}}catch(e){}return n||o}});
//# sourceMappingURL=global-storage-utils.umd.js.map
