!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n||self).utilSessionStorage={})}(this,function(n){var e="undefined",t="object";"undefined"==typeof process||process;var o="undefined"!=typeof document;function r(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,o&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var i=r.bind(null,"toUpperCase"),u=r.bind(null,"toLowerCase");function l(n,e){void 0===e&&(e=!0);var t=function(n){return a(n)?i("null"):"object"==typeof n?function(n){return c(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?u(t):t}function f(n,e){return typeof e===n}var c=f.bind(null,"function"),s=f.bind(null,"string");function a(n){return null===n}function d(n,e){if("object"!=typeof e||a(e))return!1;if(e instanceof n)return!0;var t=l(new n(""));if(function(n){return n instanceof Error||s(n.message)&&n.constructor&&function(n){return"number"===l(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(l(e)===t)return!0;e=Object.getPrototypeOf(e)}return!1}function p(n,e){var t=n instanceof Element||n instanceof HTMLDocument;return t&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):t}function b(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}f.bind(null,"undefined"),f.bind(null,"boolean"),f.bind(null,"symbol"),d.bind(null,TypeError),d.bind(null,SyntaxError),b(p,"form"),b(p,"button"),b(p,"input"),b(p,"select");var v="__global__",m=typeof self===t&&self.self===self&&self||typeof global===t&&global.global===global&&global||void 0;function g(n,e,t){var o;try{if(S(n)){var r=window[n];o=r[e].bind(r)}}catch(n){}return o||t}m[v]||(m[v]={});var y={};function S(n){if(typeof y[n]!==e)return y[n];try{var t=window[n];t.setItem(e,e),t.removeItem(e)}catch(e){return y[n]=!1}return y[n]=!0}var j="sessionStorage",I=S.bind(null,j),w=g(j,"getItem",function(n){return m[v][n]}),h=g(j,"setItem",function(n,e){return m[v][n]=e}),E=g(j,"removeItem",function(n){delete m[v][n]});n.SESSION_STORAGE=j,n.getSessionItem=w,n.hasSessionStorage=I,n.removeSessionItem=E,n.setSessionItem=h});
//# sourceMappingURL=session-storage-utils.umd.js.map
