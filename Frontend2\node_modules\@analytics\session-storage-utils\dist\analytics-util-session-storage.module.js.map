{"version": 3, "file": "analytics-util-session-storage.module.js", "sources": ["../src/index.js"], "sourcesContent": ["import { get, set, remove, hasSupport, wrap } from '@analytics/global-storage-utils'\n\nconst SESSION_STORAGE = 'sessionStorage'\n\n/**\n * Check if browser has access to sessionStorage\n * @returns {Boolean}\n */\nconst hasSessionStorage = hasSupport.bind(null, SESSION_STORAGE)\n\n/**\n * Get value from sessionStorage or fallback to global window\n * @param {string} key - Key of value to get\n * @returns {*} value\n */\nconst getSessionItem = wrap(SESSION_STORAGE, 'getItem', get)\n\n/**\n * Set value to sessionStorage or fallback to global window\n * @param {string} key - Key of value to set\n * @param {*} value \n * @returns value\n */\nconst setSessionItem = wrap(SESSION_STORAGE, 'setItem', set)\n\n/**\n * Remove value from sessionStorage or fallback to global window\n * @param {string} key - Key of value to remove\n */\nconst removeSessionItem = wrap(SESSION_STORAGE, 'removeItem', remove)\n\nexport {\n  SESSION_STORAGE,\n  hasSessionStorage,\n  getSessionItem,\n  setSessionItem,\n  removeSessionItem\n}"], "names": ["SESSION_STORAGE", "hasSessionStorage", "hasSupport", "bind", "getSessionItem", "wrap", "get", "setSessionItem", "set", "removeSessionItem", "remove"], "mappings": "qGAEMA,IAAAA,EAAkB,iBAMlBC,EAAoBC,EAAWC,KAAK,KANlB,kBAalBC,EAAiBC,EAbC,iBAaqB,UAAWC,GAQlDC,EAAiBF,EArBC,iBAqBqB,UAAWG,GAMlDC,EAAoBJ,EA3BF,iBA2BwB,aAAcK"}