/**
 * Get host domain of url
 * @param  {String} url - href of page
 * @return {String} hostname of page
 *
 * @example
 *  getDomainHost('https://subdomain.my-site.com/')
 *  > subdomain.my-site.com
 */
export function getDomainHost(url: string): string;
/**
 * Get host domain of url
 * @param  {String} url - href of page
 * @return {String} base hostname of page
 *
 * @example
 *  getDomainBase('https://subdomain.my-site.com/')
 *  > my-site.com
 */
export function getDomainBase(url: string): string;
/**
 * Remove TLD from domain string
 * @param  {String} baseDomain - host name of site
 * @return {String}
 * @example
 *  trimTld('google.com')
 *  > google
 */
export function trimTld(baseDomain: string): string;
declare namespace _default {
    export { trimTld };
    export { getDomainBase };
    export { getDomainHost };
}
export default _default;
