{"name": "@analytics/google-analytics", "version": "1.1.0", "description": "Google analytics v4 plugin for 'analytics' module", "projectMeta": {"provider": {"name": "Google Analytics", "url": "https://analytics.google.com/analytics/web/"}, "windowGlobal": "analyticsGa", "platforms": {"browser": "./src/browser.js"}}, "keywords": ["analytics", "analytics-project", "analytics-plugin", "GA", "google-analytics", "Google Analytics"], "author": "<PERSON>, <PERSON>", "license": "MIT", "scripts": {"types": "../../node_modules/.bin/jsdoc -t ../../node_modules/tsd-jsdoc/dist -r ./src/ -d temp-types", "build": "node ../../scripts/build/index.js", "watch": "node ../../scripts/build/_watch.js", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish", "es": "../../node_modules/.bin/babel-node ./testBabel.js"}, "main": "lib/analytics-plugin-ga.cjs.js", "globalName": "analyticsGa", "jsnext:main": "lib/analytics-plugin-ga.es.js", "module": "lib/analytics-plugin-ga.es.js", "browser": {"./lib/analytics-plugin-ga.cjs.js": "./lib/analytics-plugin-ga.browser.cjs.js", "./lib/analytics-plugin-ga.es.js": "./lib/analytics-plugin-ga.browser.es.js"}, "files": ["dist", "lib", "README.md"], "homepage": "https://github.com/DavidWells/analytics#readme", "repository": {"type": "git", "url": "git+https://github.com/DavidWells/analytics.git"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/preset-env": "^7.3.1"}, "gitHead": "8730d1b0e462f7bd2ae1b4cadd175c166ea5ffef"}