import{PREFIX as t,OBJECT as o,UNDEFINED as e}from"@analytics/type-utils";const n="global",l=t+"global"+t,r=typeof self===o&&self.self===self&&self||typeof global===o&&global.global===global&&global||void 0;function f(t){return r[l][t]}function c(t,o){return r[l][t]=o}function i(t){delete r[l][t]}function s(t,o,e){let n;try{if(a(t)){const e=window[t];n=e[o].bind(e)}}catch(t){}return n||e}r[l]||(r[l]={});const u={};function a(t){if(typeof u[t]!==e)return u[t];try{const o=window[t];o.setItem(e,e),o.removeItem(e)}catch(o){return u[t]=!1}return u[t]=!0}export{n as GLOBAL,l as KEY,f as get,r as globalContext,a as hasSupport,i as remove,c as set,s as wrap};
//# sourceMappingURL=analytics-util-global-storage.modern.js.map
