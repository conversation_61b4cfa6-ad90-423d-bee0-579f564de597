!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("@analytics/global-storage-utils")):"function"==typeof define&&define.amd?define(["exports","@analytics/global-storage-utils"],t):t((e||self).utilSessionStorage={},e.globalStorageUtils)}(this,function(e,t){var o="sessionStorage",s=t.hasSupport.bind(null,o),i=t.wrap(o,"getItem",t.get),n=t.wrap(o,"setItem",t.set),a=t.wrap(o,"removeItem",t.remove);e.SESSION_STORAGE=o,e.getSessionItem=i,e.hasSessionStorage=s,e.removeSessionItem=a,e.setSessionItem=n});
//# sourceMappingURL=analytics-util-session-storage.umd.js.map
