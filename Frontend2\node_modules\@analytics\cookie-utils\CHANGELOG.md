# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [0.2.12](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.11...@analytics/cookie-utils@0.2.12) (2023-05-27)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.11](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.10...@analytics/cookie-utils@0.2.11) (2023-05-27)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.10](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.9...@analytics/cookie-utils@0.2.10) (2022-03-18)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.9](https://github.com/<PERSON>Wells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.8...@analytics/cookie-utils@0.2.9) (2022-02-05)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.8](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.7...@analytics/cookie-utils@0.2.8) (2021-12-12)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.7](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.6...@analytics/cookie-utils@0.2.7) (2021-10-17)


### Bug Fixes

* change temp key to value cookie value ([1708369](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/commit/1708369)), closes [#223](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/issues/223)





## [0.2.6](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.5...@analytics/cookie-utils@0.2.6) (2021-07-28)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.5](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.4...@analytics/cookie-utils@0.2.5) (2021-07-20)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.4](https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-cookie/compare/@analytics/cookie-utils@0.2.3...@analytics/cookie-utils@0.2.4) (2021-07-20)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.3](https://github.com/DavidWells/analytics/compare/@analytics/cookie-utils@0.2.2...@analytics/cookie-utils@0.2.3) (2020-04-16)

**Note:** Version bump only for package @analytics/cookie-utils





## [0.2.2](https://github.com/DavidWells/analytics/compare/@analytics/cookie-utils@0.2.0...@analytics/cookie-utils@0.2.2) (2019-10-03)


### Bug Fixes

* hasCookie support return true ([3d93380](https://github.com/DavidWells/analytics/commit/3d93380))
* noOp in server ([a09f1f7](https://github.com/DavidWells/analytics/commit/a09f1f7))





# 0.2.0 (2019-09-10)


### Features

* **cookies:** add cookie util ([7b0ecfa](https://github.com/DavidWells/analytics/commit/7b0ecfa))





## [0.1.4](https://github.com/DavidWells/analytics/compare/<EMAIL>-util-cookie@0.1.4) (2019-09-10)

**Note:** Version bump only for package analytics-util-cookie





## [0.1.3](https://github.com/DavidWells/analytics/compare/<EMAIL>-util-cookie@0.1.3) (2019-09-10)

**Note:** Version bump only for package analytics-util-cookie





## [0.1.2](https://github.com/DavidWells/analytics/compare/<EMAIL>-util-cookie@0.1.2) (2019-08-14)

**Note:** Version bump only for package analytics-util-cookie





## [0.1.1](https://github.com/DavidWells/analytics/compare/<EMAIL>-util-cookie@0.1.1) (2019-08-14)

**Note:** Version bump only for package analytics-util-cookie





# 0.1.0 (2019-08-14)


### Features

* **cookies:** add cookie util ([7b0ecfa](https://github.com/DavidWells/analytics/commit/7b0ecfa))
