{"version": 3, "file": "analytics-util-cookie.umd.js", "sources": ["../src/index.js"], "sourcesContent": ["import { get, set, remove } from '@analytics/global-storage-utils'\n\nexport const COOKIE = 'cookie'\n\nlet isSupported = hasCookies()\n\n/**\n * Get a cookie value\n * @param  {string} name - key of cookie\n * @return {string} value of cookie\n */\nexport const getCookie = cookie\n\n/**\n * Set a cookie value\n * @param {string} name  - key of cookie\n * @param {string} value - value of cookie\n * @param {string} days  - days to keep cookie\n */\nexport const setCookie = cookie\n\n/**\n * Remove a cookie value.\n * @param {string} name  - key of cookie\n */\nexport function removeCookie(name) {\n  return isSupported ? cookie(name, '', -1) : remove(name) \n}\n\n/**\n * Check if browser has cookie support\n * @returns {boolean}\n */\nexport function hasCookies() {\n  if (typeof isSupported !== 'undefined') {\n    return isSupported\n  }\n  const tmp = COOKIE + COOKIE\n  try {\n    // Try to set cookie\n    cookie(tmp, tmp)\n    isSupported = document.cookie.indexOf(tmp) !== -1\n    // Cleanup cookie\n    removeCookie(tmp)\n  } catch (e) {\n    isSupported = false\n  }\n  return isSupported\n}\n\n/** \n  * Cookie setter & getter\n  * @version    1.0.4\n  * @date       2015-03-13\n  * @stability  3 - Stable\n  * <AUTHOR> <<EMAIL>>\n  * @license    MIT License\n  * Modified by David Wells\n  * @param {string} name \n  * @param {*} value \n  * @param {*} ttl - Time to live in seconds\n  * @param {*} path - Cookie domain\n  * @param {*} domain - Cookie domain\n  * @param {Boolean} secure - secure cookie\n  * @returns {*} value\n  * @example\n    cookie('test', 'a') // set\n    cookie('test', 'a', 60*60*24, '/api', '*.example.com', true) // complex set - cookie(name, value, ttl, path, domain, secure)\n    cookie('test') // get\n    cookie('test', '', -1) // destroy\n*/\nfunction cookie(name, value, ttl, path, domain, secure) {\n  if (typeof window === 'undefined') return\n  const isSet = arguments.length > 1\n  /* If cookies not supported fallback to global */\n  if (isSupported === false) (isSet) ? set(name, value) : get(name)\n  /* Set values */\n  if (isSet) {\n    return document.cookie = name + '=' + encodeURIComponent(value) +\n      /* Set TTL set expiration on cookie */\n      ((!ttl) ? '' : '; expires=' + new Date(+new Date() + (ttl * 1000)).toUTCString() +\n      // If path set path\n      ((!path) ? '' : '; path=' + path) +\n      // If domain set domain\n      ((!domain) ? '' : '; domain=' + domain) +\n      // If secure set secure\n      ((!secure) ? '' : '; secure'))\n  }\n  /* Get values */\n  return decodeURIComponent((('; ' + document.cookie).split('; ' + name + '=')[1] || '').split(';')[0])\n}\n"], "names": ["COOKIE", "isSupported", "hasCookies", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "name", "remove", "tmp", "document", "indexOf", "e", "value", "ttl", "path", "domain", "secure", "window", "isSet", "arguments", "length", "set", "get", "encodeURIComponent", "Date", "toUTCString", "decodeURIComponent", "split"], "mappings": "6UAEaA,EAAS,SAElBC,EAAcC,IAOLC,EAAYC,EAQZC,EAAYD,WAMTE,EAAaC,GAC3B,OAAON,EAAcG,EAAOG,EAAM,IAAK,GAAKC,SAAOD,YAOrCL,IACd,QAA2B,IAAhBD,EACT,OAAOA,EAET,IAAMQ,EAAMT,EAASA,EACrB,IAEEI,EAAOK,EAAKA,GACZR,GAAgD,IAAlCS,SAASN,OAAOO,QAAQF,GAEtCH,EAAaG,GACb,MAAOG,GACPX,GAAc,EAEhB,OAAOA,EAwBT,SAASG,EAAOG,EAAMM,EAAOC,EAAKC,EAAMC,EAAQC,GAC9C,GAAsB,oBAAXC,OAAX,CACA,IAAMC,EAAQC,UAAUC,OAAS,EAIjC,OAFoB,IAAhBpB,IAAwBkB,EAASG,MAAIf,EAAMM,GAASU,MAAIhB,IAExDY,EACKT,SAASN,OAASG,EAAO,IAAMiB,mBAAmBX,IAEpDC,EAAY,aAAe,IAAIW,MAAM,IAAIA,KAAgB,IAANX,GAAaY,eAEhEX,EAAa,UAAYA,EAAjB,KAERC,EAAe,YAAcA,EAAnB,KAEVC,EAAe,WAAL,IANH,IASPU,qBAAqB,KAAOjB,SAASN,QAAQwB,MAAM,KAAOrB,EAAO,KAAK,IAAM,IAAIqB,MAAM,KAAK"}