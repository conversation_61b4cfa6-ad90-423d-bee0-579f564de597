# Analytics Utility functions

Common analytic utility functions for [analytics](https://www.npmjs.com/package/analytics)

## Install

```bash
npm install analytics-utils --save
```

Or as a script tag:

```html
<script src="https://unpkg.com/analytics-utils/dist/analytics-utils.min.js"></script>
```

## Special thanks

- [A small guide to create extra small libraries](https://medium.com/@kelin2025/writing-js-libraries-less-than-1tb-size-6342da0c006a)
- [How to write and build JS libraries in 2018](https://medium.com/@kelin2025/so-you-wanna-use-es6-modules-714f48b3a953)
