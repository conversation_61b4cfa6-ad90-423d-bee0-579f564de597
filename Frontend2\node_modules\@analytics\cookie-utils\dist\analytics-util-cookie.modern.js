import{remove as o,set as e,get as t}from"@analytics/global-storage-utils";const n="cookie";let i=d();const c=s,r=s;function u(e){return i?s(e,"",-1):o(e)}function d(){if(void 0!==i)return i;const o="cookiecookie";try{s(o,o),i=-1!==document.cookie.indexOf(o),u(o)}catch(o){i=!1}return i}function s(o,n,c,r,u,d){if("undefined"==typeof window)return;const s=arguments.length>1;return!1===i&&(s?e(o,n):t(o)),s?document.cookie=o+"="+encodeURIComponent(n)+(c?"; expires="+new Date(+new Date+1e3*c).toUTCString()+(r?"; path="+r:"")+(u?"; domain="+u:"")+(d?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+o+"=")[1]||"").split(";")[0])}export{n as COOKIE,c as getCookie,d as hasCookies,u as removeCookie,r as setCookie};
//# sourceMappingURL=analytics-util-cookie.modern.js.map
