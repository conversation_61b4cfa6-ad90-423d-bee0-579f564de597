var utilCookies=function(n){var e="object";"undefined"==typeof process||process;var o="undefined"!=typeof document;function t(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,o&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var r=t.bind(null,"toUpperCase"),i=t.bind(null,"toLowerCase");function u(n,e){void 0===e&&(e=!0);var o=function(n){return f(n)?r("null"):"object"==typeof n?function(n){return l(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?i(o):o}function c(n,e){return typeof e===n}var l=c.bind(null,"function"),a=c.bind(null,"string");function f(n){return null===n}function s(n,e){if("object"!=typeof e||f(e))return!1;if(e instanceof n)return!0;var o=u(new n(""));if(function(n){return n instanceof Error||a(n.message)&&n.constructor&&function(n){return"number"===u(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(u(e)===o)return!0;e=Object.getPrototypeOf(e)}return!1}function d(n,e){var o=n instanceof Element||n instanceof HTMLDocument;return o&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):o}function p(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}c.bind(null,"undefined"),c.bind(null,"boolean"),c.bind(null,"symbol"),s.bind(null,TypeError),s.bind(null,SyntaxError),p(d,"form"),p(d,"button"),p(d,"input"),p(d,"select");var v="__global__",b=typeof self===e&&self.self===self&&self||typeof global===e&&global.global===global&&global||void 0;function g(n){return b[v][n]}function m(n,e){return b[v][n]=e}b[v]||(b[v]={});var y="cookie",C=O(),k=h,j=h;function w(n){return C?h(n,"",-1):void delete b[v][n]}function O(){if(void 0!==C)return C;var n=y+y;try{h(n,n),C=-1!==document.cookie.indexOf(n),w(n)}catch(n){C=!1}return C}function h(n,e,o,t,r,i){if("undefined"!=typeof window){var u=arguments.length>1;return!1===C&&(u?m(n,e):g(n)),u?document.cookie=n+"="+encodeURIComponent(e)+(o?"; expires="+new Date(+new Date+1e3*o).toUTCString()+(t?"; path="+t:"")+(r?"; domain="+r:"")+(i?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+n+"=")[1]||"").split(";")[0])}}return n.COOKIE=y,n.getCookie=k,n.hasCookies=O,n.removeCookie=w,n.setCookie=j,n}({});
//# sourceMappingURL=cookie-utils.js.map
