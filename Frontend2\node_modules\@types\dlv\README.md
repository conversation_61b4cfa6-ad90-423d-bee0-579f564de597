# Installation
> `npm install --save @types/dlv`

# Summary
This package contains type definitions for dlv (https://github.com/developit/dlv#readme).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/dlv.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/dlv/index.d.ts)
````ts
/**
 * Safely get a dot-notated path within a nested object, with ability to
 * return a default if the full key path does not exist or the value is
 * undefined
 */
export = dlv;
declare function dlv(object: object | undefined, key: string | Array<string | number>, defaultValue?: any): any;

````

### Additional Details
 * Last updated: Wed, 13 Nov 2024 14:35:07 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/ryansonshine).
