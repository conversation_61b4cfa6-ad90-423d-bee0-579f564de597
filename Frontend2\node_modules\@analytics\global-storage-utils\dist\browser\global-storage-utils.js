var utilGlobalStorage=function(n){var e="undefined",t="object";"undefined"==typeof process||process;var r="undefined"!=typeof document;function o(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,r&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var u=o.bind(null,"toUpperCase"),i=o.bind(null,"toLowerCase");function l(n,e){void 0===e&&(e=!0);var t=function(n){return s(n)?u("null"):"object"==typeof n?function(n){return a(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?i(t):t}function c(n,e){return typeof e===n}var a=c.bind(null,"function"),f=c.bind(null,"string");function s(n){return null===n}function d(n,e){if("object"!=typeof e||s(e))return!1;if(e instanceof n)return!0;var t=l(new n(""));if(function(n){return n instanceof Error||f(n.message)&&n.constructor&&function(n){return"number"===l(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(l(e)===t)return!0;e=Object.getPrototypeOf(e)}return!1}function b(n,e){var t=n instanceof Element||n instanceof HTMLDocument;return t&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):t}function p(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}c.bind(null,"undefined"),c.bind(null,"boolean"),c.bind(null,"symbol"),d.bind(null,TypeError),d.bind(null,SyntaxError),p(b,"form"),p(b,"button"),p(b,"input"),p(b,"select");var v="__global__",g=typeof self===t&&self.self===self&&self||typeof global===t&&global.global===global&&global||void 0;g[v]||(g[v]={});var y={};function m(n){if(typeof y[n]!==e)return y[n];try{var t=window[n];t.setItem(e,e),t.removeItem(e)}catch(e){return y[n]=!1}return y[n]=!0}return n.GLOBAL="global",n.KEY=v,n.get=function(n){return g[v][n]},n.globalContext=g,n.hasSupport=m,n.remove=function(n){delete g[v][n]},n.set=function(n,e){return g[v][n]=e},n.wrap=function(n,e,t){var r;try{if(m(n)){var o=window[n];r=o[e].bind(o)}}catch(n){}return r||t},n}({});
//# sourceMappingURL=global-storage-utils.js.map
