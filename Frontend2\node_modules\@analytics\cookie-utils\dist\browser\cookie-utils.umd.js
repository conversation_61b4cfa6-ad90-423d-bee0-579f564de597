!function(n,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((n||self).utilCookies={})}(this,function(n){var e="object";"undefined"==typeof process||process;var o="undefined"!=typeof document;function t(n,e){return e.charAt(0)[n]()+e.slice(1)}"undefined"!=typeof Deno&&Deno,o&&"nodejs"===window.name||"undefined"!=typeof navigator&&void 0!==navigator.userAgent&&(navigator.userAgent.includes("Node.js")||navigator.userAgent.includes("jsdom"));var r=t.bind(null,"toUpperCase"),i=t.bind(null,"toLowerCase");function u(n,e){void 0===e&&(e=!0);var o=function(n){return a(n)?r("null"):"object"==typeof n?function(n){return l(n.constructor)?n.constructor.name:null}(n):Object.prototype.toString.call(n).slice(8,-1)}(n);return e?i(o):o}function c(n,e){return typeof e===n}var l=c.bind(null,"function"),f=c.bind(null,"string");function a(n){return null===n}function d(n,e){if("object"!=typeof e||a(e))return!1;if(e instanceof n)return!0;var o=u(new n(""));if(function(n){return n instanceof Error||f(n.message)&&n.constructor&&function(n){return"number"===u(n)&&!isNaN(n)}(n.constructor.stackTraceLimit)}(e))for(;e;){if(u(e)===o)return!0;e=Object.getPrototypeOf(e)}return!1}function s(n,e){var o=n instanceof Element||n instanceof HTMLDocument;return o&&e?function(n,e){return void 0===e&&(e=""),n&&n.nodeName===e.toUpperCase()}(n,e):o}function p(n){var e=[].slice.call(arguments,1);return function(){return n.apply(void 0,[].slice.call(arguments).concat(e))}}c.bind(null,"undefined"),c.bind(null,"boolean"),c.bind(null,"symbol"),d.bind(null,TypeError),d.bind(null,SyntaxError),p(s,"form"),p(s,"button"),p(s,"input"),p(s,"select");var b="__global__",v=typeof self===e&&self.self===self&&self||typeof global===e&&global.global===global&&global||void 0;function g(n){return v[b][n]}function m(n,e){return v[b][n]=e}v[b]||(v[b]={});var y="cookie",C=w(),k=x,j=x;function h(n){return C?x(n,"",-1):void delete v[b][n]}function w(){if(void 0!==C)return C;var n=y+y;try{x(n,n),C=-1!==document.cookie.indexOf(n),h(n)}catch(n){C=!1}return C}function x(n,e,o,t,r,i){if("undefined"!=typeof window){var u=arguments.length>1;return!1===C&&(u?m(n,e):g(n)),u?document.cookie=n+"="+encodeURIComponent(e)+(o?"; expires="+new Date(+new Date+1e3*o).toUTCString()+(t?"; path="+t:"")+(r?"; domain="+r:"")+(i?"; secure":""):""):decodeURIComponent((("; "+document.cookie).split("; "+n+"=")[1]||"").split(";")[0])}}n.COOKIE=y,n.getCookie=k,n.hasCookies=w,n.removeCookie=h,n.setCookie=j});
//# sourceMappingURL=cookie-utils.umd.js.map
