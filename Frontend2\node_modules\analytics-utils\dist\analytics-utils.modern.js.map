{"version": 3, "file": "analytics-utils.modern.js", "sources": ["../src/decodeUri.js", "../src/getBrowserLocale.js", "../src/getTimeZone.js", "../src/isExternalReferrer.js", "../src/isScriptLoaded.js", "../src/paramsClean.js", "../src/paramsGet.js", "../src/paramsParse.js", "../src/paramsRemove.js", "../src/url.js", "../src/parseReferrer.js", "../src/uuid.js", "../src/throttle.js"], "sourcesContent": ["/**\n * Decode URI string\n *\n * @param {String} s string to decode\n * @returns {String} decoded string\n * @example\n * decode(\"Bought%20keyword)\n * => \"Bought keyword\"\n */\nexport function decodeUri(s) {\n  try {\n    return decodeURIComponent(s.replace(/\\+/g, ' '))\n  } catch (e) {\n    return null\n  }\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * @returns {string | undefined}\n */\nexport function getBrowserLocale() {\n  if (!isBrowser) return\n  const { language, languages, userLanguage } = navigator\n  if (userLanguage) return userLanguage // IE only\n  return (languages && languages.length) ? languages[0] : language\n}\n", "\n/**\n * @returns {string | undefined}\n */\nexport function getTimeZone() {\n  try {\n    return Intl.DateTimeFormat().resolvedOptions().timeZone\n  } catch (error) {}\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * @param {string | null | undefined} ref\n * @returns {boolean | undefined}\n */\nexport function isExternalReferrer(ref) {\n  if (!isBrowser) return false\n  const referrer = ref || document.referrer\n  if (referrer) {\n    const port = window.document.location.port\n    let ref = referrer.split('/')[2]\n    if (port) {\n      ref = ref.replace(`:${port}`, '')\n    }\n    return ref !== window.location.hostname\n  }\n  return false\n}\n", "import { isBrowser, isString, isRegex } from '@analytics/type-utils'\n\n/**\n * Check if a script is loaded\n * @param  {String|RegExp} script - Script src as string or regex\n * @return {Boolean} is script loaded\n */\nexport function isScriptLoaded(script) {\n  if (!isBrowser) return true\n  const scripts = document.getElementsByTagName('script')\n  return !!Object.keys(scripts).filter((key) => {\n    const { src } = scripts[key]\n    if (isString(script)) {\n      return src.indexOf(script) !== -1\n    } else if (isRegex(script)) {\n      return src.match(script)\n    }\n    return false\n  }).length\n}\n", "\n/**\n * Strip a query parameter from a url string\n * @param  {string} url   - url with query parameters\n * @param  {string} param - parameter to strip\n * @return {string} cleaned url\n */\nexport function paramsClean(url, param) {\n  const search = (url.split('?') || [ , ])[1] // eslint-disable-line\n  if (!search || search.indexOf(param) === -1) {\n    return url\n  }\n  // remove all utm params from URL search\n  const regex = new RegExp(`(\\\\&|\\\\?)${param}([_A-Za-z0-9\"+=.\\\\/\\\\-@%]+)`, 'g')\n  const cleanSearch = `?${search}`.replace(regex, '').replace(/^&/, '?')\n  // replace search params with clean params\n  const cleanURL = url.replace(`?${search}`, cleanSearch)\n  // use browser history API to clean the params\n  return cleanURL\n}\n", "import {decodeUri} from './decodeUri'\n\n/**\n * Get a given query parameter value\n * @param  {string} param - Key of parameter to find\n * @param  {string} url - url to search\n * @return {string} match\n */\nexport function paramsGet(param, url) {\n  return decodeUri((RegExp(`${param}=(.+?)(&|$)`).exec(url) || [, ''])[1])\n}\n", "import { isBrowser } from '@analytics/type-utils'\nimport { decodeUri } from './decodeUri'\n\n/**\n * Get search string from given url\n * @param  {string} [url] - optional url string. If no url, window.location.search will be used\n * @return {string} url search string\n */\nfunction getSearchString(url) {\n  if (url) {\n    const p = url.match(/\\?(.*)/)\n    return (p && p[1]) ? p[1].split('#')[0] : ''\n  }\n  return isBrowser && window.location.search.substring(1)\n}\n\n/**\n * Parse url parameters into javascript object\n * @param  {string} [url] - URI to parse. If no url supplied window.location will be used\n * @return {object} parsed url parameters\n */\nexport function paramsParse(url) {\n  return getParamsAsObject(getSearchString(url))\n}\n\n/*\n?first=abc&a[]=123&a[]=false&b[]=str&c[]=3.5&a[]=last\nhttps://random.url.com?Target=Report&Method=getStats&fields%5B%5D=Offer.name&fields%5B%5D=Advertiser.company&fields%5B%5D=Stat.clicks&fields%5B%5D=Stat.conversions&fields%5B%5D=Stat.cpa&fields%5B%5D=Stat.payout&fields%5B%5D=Stat.date&fields%5B%5D=Stat.offer_id&fields%5B%5D=Affiliate.company&groups%5B%5D=Stat.offer_id&groups%5B%5D=Stat.date&filters%5BStat.affiliate_id%5D%5Bconditional%5D=EQUAL_TO&filters%5BStat.affiliate_id%5D%5Bvalues%5D=1831&limit=9999\nhttps://random.url.com?Target=Offer&Method=findAll&filters%5Bhas_goals_enabled%5D%5BTRUE%5D=1&filters%5Bstatus%5D=active&fields%5B%5D=id&fields%5B%5D=name&fields%5B%5D=default_goal_name\nhttp://localhost:3000/?Target=Offer&Method=findAll&filters[has_goals_enabled][TRUE]=1&filters[status]=active&filters[wow]arr[]=yaz&filters[wow]arr[]=naz&fields[]=id&fields[]=name&fields[]=default_goal_name */\n\n\nfunction getParamsAsObject(query) {\n  let params = Object.create(null)\n  let temp\n  const re = /([^&=]+)=?([^&]*)/g\n\n  while (temp = re.exec(query)) {\n    var k = decodeUri(temp[1])\n    var v = decodeUri(temp[2])\n    if (!k) continue\n    if (k.substring(k.length - 2) === '[]') {\n      k = k.substring(0, k.length - 2);\n      var arrVal = params[k] || (params[k] = [])\n      params[k] = Array.isArray(arrVal) ? arrVal : []\n      params[k].push(v)\n    } else {\n      params[k] = (v === '') ? true : v\n    }\n  }\n\n  for (var prop in params) {\n    var arr = prop.split('[')\n    if (arr.length > 1) {\n      assign(params, arr.map((x) => x.replace(/[?[\\]\\\\ ]/g, '')), params[prop])\n      delete params[prop]\n    }\n  }\n  return params\n}\n\nfunction assign(obj, keyPath, value) {\n  var lastKeyIndex = keyPath.length - 1\n  for (var i = 0; i < lastKeyIndex; ++i) {\n    var key = keyPath[i]\n    if (key === '__proto__' || key === 'constructor') {\n      break;\n    }\n    if (!(key in obj)) { \n      obj[key] = {} \n    }\n    obj = obj[key]\n  }\n  obj[keyPath[lastKeyIndex]] = value\n}\n\n\n/*\nhttps://github.com/choojs/nanoquery/blob/791cbdfe49cc380f0b2f93477572128946171b46/browser.js\nvar reg = /([^?=&]+)(=([^&]*))?/g\n\nfunction qs (url) {\n  var obj = {}\n  url.replace(/^.*\\?/, '').replace(reg, function (a0, a1, a2, a3) {\n    var value = decodeURIComponent(a3)\n    var key = decodeURIComponent(a1)\n    if (obj.hasOwnProperty(key)) {\n      if (Array.isArray(obj[key])) obj[key].push(value)\n      else obj[key] = [obj[key], value]\n    } else {\n      obj[key] = value\n    }\n  })\n  return obj\n}\n*/", "import { isBrowser } from '@analytics/type-utils'\nimport { paramsClean } from './paramsClean'\n\n/**\n * Removes params from url in browser\n * @param  {string}   param       - param key to remove from current URL\n * @param  {() => void} [callback]  - callback function to run. Only runs in browser\n * @return {PromiseLike<void>}\n */\nexport function paramsRemove(param, callback) {\n  if (!isBrowser) return Promise.resolve()\n\n  return new Promise((resolve, reject) => {\n    if (window.history && window.history.replaceState) {\n      const url = window.location.href\n      const cleanUrl = paramsClean(url, param)\n      if (url !== cleanUrl) {\n        /* replace URL with history API */\n        // eslint-disable-next-line no-restricted-globals\n        history.replaceState({}, '', cleanUrl)\n      }\n    }\n\n    if (callback) callback()\n\n    return resolve()\n  })\n}\n", "import { isBrowser } from '@analytics/type-utils'\n\n/**\n * Get host domain of url\n * @param  {String} url - href of page\n * @return {String} hostname of page\n *\n * @example\n *  getDomainHost('https://subdomain.my-site.com/')\n *  > subdomain.my-site.com\n */\nexport function getDomainHost(url) {\n  if (!isBrowser) return null\n  const a = document.createElement('a')\n  a.setAttribute('href', url)\n  return a.hostname\n}\n\n/**\n * Get host domain of url\n * @param  {String} url - href of page\n * @return {String} base hostname of page\n *\n * @example\n *  getDomainBase('https://subdomain.my-site.com/')\n *  > my-site.com\n */\nexport function getDomainBase(url) {\n  const host = getDomainHost(url) || ''\n  return host.split('.').slice(-2).join('.')\n}\n\n/**\n * Remove TLD from domain string\n * @param  {String} baseDomain - host name of site\n * @return {String}\n * @example\n *  trimTld('google.com')\n *  > google\n */\nexport function trimTld(baseDomain) {\n  const arr = baseDomain.split('.')\n  return (arr.length > 1) ? arr.slice(0, -1).join('.') : baseDomain\n}\n\nexport default {\n  trimTld,\n  getDomainBase,\n  getDomainHost\n}\n", "import { isBrowser } from '@analytics/type-utils'\nimport { paramsParse } from './paramsParse'\nimport { isExternalReferrer } from './isExternalReferrer'\nimport { trimTld, getDomainBase } from './url'\n\nconst googleKey = 'google'\n\n/**\n * @typedef {{\n *  campaign: string,\n *  referrer?: string,\n * } & DomainObject & Object.<string, any>} ReferrerObject\n */\n\n/**\n * Checks a given url and parses referrer data\n * @param  {String} [referrer] - (optional) referring URL\n * @param  {String} [currentUrl] - (optional) the current url\n * @return {ReferrerObject}     [description]\n */\nexport function parseReferrer(referrer, currentUrl) {\n  if (!isBrowser) return false\n  // default referral data\n  let refData = {\n    'source': '(direct)',\n    'medium': '(none)',\n    'campaign': '(not set)'\n  }\n  // Add raw ref url if external\n  if (referrer && isExternalReferrer(referrer)) {\n    refData.referrer = referrer\n  }\n\n  const domainInfo = parseDomain(referrer)\n  // Read referrer URI and infer source\n  if (domainInfo && Object.keys(domainInfo).length) {\n    refData = Object.assign({}, refData, domainInfo)\n  }\n\n  // Read URI params and use set utm params\n  const params = paramsParse(currentUrl)\n  const paramKeys = Object.keys(params)\n  if (!paramKeys.length) {\n    return refData\n  }\n\n  // set campaign params off GA matches\n  const gaParams = paramKeys.reduce((acc, key) => {\n    // match utm params & dclid (display) & gclid (cpc)\n    if (key.match(/^utm_/)) {\n      acc[`${key.replace(/^utm_/, '')}`] = params[key]\n    }\n    // https://developers.google.com/analytics/devguides/collection/protocol/v1/parameters\n    // dclid - cpc Cost-Per-Thousand Impressions\n    // gclid - cpc Cost per Click\n    if (key.match(/^(d|g)clid/)) {\n      acc['source'] = googleKey\n      acc['medium'] = (params.gclid) ? 'cpc' : 'cpm'\n      acc[key] = params[key]\n    }\n    return acc\n  }, {})\n\n  return Object.assign({}, refData, gaParams)\n}\n\n/**\n * @typedef {{\n *  source: string,\n *  medium: string,\n *  term?: string\n * }} DomainObject\n */\n\n/**\n * Client side domain parser for determining marketing data.\n * @param  {String} referrer - ref url\n * @return {DomainObject | boolean}\n */\nfunction parseDomain(referrer) {\n  if (!referrer || !isBrowser) return false\n\n  let referringDomain = getDomainBase(referrer)\n  const a = document.createElement('a')\n  a.href = referrer\n\n  // Shim for the billion google search engines\n  if (a.hostname.indexOf(googleKey) > -1) {\n    referringDomain = googleKey\n  }\n\n  // If is search engine\n  if (searchEngines[referringDomain]) {\n    const searchEngine = searchEngines[referringDomain]\n    const queryParam = (typeof searchEngine === 'string') ? searchEngine : searchEngine.p\n    const termRegex = new RegExp(queryParam + '=.*?([^&#]*|$)', 'gi')\n    const term = a.search.match(termRegex)\n\n    return {\n      source: searchEngine.n || trimTld(referringDomain),\n      medium: 'organic',\n      term: (term ? term[0].split('=')[1] : '') || '(not provided)'\n    }\n  }\n\n  // Default\n  const medium = (!isExternalReferrer(referrer)) ? 'internal' : 'referral'\n  return {\n    source: a.hostname,\n    medium: medium\n  }\n}\n\n/**\n * Search engine query string data\n * @type {Object}\n */\nconst Q = 'q'\nconst QUERY = 'query'\nconst searchEngines = {\n  'daum.net': Q,\n  'eniro.se': 'search_word',\n  'naver.com': QUERY,\n  'yahoo.com': 'p',\n  'msn.com': Q,\n  'aol.com': Q,\n  'ask.com': Q,\n  'baidu.com': 'wd',\n  'yandex.com': 'text',\n  'rambler.ru': 'words',\n  'google': Q,\n  'bing.com': {\n    'p': Q,\n    'n': 'live'\n  },\n}\n", "/* ref: http://bit.ly/2daP79j */\n/**\n * @return {string}\n */\nexport function uuid() {\n  var u = '',\n  m = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx',\n  i = 0,\n  rb = Math.random() * 0xffffffff|0;\n\n  while (i++<36) {\n    var c = m [i-1],\n    r = rb&0xf,\n    v = c=='x' ? r : (r&0x3|0x8);\n\n    u += (c=='-' || c=='4') ? c : v.toString(16);\n    rb = i%8==0 ? Math.random() * 0xffffffff|0 : rb>>4\n  }\n  return u\n}\n", "/**\n * @template {Function} F;\n * @param {F} func;\n * @param {number} wait;\n * @return {F};\n */\nexport function throttle(func, wait) {\n  var context, args, result\n  var timeout = null\n  var previous = 0\n  var later = function () {\n    previous = new Date()\n    timeout = null\n    result = func.apply(context, args)\n  };\n  return function () {\n    var now = new Date()\n    if (!previous) {\n      previous = now\n    }\n    var remaining = wait - (now - previous)\n    context = this\n    args = arguments\n    if (remaining <= 0) {\n      clearTimeout(timeout)\n      timeout = null\n      previous = now\n      result = func.apply(context, args)\n    } else if (!timeout) {\n      timeout = setTimeout(later, remaining)\n    }\n    return result\n  }\n}\n"], "names": ["decodeUri", "s", "decodeURIComponent", "replace", "e", "getBrowserLocale", "<PERSON><PERSON><PERSON><PERSON>", "language", "languages", "userLanguage", "navigator", "length", "getTimeZone", "Intl", "DateTimeFormat", "resolvedOptions", "timeZone", "error", "isExternalReferrer", "ref", "referrer", "document", "port", "window", "location", "split", "hostname", "isScriptLoaded", "script", "scripts", "getElementsByTagName", "Object", "keys", "filter", "key", "src", "isString", "indexOf", "isRegex", "match", "paramsClean", "url", "param", "search", "regex", "RegExp", "cleanSearch", "paramsGet", "exec", "paramsParse", "query", "temp", "params", "create", "re", "k", "v", "substring", "arrVal", "Array", "isArray", "push", "prop", "arr", "assign", "map", "x", "getParamsAsObject", "p", "getSearchString", "obj", "keyP<PERSON>", "value", "lastKeyIndex", "i", "params<PERSON><PERSON>ove", "callback", "Promise", "resolve", "reject", "history", "replaceState", "href", "cleanUrl", "getDomainHost", "a", "createElement", "setAttribute", "getDomainBase", "slice", "join", "trimTld", "baseDomain", "<PERSON>se<PERSON><PERSON><PERSON><PERSON>", "currentUrl", "refData", "source", "medium", "campaign", "domainInfo", "referringDomain", "searchEngines", "searchEngine", "termRegex", "term", "n", "parseDomain", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gaParams", "reduce", "acc", "gclid", "Q", "google", "uuid", "u", "rb", "Math", "random", "c", "r", "toString", "throttle", "func", "wait", "context", "args", "result", "timeout", "previous", "later", "Date", "apply", "now", "remaining", "this", "arguments", "clearTimeout", "setTimeout"], "mappings": "0HASgBA,EAAUC,GACxB,IACE,OAAOC,mBAAmBD,EAAEE,QAAQ,MAAO,MAC3C,MAAOC,GACP,sBCRYC,IACd,IAAKC,EAAW,OAChB,MAAMC,SAAEA,EAAFC,UAAYA,EAAZC,aAAuBA,GAAiBC,UAC9C,OAAID,IACID,GAAaA,EAAUG,OAAUH,EAAU,GAAKD,YCL1CK,IACd,IACE,OAAOC,KAAKC,iBAAiBC,kBAAkBC,SAC/C,MAAOC,cCDKC,EAAmBC,GACjC,IAAKb,EAAW,SAChB,MAAMc,EAAWD,GAAOE,SAASD,SACjC,GAAIA,EAAU,CACZ,MAAME,EAAOC,OAAOF,SAASG,SAASF,KACtC,IAAIH,EAAMC,EAASK,MAAM,KAAK,GAI9B,OAHIH,IACFH,EAAMA,EAAIhB,QAAS,IAAGmB,IAAQ,KAEzBH,IAAQI,OAAOC,SAASE,SAEjC,kBCVcC,EAAeC,GAC7B,IAAKtB,EAAW,SAChB,MAAMuB,EAAUR,SAASS,qBAAqB,UAC9C,QAASC,OAAOC,KAAKH,GAASI,OAAQC,IACpC,MAAMC,IAAEA,GAAQN,EAAQK,GACxB,OAAIE,EAASR,IACqB,IAAzBO,EAAIE,QAAQT,KACVU,EAAQV,IACVO,EAAII,MAAMX,KAGlBjB,gBCXW6B,EAAYC,EAAKC,GAC/B,MAAMC,GAAUF,EAAIhB,MAAM,MAAQ,KAAO,GACzC,IAAKkB,IAAqC,IAA3BA,EAAON,QAAQK,GAC5B,OAAOD,EAGT,MAAMG,EAAQ,IAAIC,OAAQ,YAAWH,+BAAoC,KACnEI,EAAe,IAAGH,IAASxC,QAAQyC,EAAO,IAAIzC,QAAQ,KAAM,KAIlE,OAFiBsC,EAAItC,QAAS,IAAGwC,IAAUG,YCR7BC,EAAUL,EAAOD,GAC/B,OAAOzC,GAAW6C,OAAQ,GAAEH,gBAAoBM,KAAKP,IAAQ,EAAG,KAAK,aCYvDQ,EAAYR,GAC1B,OAUF,SAA2BS,GACzB,IACIC,EADAC,EAASrB,OAAOsB,OAAO,MAE3B,MAAMC,EAAK,qBAEX,KAAOH,EAAOG,EAAGN,KAAKE,IAAQ,CAC5B,IAAIK,EAAIvD,EAAUmD,EAAK,IACnBK,EAAIxD,EAAUmD,EAAK,IACvB,GAAKI,EACL,GAAkC,OAA9BA,EAAEE,UAAUF,EAAE5C,OAAS,GAAa,CAEtC,IAAI+C,EAASN,EADbG,EAAIA,EAAEE,UAAU,EAAGF,EAAE5C,OAAS,MACHyC,EAAOG,GAAK,IACvCH,EAAOG,GAAKI,MAAMC,QAAQF,GAAUA,EAAS,GAC7CN,EAAOG,GAAGM,KAAKL,QAEfJ,EAAOG,GAAY,KAANC,GAAmBA,EAIpC,IAAK,IAAIM,KAAQV,EAAQ,CACvB,IAAIW,EAAMD,EAAKrC,MAAM,KACjBsC,EAAIpD,OAAS,IACfqD,EAAOZ,EAAQW,EAAIE,IAAKC,GAAMA,EAAE/D,QAAQ,aAAc,KAAMiD,EAAOU,WAC5DV,EAAOU,IAGlB,OAAOV,EApCAe,CAdT,SAAyB1B,GACvB,GAAIA,EAAK,CACP,MAAM2B,EAAI3B,EAAIF,MAAM,UACpB,OAAQ6B,GAAKA,EAAE,GAAMA,EAAE,GAAG3C,MAAM,KAAK,GAAK,GAE5C,OAAOnB,GAAaiB,OAAOC,SAASmB,OAAOc,UAAU,GAS5BY,CAAgB5B,IAuC3C,SAASuB,EAAOM,EAAKC,EAASC,GAE5B,IADA,IAAIC,EAAeF,EAAQ5D,OAAS,EAC3B+D,EAAI,EAAGA,EAAID,IAAgBC,EAAG,CACrC,IAAIxC,EAAMqC,EAAQG,GAClB,GAAY,cAARxC,GAA+B,gBAARA,EACzB,MAEIA,KAAOoC,IACXA,EAAIpC,GAAO,IAEboC,EAAMA,EAAIpC,GAEZoC,EAAIC,EAAQE,IAAiBD,WChEfG,EAAajC,EAAOkC,GAClC,OAAKtE,MAEMuE,QAAQ,CAACC,EAASC,KAC3B,GAAIxD,OAAOyD,SAAWzD,OAAOyD,QAAQC,aAAc,CACjD,MAAMxC,EAAMlB,OAAOC,SAAS0D,KACtBC,EAAW3C,EAAYC,EAAKC,GAC9BD,IAAQ0C,GAGVH,QAAQC,aAAa,GAAI,GAAIE,GAMjC,OAFIP,GAAUA,IAEPE,MAfcD,QAAQC,mBCCjBM,EAAc3C,GAC5B,IAAKnC,EAAW,YAChB,MAAM+E,EAAIhE,SAASiE,cAAc,KAEjC,OADAD,EAAEE,aAAa,OAAQ9C,GAChB4C,EAAE3D,kBAYK8D,EAAc/C,GAE5B,OADa2C,EAAc3C,IAAQ,IACvBhB,MAAM,KAAKgE,OAAO,GAAGC,KAAK,cAWxBC,EAAQC,GACtB,MAAM7B,EAAM6B,EAAWnE,MAAM,KAC7B,OAAQsC,EAAIpD,OAAS,EAAKoD,EAAI0B,MAAM,GAAI,GAAGC,KAAK,KAAOE,EAGzD,MAAe,CACbD,QAAAA,EACAH,cAAAA,EACAJ,cAAAA,YC5BcS,EAAczE,EAAU0E,GACtC,IAAKxF,EAAW,SAEhB,IAAIyF,EAAU,CACZC,OAAU,WACVC,OAAU,SACVC,SAAY,aAGV9E,GAAYF,EAAmBE,KACjC2E,EAAQ3E,SAAWA,GAGrB,MAAM+E,EA8CR,SAAqB/E,GACnB,IAAKA,IAAad,EAAW,SAE7B,IAAI8F,EAAkBZ,EAAcpE,GACpC,MAAMiE,EAAIhE,SAASiE,cAAc,KASjC,GARAD,EAAEH,KAAO9D,EAGLiE,EAAE3D,SAASW,QAlFC,WAkFqB,IACnC+D,EAnFc,UAuFZC,EAAcD,GAAkB,CAClC,MAAME,EAAeD,EAAcD,GAE7BG,EAAY,IAAI1D,QADsB,iBAAjByD,EAA6BA,EAAeA,EAAalC,GAC1C,iBAAkB,MACtDoC,EAAOnB,EAAE1C,OAAOJ,MAAMgE,GAE5B,MAAO,CACLP,OAAQM,EAAaG,GAAKd,EAAQS,GAClCH,OAAQ,UACRO,MAAOA,EAAOA,EAAK,GAAG/E,MAAM,KAAK,GAAK,KAAO,kBAKjD,MAAMwE,EAAW/E,EAAmBE,GAA0B,WAAb,WACjD,MAAO,CACL4E,OAAQX,EAAE3D,SACVuE,OAAQA,GA5ESS,CAAYtF,GAE3B+E,GAAcpE,OAAOC,KAAKmE,GAAYxF,SACxCoF,EAAUhE,OAAOiC,OAAO,GAAI+B,EAASI,IAIvC,MAAM/C,EAASH,EAAY6C,GACrBa,EAAY5E,OAAOC,KAAKoB,GAC9B,IAAKuD,EAAUhG,OACb,OAAOoF,EAIT,MAAMa,EAAWD,EAAUE,OAAO,CAACC,EAAK5E,KAElCA,EAAIK,MAAM,WACZuE,EAAK,GAAE5E,EAAI/B,QAAQ,QAAS,OAASiD,EAAOlB,IAK1CA,EAAIK,MAAM,gBACZuE,EAAG,OAnDS,SAoDZA,EAAG,OAAc1D,EAAO2D,MAAS,MAAQ,MACzCD,EAAI5E,GAAOkB,EAAOlB,IAEb4E,GACN,IAEH,OAAO/E,OAAOiC,OAAO,GAAI+B,EAASa,GAsDpC,MAAMI,EAAI,IAEJX,EAAgB,CACpB,WAAYW,EACZ,WAAY,cACZ,YAJY,QAKZ,YAAa,IACb,UAAWA,EACX,UAAWA,EACX,UAAWA,EACX,YAAa,KACb,aAAc,OACd,aAAc,QACdC,OAAUD,EACV,WAAY,CACV5C,EAAK4C,EACLP,EAAK,kBCjIOS,IAMd,IALA,IAAIC,EAAI,GAERzC,EAAI,EACJ0C,EAAqB,WAAhBC,KAAKC,SAAsB,EAEzB5C,IAAI,IAAI,CACb,IAAI6C,EALF,uCAKS7C,EAAE,GACb8C,EAAO,GAAHJ,EAGJD,GAAS,KAAHI,GAAa,KAAHA,EAAUA,GAFnB,KAAHA,EAASC,EAAO,EAAFA,EAAM,GAEQC,SAAS,IACzCL,EAAK1C,EAAE,GAAG,EAAoB,WAAhB2C,KAAKC,SAAsB,EAAIF,GAAI,EAEnD,OAAOD,WCZOO,EAASC,EAAMC,GAC7B,IAAIC,EAASC,EAAMC,EACfC,EAAU,KACVC,EAAW,EACXC,EAAQ,WACVD,EAAW,IAAIE,KACfH,EAAU,KACVD,EAASJ,EAAKS,MAAMP,EAASC,IAE/B,kBACE,IAAIO,EAAM,IAAIF,KACTF,IACHA,EAAWI,GAEb,IAAIC,EAAYV,GAAQS,EAAMJ,GAW9B,OAVAJ,EAAUU,KACVT,EAAOU,UACHF,GAAa,GACfG,aAAaT,GACbA,EAAU,KACVC,EAAWI,EACXN,EAASJ,EAAKS,MAAMP,EAASC,IACnBE,IACVA,EAAUU,WAAWR,EAAOI,IAEvBP"}