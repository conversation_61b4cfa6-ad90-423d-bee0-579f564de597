var e=require("@analytics/type-utils"),t=e.PREFIX+"global"+e.PREFIX,r=typeof self===e.OBJECT&&self.self===self&&self||typeof global===e.OBJECT&&global.global===global&&global||void 0;r[t]||(r[t]={});var o={};function l(t){if(typeof o[t]!==e.UNDEFINED)return o[t];try{var r=window[t];r.setItem(e.UNDEFINED,e.UNDEFINED),r.removeItem(e.UNDEFINED)}catch(e){return o[t]=!1}return o[t]=!0}exports.GLOBAL="global",exports.KEY=t,exports.get=function(e){return r[t][e]},exports.globalContext=r,exports.hasSupport=l,exports.remove=function(e){delete r[t][e]},exports.set=function(e,o){return r[t][e]=o},exports.wrap=function(e,t,r){var o;try{if(l(e)){var n=window[e];o=n[t].bind(n)}}catch(e){}return o||r};
//# sourceMappingURL=analytics-util-global-storage.js.map
