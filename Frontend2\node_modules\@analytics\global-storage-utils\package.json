{"name": "@analytics/global-storage-utils", "version": "0.1.7", "description": "Tiny global storage utility library", "author": "<PERSON>", "license": "MIT", "homepage": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-global-storage#readme", "repository": "https://github.com/DavidWells/analytics/tree/master/packages/analytics-util-global-storage", "keywords": ["analytics", "analytics-project", "analytics-util", "global-storage", "window-storage", "storage"], "netlifySiteId": "2401aec5-ceab-49bb-9343-f11108fdeef6", "amdName": "utilGlobalStorage", "source": "src/index.js", "main": "dist/analytics-util-global-storage.js", "module": "dist/analytics-util-global-storage.module.js", "unpkg": "dist/analytics-util-global-storage.umd.js", "sideEffects": false, "scripts": {"start": "npm run sync && concurrently 'npm:watch' 'npm:copy' 'npm:serve'", "serve": "servor dist index.html 8081 --reload --browse", "copy": "watchlist example -- npm run sync", "sync": "cp example/index.html dist", "watch": "npm run build:browser -- --watch --no-compress", "build": "concurrently 'npm:build:*' && npm run sync", "build:package": "microbundle", "build:browser": "microbundle build --external none -f iife,umd -o dist/browser", "build:no-deps": "microbundle build -f iife,umd -o dist/browser-no-deps", "release:patch": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish", "deploy": "npm run build && netlify deploy --prod --dir dist --site $npm_package_netlifySiteId"}, "files": ["lib", "dist", "package.json", "package-lock.json", "README.md"], "devDependencies": {"concurrently": "^6.1.0", "microbundle": "^0.13.0", "servor": "^4.0.2", "watchlist": "^0.2.3"}, "dependencies": {"@analytics/type-utils": "^0.6.2"}, "gitHead": "904e8bc88a9de0d9e55ecbe1e87ac2ee2a454e32"}