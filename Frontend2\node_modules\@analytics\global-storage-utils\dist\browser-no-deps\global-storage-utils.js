var utilGlobalStorage=function(t,e){var r="global",o=e.PREFIX+r+e.PREFIX,l=typeof self===e.OBJECT&&self.self===self&&self||typeof global===e.OBJECT&&global.global===global&&global||void 0;l[o]||(l[o]={});var n={};function a(t){if(typeof n[t]!==e.UNDEFINED)return n[t];try{var r=window[t];r.setItem(e.UNDEFINED,e.UNDEFINED),r.removeItem(e.UNDEFINED)}catch(e){return n[t]=!1}return n[t]=!0}return t.GLOBAL=r,t.KEY=o,t.get=function(t){return l[o][t]},t.globalContext=l,t.hasSupport=a,t.remove=function(t){delete l[o][t]},t.set=function(t,e){return l[o][t]=e},t.wrap=function(t,e,r){var o;try{if(a(t)){var l=window[t];o=l[e].bind(l)}}catch(t){}return o||r},t}({},typeUtils);
//# sourceMappingURL=global-storage-utils.js.map
